# 使用 Python slim 镜像作为基础环境
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    NODE_VERSION=18.17.0

# 设置工作目录
WORKDIR /app

# 配置Debian国内镜像源
RUN if [ -f /etc/apt/sources.list.d/debian.sources ]; then \
        sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources; \
    else \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main" > /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main" >> /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main" >> /etc/apt/sources.list; \
    fi

# 安装系统依赖和Node.js
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    libffi-dev \
    libpng-dev \
    libtiff5-dev \
    libopenjp2-7-dev \
    fonts-dejavu-core \
    fonts-liberation \
    curl \
    wget \
    xz-utils \
    && rm -rf /var/lib/apt/lists/*

# 手动安装Node.js
RUN curl -fsSL https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-linux-x64.tar.xz -o node.tar.xz \
    && tar -xJf node.tar.xz -C /usr/local --strip-components=1 \
    && rm node.tar.xz \
    && ln -s /usr/local/bin/node /usr/local/bin/nodejs

# 配置npm国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 配置pip国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn

# 升级pip和安装wheel
RUN pip install --upgrade pip setuptools wheel

# 先安装numpy，确保版本正确
RUN pip install --no-cache-dir numpy==1.25.2

# 拷贝前端依赖文件并安装
COPY package.json ./
COPY package-lock.json* ./
RUN npm install

# 拷贝 Python 依赖文件并安装其他依赖
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 拷贝应用源码
COPY . .

# 构建前端资源
RUN npm run build

# 创建必要的目录
RUN mkdir -p logs cache && \
    chmod 755 logs cache

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/ || exit 1

# 启动命令
CMD ["gunicorn", "-c", "gunicorn_config.py", "run:app"]