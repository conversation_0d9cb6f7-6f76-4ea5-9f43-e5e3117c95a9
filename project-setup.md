# Vue.js 3 + TypeScript 项目初始化指南

## 1. 项目创建

```bash
# 使用Vite创建Vue 3 + TypeScript项目
npm create vue@latest hdsc-frontend

# 选择配置选项
✔ Add TypeScript? … Yes
✔ Add JSX Support? … No
✔ Add Vue Router for Single Page Application development? … Yes
✔ Add Pinia for state management? … Yes
✔ Add Vitest for Unit Testing? … Yes
✔ Add an End-to-End Testing Solution? › Playwright
✔ Add ESLint for code quality? … Yes
✔ Add Prettier for code formatting? … Yes

# 进入项目目录
cd hdsc-frontend

# 安装依赖
npm install
```

## 2. 核心依赖安装

```bash
# UI组件库 (选择其一)
npm install ant-design-vue@4.x
# 或者
npm install element-plus@2.x

# 图表库
npm install echarts@5.x

# HTTP客户端
npm install axios@1.x

# 工具库
npm install @vueuse/core@10.x
npm install dayjs@1.x

# 样式预处理器
npm install -D sass@1.x

# 状态持久化
npm install pinia-plugin-persistedstate@3.x

# 开发工具
npm install -D @types/node@20.x
npm install -D unplugin-auto-import@0.x
npm install -D unplugin-vue-components@0.x
```

## 3. Vite配置优化

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    // 自动导入Vue API
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      dts: true,
      eslintrc: {
        enabled: true
      }
    }),
    // 自动导入组件
    Components({
      resolvers: [AntDesignVueResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/views': resolve(__dirname, 'src/views'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/styles': resolve(__dirname, 'src/styles')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue'],
          echarts: ['echarts']
        }
      }
    }
  }
})
```

## 4. TypeScript配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/views/*": ["src/views/*"],
      "@/stores/*": ["src/stores/*"],
      "@/api/*": ["src/api/*"],
      "@/utils/*": ["src/utils/*"],
      "@/styles/*": ["src/styles/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "auto-imports.d.ts",
    "components.d.ts"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## 5. ESLint和Prettier配置

```javascript
// .eslintrc.cjs
module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting',
    './.eslintrc-auto-import.json'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
    'vue/multi-word-component-names': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    'prefer-const': 'error',
    'no-var': 'error'
  }
}
```

```json
// .prettierrc.json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "endOfLine": "lf"
}
```

## 6. 环境变量配置

```bash
# .env.development
VITE_APP_TITLE=太享查询系统
VITE_API_BASE_URL=http://localhost:5000
VITE_APP_ENV=development

# .env.production
VITE_APP_TITLE=太享查询系统
VITE_API_BASE_URL=https://your-production-api.com
VITE_APP_ENV=production
```

## 7. 项目结构创建

```bash
# 创建目录结构
mkdir -p src/{api/{types,services},components/{common,business,charts},views,stores,router,styles/{components,themes},utils,composables,assets/{images,icons}}

# 创建基础文件
touch src/api/client.ts
touch src/api/types/index.ts
touch src/stores/index.ts
touch src/router/index.ts
touch src/styles/main.scss
touch src/utils/index.ts
```

## 8. 开发脚本配置

```json
// package.json scripts
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "test:unit": "vitest",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "format": "prettier --write src/",
    "type-check": "vue-tsc --noEmit"
  }
}
```

## 9. Git配置

```bash
# .gitignore 添加
node_modules/
dist/
.env.local
.env.*.local
auto-imports.d.ts
components.d.ts
```

## 10. 开发工作流

### 日常开发
```bash
# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint
```

### 构建部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 测试
```bash
# 运行单元测试
npm run test:unit

# 运行E2E测试
npm run test:e2e
```

## 11. 开发规范

### 组件命名
- 组件文件使用PascalCase: `DataTable.vue`
- 组件注册使用PascalCase: `<DataTable />`

### 文件组织
- 页面组件放在 `views/` 目录
- 可复用组件放在 `components/` 目录
- 业务逻辑放在 `composables/` 目录
- 工具函数放在 `utils/` 目录

### 代码风格
- 使用Composition API
- 优先使用`<script setup>`语法
- 使用TypeScript类型注解
- 遵循ESLint和Prettier规则

## 12. 性能优化配置

### 代码分割
```typescript
// 路由懒加载
const Dashboard = () => import('@/views/Dashboard.vue')
const DataQuery = () => import('@/views/DataQuery.vue')
```

### 组件懒加载
```vue
<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

const HeavyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'))
</script>
```

### 构建优化
- 启用Gzip压缩
- 配置CDN资源
- 优化图片资源
- 使用Web Workers处理大数据

这个配置为您的Vue 3 + TypeScript项目提供了完整的开发环境和最佳实践。
