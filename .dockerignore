# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.env
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.claude/

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Cache
cache/
.cache/

# Git
.git/
.gitignore

# Documentation
*.md
docs/

# Test files
test_*
*_test.py
tests/

# Deployment scripts
deploy.sh
deploy-docker-image.sh
deploy-docker-image.bat
build-docker.sh
build-docker.bat
build-and-export-optimized.bat
hdsc

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Temporary files
*.tmp
*.temp

# Trunk and other tools
.trunk/
.cursorignore

# Build artifacts
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Deployment packages
hdsc-deployment-package/
docker-images/

# Frontend build (will be built inside container)
frontend-next/

# Monitoring
monitoring/