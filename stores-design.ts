// Pinia状态管理设计 - 替换现有的全局变量和sessionStorage
// frontend/src/stores/index.ts

import { createPinia } from 'pinia';
import { createPersistedState } from 'pinia-plugin-persistedstate';

const pinia = createPinia();

// 配置持久化插件
pinia.use(
  createPersistedState({
    storage: localStorage,
    key: id => `hdsc_${id}`,
  })
);

export default pinia;

// ==================== 用户状态管理 ====================
// frontend/src/stores/user.ts

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { UserInfo } from '@/api/types';

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null);
  const token = ref<string>('');
  const isAuthenticated = ref(false);

  // 计算属性
  const hasPermission = computed(() => (level: 'limited' | 'standard' | 'full') => {
    if (!userInfo.value) return false;
    
    const permissions = {
      limited: ['limited', 'standard', 'full'],
      standard: ['standard', 'full'],
      full: ['full']
    };
    
    return permissions[level].includes(userInfo.value.permission_level);
  });

  const userName = computed(() => userInfo.value?.username || '');
  const permissionLevel = computed(() => userInfo.value?.permission_level || 'limited');

  // 动作
  const login = async (credentials: { username: string; password: string; captcha: string }) => {
    try {
      // 调用登录API
      const response = await AuthService.login(credentials);
      
      userInfo.value = response.user;
      token.value = response.token;
      isAuthenticated.value = true;
      
      return response;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      userInfo.value = null;
      token.value = '';
      isAuthenticated.value = false;
    }
  };

  const updateUserInfo = (info: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...info };
    }
  };

  return {
    // 状态
    userInfo,
    token,
    isAuthenticated,
    
    // 计算属性
    hasPermission,
    userName,
    permissionLevel,
    
    // 动作
    login,
    logout,
    updateUserInfo
  };
}, {
  persist: {
    paths: ['userInfo', 'token', 'isAuthenticated']
  }
});

// ==================== 数据状态管理 ====================
// frontend/src/stores/data.ts

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { DataService } from '@/api/services/dataService';
import type { 
  FilterDataResponse, 
  CustomerOrderResponse, 
  OverdueOrderResponse,
  CustomerSummaryData 
} from '@/api/types';

export const useDataStore = defineStore('data', () => {
  // 状态
  const filterData = ref<FilterDataResponse | null>(null);
  const customerOrders = ref<CustomerOrderResponse | null>(null);
  const overdueOrders = ref<OverdueOrderResponse | null>(null);
  const customerSummary = ref<CustomerSummaryData | null>(null);
  
  const loading = ref({
    filter: false,
    customer: false,
    overdue: false,
    summary: false
  });

  const errors = ref({
    filter: '',
    customer: '',
    overdue: '',
    summary: ''
  });

  // 缓存时间戳
  const cacheTimestamps = ref({
    filter: 0,
    customer: 0,
    overdue: 0,
    summary: 0
  });

  // 计算属性
  const hasFilterData = computed(() => 
    filterData.value?.results && filterData.value.results.length > 0
  );

  const hasCustomerData = computed(() => 
    customerOrders.value?.results && customerOrders.value.results.length > 0
  );

  const hasOverdueData = computed(() => 
    overdueOrders.value?.results && overdueOrders.value.results.length > 0
  );

  // 动作
  const fetchFilterData = async (params: { date: string }, forceRefresh = false) => {
    const cacheKey = 'filter';
    const cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    
    // 检查缓存
    if (!forceRefresh && 
        filterData.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return filterData.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getFilterData(params);
      filterData.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      errors.value[cacheKey] = error instanceof Error ? error.message : '获取数据失败';
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchCustomerOrders = async (params: { customer_name: string }, forceRefresh = false) => {
    const cacheKey = 'customer';
    const cacheExpiry = 10 * 60 * 1000; // 10分钟缓存
    
    if (!forceRefresh && 
        customerOrders.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return customerOrders.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getCustomerOrders(params);
      customerOrders.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      errors.value[cacheKey] = error instanceof Error ? error.message : '获取客户数据失败';
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchOverdueOrders = async (params: { page?: number; limit?: number }, forceRefresh = false) => {
    const cacheKey = 'overdue';
    const cacheExpiry = 3 * 60 * 1000; // 3分钟缓存（逾期数据更新频繁）
    
    if (!forceRefresh && 
        overdueOrders.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return overdueOrders.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getOverdueOrders(params);
      overdueOrders.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      errors.value[cacheKey] = error instanceof Error ? error.message : '获取逾期订单失败';
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchCustomerSummary = async (params: { customer_name: string }, forceRefresh = false) => {
    const cacheKey = 'summary';
    const cacheExpiry = 15 * 60 * 1000; // 15分钟缓存
    
    if (!forceRefresh && 
        customerSummary.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return customerSummary.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getCustomerSummary(params);
      customerSummary.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      errors.value[cacheKey] = error instanceof Error ? error.message : '获取客户汇总失败';
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  // 清除缓存
  const clearCache = (type?: 'filter' | 'customer' | 'overdue' | 'summary') => {
    if (type) {
      switch (type) {
        case 'filter':
          filterData.value = null;
          break;
        case 'customer':
          customerOrders.value = null;
          break;
        case 'overdue':
          overdueOrders.value = null;
          break;
        case 'summary':
          customerSummary.value = null;
          break;
      }
      cacheTimestamps.value[type] = 0;
    } else {
      // 清除所有缓存
      filterData.value = null;
      customerOrders.value = null;
      overdueOrders.value = null;
      customerSummary.value = null;
      cacheTimestamps.value = {
        filter: 0,
        customer: 0,
        overdue: 0,
        summary: 0
      };
    }
  };

  return {
    // 状态
    filterData,
    customerOrders,
    overdueOrders,
    customerSummary,
    loading,
    errors,
    
    // 计算属性
    hasFilterData,
    hasCustomerData,
    hasOverdueData,
    
    // 动作
    fetchFilterData,
    fetchCustomerOrders,
    fetchOverdueOrders,
    fetchCustomerSummary,
    clearCache
  };
});

// ==================== 应用状态管理 ====================
// frontend/src/stores/app.ts

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<'light' | 'dark' | 'auto'>('auto');
  const sidebarCollapsed = ref(false);
  const isMobile = ref(false);
  const currentRoute = ref('');
  
  // 全局加载状态
  const globalLoading = ref(false);
  const loadingText = ref('加载中...');

  // 计算属性
  const isDarkMode = computed(() => {
    if (theme.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return theme.value === 'dark';
  });

  // 动作
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light';
  };

  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme;
  };

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  };

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed;
  };

  const setMobile = (mobile: boolean) => {
    isMobile.value = mobile;
  };

  const setCurrentRoute = (route: string) => {
    currentRoute.value = route;
  };

  const showGlobalLoading = (text = '加载中...') => {
    globalLoading.value = true;
    loadingText.value = text;
  };

  const hideGlobalLoading = () => {
    globalLoading.value = false;
  };

  return {
    // 状态
    theme,
    sidebarCollapsed,
    isMobile,
    currentRoute,
    globalLoading,
    loadingText,
    
    // 计算属性
    isDarkMode,
    
    // 动作
    toggleTheme,
    setTheme,
    toggleSidebar,
    setSidebarCollapsed,
    setMobile,
    setCurrentRoute,
    showGlobalLoading,
    hideGlobalLoading
  };
}, {
  persist: {
    paths: ['theme', 'sidebarCollapsed']
  }
});
