/**
 * 太享查询系统 - 主JavaScript文件
 * 提供基础的页面功能和工具函数
 */

// 全局配置
window.AppConfig = {
    debug: false,
    apiTimeout: 30000,
    version: '2.0'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('太享查询系统已加载');
    
    // 初始化基础功能
    initBasicFeatures();
    
    // 初始化表格
    initAllDataTables();
    
    // 初始化提示信息自动消失
    initAutoHideAlerts();
});

/**
 * 初始化基础功能
 */
function initBasicFeatures() {
    // 设置当前年份
    const yearElements = document.querySelectorAll('.current-year');
    const currentYear = new Date().getFullYear();
    yearElements.forEach(el => el.textContent = currentYear);
    
    // 初始化工具提示
    initTooltips();
    
    // 初始化确认对话框
    initConfirmDialogs();
}

/**
 * 初始化所有数据表格
 */
function initAllDataTables() {
    const tables = document.querySelectorAll('.data-table, table[id*="Table"]');
    tables.forEach(table => {
        if (!$.fn.DataTable.isDataTable(table)) {
            initDataTable(table);
        }
    });
}

/**
 * 初始化单个数据表格
 */
function initDataTable(table) {
    if (!table || $.fn.DataTable.isDataTable(table)) {
        return null;
    }
    
    try {
        const dataTable = $(table).DataTable({
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
            language: {
                lengthMenu: "显示 _MENU_ 条记录",
                zeroRecords: "没有找到匹配的记录",
                info: "显示第 _START_ 到 _END_ 条记录，共 _TOTAL_ 条",
                infoEmpty: "显示第 0 到 0 条记录，共 0 条",
                infoFiltered: "(从 _MAX_ 条记录中筛选)",
                search: "搜索:",
                paginate: {
                    first: "首页",
                    previous: "上页",
                    next: "下页",
                    last: "末页"
                }
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            order: [[0, 'desc']],
            columnDefs: [
                {
                    targets: 'no-sort',
                    orderable: false
                }
            ]
        });
        
        console.log('数据表格初始化成功:', table.id);
        return dataTable;
        
    } catch (error) {
        console.error('数据表格初始化失败:', error);
        return null;
    }
}

/**
 * 初始化工具提示
 */
function initTooltips() {
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(
            document.querySelectorAll('[data-bs-toggle="tooltip"]')
        );
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * 初始化确认对话框
 */
function initConfirmDialogs() {
    const confirmElements = document.querySelectorAll('[data-confirm]');
    confirmElements.forEach(element => {
        element.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 初始化自动消失提示
 */
function initAutoHideAlerts() {
    const alerts = document.querySelectorAll('.alert[data-auto-hide]');
    alerts.forEach(alert => {
        const delay = parseInt(alert.getAttribute('data-auto-hide')) || 3000;
        setTimeout(() => {
            if (alert.parentNode) {
                alert.classList.add('fade');
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        }, delay);
    });
}

/**
 * 显示加载状态
 */
function showLoading(message = '加载中...') {
    const loadingHtml = `
        <div id="globalLoading" class="d-flex justify-content-center align-items-center position-fixed" 
             style="top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
            <div class="text-center text-white">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>${message}</div>
            </div>
        </div>
    `;
    
    // 移除已存在的加载提示
    const existing = document.getElementById('globalLoading');
    if (existing) {
        existing.remove();
    }
    
    document.body.insertAdjacentHTML('beforeend', loadingHtml);
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loading = document.getElementById('globalLoading');
    if (loading) {
        loading.remove();
    }
}

/**
 * 显示成功消息
 */
function showSuccess(message, autoHide = true) {
    showAlert(message, 'success', autoHide);
}

/**
 * 显示错误消息
 */
function showError(message, autoHide = true) {
    showAlert(message, 'danger', autoHide);
}

/**
 * 显示信息消息
 */
function showInfo(message, autoHide = true) {
    showAlert(message, 'info', autoHide);
}

/**
 * 显示警告消息
 */
function showWarning(message, autoHide = true) {
    showAlert(message, 'warning', autoHide);
}

/**
 * 显示通用提示消息
 */
function showAlert(message, type = 'info', autoHide = true) {
    const alertId = 'alert-' + Date.now();
    const autoHideClass = autoHide ? 'auto-dismiss-alert' : '';
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible ${autoHideClass}" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 自动消失
    if (autoHide) {
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

/**
 * 格式化日期
 */
function formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 数字格式化
 */
function formatNumber(num, decimals = 2) {
    if (isNaN(num)) return '0';
    return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

/**
 * 复制文本到剪贴板
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showSuccess('已复制到剪贴板');
        }).catch(() => {
            showError('复制失败');
        });
    } else {
        // 备用方法
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            document.execCommand('copy');
            showSuccess('已复制到剪贴板');
        } catch (err) {
            showError('复制失败');
        }
        
        document.body.removeChild(textarea);
    }
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出常用函数到全局
window.AppUtils = {
    showLoading,
    hideLoading,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    showAlert,
    formatDate,
    formatNumber,
    copyToClipboard,
    debounce,
    throttle,
    initDataTable
}; 