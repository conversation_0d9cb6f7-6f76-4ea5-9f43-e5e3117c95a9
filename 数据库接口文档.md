# 数据库API接口文档（新的ip：*************:5000）

本文档整理了系统中所有数据库相关的API节点，并提供测试链接示例。

## 数据筛选接口

### 1. 日期筛选订单接口

- **接口路径**: `/filter_data_db`
- **请求方法**: GET
- **参数**:
  - `date`: 日期，格式为YYYY-MM-DD
  - `api_key`: API密钥
- **功能描述**: 根据日期筛选订单，并返回结果，包含客户信息补充
- **测试链接**: `/filter_data_db?date=2025-04-21&api_key=lxw8025031`

### 2. 客户姓名筛选订单接口

- **接口路径**: `/filter_orders_by_customer_name_db`
- **请求方法**: GET
- **参数**:
  - `customer_name`: 客户姓名
  - `api_key`: API密钥
- **功能描述**: 根据客户姓名筛选订单，并返回订单信息，包括账单日期和状态
- **测试链接**: `/filter_orders_by_customer_name_db?customer_name=张三&api_key=lxw8025031`

### 3. 逾期订单筛选接口

- **接口路径**: `/filter_overdue_orders_db`
- **请求方法**: GET
- **参数**:
  - `api_key`: API密钥
- **功能描述**: 筛选逾期订单（首次逾期），并返回结果，包含客户信息补充
- **测试链接**: `/filter_overdue_orders_db?api_key=lxw8025031`

## 订单管理接口

### 4. 获取订单详情接口

- **接口路径**: `/get_order_details_db`
- **请求方法**: GET
- **参数**:
  - `order_number`: 订单编号
  - `api_key`: API密钥
- **功能描述**: 根据订单编号获取订单的详细信息
- **测试链接**: `/get_order_details_db?order_number=123456&api_key=lxw8025031`

### 5. 删除订单接口

- **接口路径**: `/delete_order_db`
- **请求方法**: DELETE
- **参数**:
  - `order_number`: 订单编号
  - `api_key`: API密钥
- **功能描述**: 根据订单编号删除订单
- **测试链接**: `/delete_order_db?order_number=123456&api_key=lxw8025031`

## 数据汇总接口

### 6. 数据汇总接口

- **接口路径**: `/summary_data_db`
- **请求方法**: GET
- **参数**:
  - `start_date`: 开始日期，格式为YYYY-MM-DD
  - `end_date`: 结束日期，格式为YYYY-MM-DD
  - `api_key`: API密钥
- **功能描述**: 根据指定时间段统计店铺的数据汇总，并增加累计数据和逾期金额
- **测试链接**: `/summary_data_db?start_date=2025-01-01&end_date=2025-04-21&api_key=lxw8025031`

### 7. 订单汇总接口

- **接口路径**: `/order_summary_db`
- **请求方法**: GET
- **参数**:
  - `end_date`: 结束日期，格式为YYYY-MM-DD
  - `api_key`: API密钥
- **功能描述**: 从数据库中查询，从最早订单日期开始，统计到指定end_date为止，每个月的电商订单数量和租赁订单数量
- **测试链接**: `/order_summary_db?end_date=2025-04-21&api_key=lxw8025031`

### 8. 客户订单汇总接口

- **接口路径**: `/customer_summary_db`
- **请求方法**: GET
- **参数**:
  - `customer_name`: 客户姓名
  - `phone`: 手机号码（与客户姓名二选一）
  - `api_key`: API密钥
- **功能描述**: 提供客户订单汇总数据，包括汇总订单数量、总融资额、当前待收、已还款等信息
- **测试链接**: `/customer_summary_db?customer_name=张三&api_key=lxw8025031`

### 9. 逾期订单汇总接口

- **接口路径**: `/overdue_summary_db`
- **请求方法**: GET
- **参数**:
  - `end_date`: 结束日期，格式为YYYY-MM-DD
  - `api_key`: API密钥
- **功能描述**: 基于数据库的逾期订单汇总API，返回从数据库中最早的逾期订单日期到指定结束日期的逾期订单数据汇总
- **测试链接**: `/overdue_summary_db?end_date=2025-04-21&api_key=lxw8025031`

## ETL接口

### 10. ETL触发接口

- **接口路径**: `/api/etl/trigger`
- **请求方法**: POST
- **功能描述**: 触发ETL过程，从Excel文件导入数据到数据库
- **测试链接**: POST请求 `/api/etl/trigger`

### 11. Excel文件上传接口

- **接口路径**: `/api/etl/upload`
- **请求方法**: POST
- **功能描述**: 处理Excel文件上传，并执行ETL过程
- **测试链接**: POST请求 `/api/etl/upload`

### 12. 更新支付状态接口

- **接口路径**: `/api/etl/update-payment-status`
- **请求方法**: POST
- **功能描述**: 更新订单的支付状态
- **测试链接**: POST请求 `/api/etl/update-payment-status`

## 注意事项

1. 所有API调用都需要提供有效的API密钥(`api_key`)
2. 日期格式必须是YYYY-MM-DD格式
3. 对于POST请求，需要使用适当的HTTP客户端或工具进行测试
4. 部分接口可能需要登录权限 