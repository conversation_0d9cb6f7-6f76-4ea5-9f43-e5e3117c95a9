{"name": "hdsc_query_app_02", "version": "1.0.0", "description": "太享查询系统是一个基于Flask框架的金融数据查询展示系统，用于展示和分析从API获取的金融数据。", "main": "搜索功能代码片段.js", "directories": {"doc": "docs"}, "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "watch": "webpack --mode development --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/lxw8080/hdsc_query_app_02.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/lxw8080/hdsc_query_app_02/issues"}, "homepage": "https://github.com/lxw8080/hdsc_query_app_02#readme", "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "mini-css-extract-plugin": "^2.9.2", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}