{"componentName": "pagination", "style": {"container": {"display": "flex", "flexDirection": "column", "alignItems": "center", "gap": "8px", "fontFamily": "'PingFang SC', 'Microsoft YaHei', sans-serif"}, "infoText": {"template": "显示第 {start} 至 {end} 项结果，共 {total} 项", "fontSize": "12px", "color": "#606266", "lineHeight": "1.6", "marginBottom": "4px"}, "controls": {"display": "flex", "alignItems": "center", "gap": "4px"}, "buttonBase": {"height": "32px", "padding": "0 14px", "fontSize": "13px", "fontWeight": 400, "border": "1px solid #dcdfe6", "borderRadius": "4px", "backgroundColor": "#ffffff", "cursor": "pointer", "color": "#606266", "transition": "all 0.2s ease"}, "buttonHover": {"backgroundColor": "#ecf5ff", "borderColor": "#c6e2ff", "color": "#409eff"}, "buttonDisabled": {"backgroundColor": "#f5f7fa", "borderColor": "#e4e7ed", "color": "#c0c4cc", "cursor": "not-allowed"}, "buttonActive": {"border": "1px solid #bfc3cd", "background": "linear-gradient(#f7f8fa, #f2f3f5)", "color": "#303133", "display": "flex", "alignItems": "center", "justifyContent": "center", "gap": "6px"}, "pagerIcon": {"icon": "view-layer", "size": "14px", "color": "#909399"}}, "elements": {"prevButton": {"label": "上页", "state": "disabled"}, "pager": {"label": "{current} / {pages}", "iconEnabled": true}, "nextButton": {"label": "下页", "state": "enabled"}}, "layout": {"align": "center", "gap": "6px", "marginTop": "4px"}}