{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 客户汇总 - {{ customer_name }}{% endblock %}

{% block styles %}
<style>
/* 数据单元格折叠样式 */
.cell-content {
    position: relative;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    display: block;
    transition: all 0.3s ease;
    color: #333;
}

.cell-content:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 30px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
    pointer-events: none;
}

.cell-content.expanded {
    white-space: normal;
    word-break: break-word;
    max-width: none;
    overflow: visible;
}

.cell-content.expanded:after {
    display: none;
}

/* 鼠标悬停状态 */
.cell-content:hover {
    color: #007bff;
}

/* 移动端样式优化 */
@media (max-width: 768px) {
    .cell-content {
        max-width: 120px;
    }
    
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        top: 50%;
        transform: translateY(-50%);
        background-color: #007bff;
        box-shadow: 0 0 0.2rem rgba(0, 123, 255, 0.5);
    }
    
    .dtr-data {
        word-break: break-word;
        max-width: calc(100vw - 100px);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 引入侧边栏模板 -->
        {% include 'sidebar.html' %}

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <h1 class="h2">客户订单汇总: {{ customer_name }}</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.history.back()">
                            <i class="bi bi-arrow-left"></i> 返回
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportCharts()">
                            <i class="bi bi-download"></i> 导出图表
                        </button>
                    </div>
                </div>
            </div>

            {% if summary_data %}
            <!-- 客户基本信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">客户基本信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <th scope="row" width="150">客户名称:</th>
                                        <td>{{ customer_name }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">联系电话:</th>
                                        <td>{{ summary_data.basic_info.phones|join(', ') }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">设备型号:</th>
                                        <td>{{ summary_data.basic_info.device_models|join(', ') }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">总台数:</th>
                                        <td>{{ summary_data.basic_info.total_devices_count }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <th scope="row" width="150">客服归属:</th>
                                        <td>{{ summary_data.basic_info.services|join(', ') }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">业务归属:</th>
                                        <td>{{ summary_data.basic_info.business_affiliations|join(', ') }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">客户信息备注:</th>
                                        <td>{{ summary_data.basic_info.customer_remarks }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单汇总信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">订单汇总信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-center bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">总订单数</h6>
                                    <p class="card-text h2">{{ summary_data.order_summary.total_count }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">总融资金额</h6>
                                    <p class="card-text h2">{{ summary_data.order_summary.total_amount }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">已还款金额</h6>
                                    <p class="card-text h2">{{ summary_data.order_summary.repaid_amount }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">当前待收</h6>
                                    <p class="card-text h2">{{ summary_data.order_summary.current_receivable }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 业务类型分布 -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="orderChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="paymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab导航 -->
            <ul class="nav nav-tabs" id="customerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="receivable-tab" data-bs-toggle="tab" data-bs-target="#receivable" type="button" role="tab">待收明细</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">订单详情</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="finance-tab" data-bs-toggle="tab" data-bs-target="#finance" type="button" role="tab">财务流水</button>
                </li>
            </ul>

            <!-- Tab内容 -->
            <div class="tab-content" id="customerTabsContent">
                <!-- 待收明细 -->
                <div class="tab-pane fade show active" id="receivable" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>待收期数</th>
                                            <th>订单数量</th>
                                            <th>台数</th>
                                            <th>待收金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if summary_data.receivable_by_periods %}
                                            {% for item in summary_data.receivable_by_periods %}
                                            <tr>
                                                <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                                <td>第{{ item.period }}期</td>
                                                <td>{{ item.order_count }}</td>
                                                <td>{{ item.devices_count }}</td>
                                                <td>{{ item.amount }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td class="text-center">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单详情 -->
                <div class="tab-pane fade" id="orders" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>订单日期</th>
                                            <th>订单编号</th>
                                            <th>产品类型</th>
                                            <th>总融资额</th>
                                            <th>当前待收</th>
                                            <th>总期数</th>
                                            <th>当前待收期数</th>
                                            <th>台数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if summary_data.order_details %}
                                            {% for item in summary_data.order_details %}
                                            <tr>
                                                <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                                <td>{{ item.get('order_date', '-') }}</td>
                                                <td>{{ item.get('order_number', '-') }}</td>
                                                <td>{{ item.get('product_type', '-') }}</td>
                                                <td>{{ item.get('total_finance', '-') }}</td>
                                                <td>{{ item.get('current_receivable', '-') }}</td>
                                                <td>{{ item.get('total_periods', '-') }}</td>
                                                <td>{{ item.get('current_receivable_periods', '-') }}</td>
                                                <td>{{ item.get('devices_count', '-') }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td class="text-center">暂无数据</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务流水 -->
                <div class="tab-pane fade" id="finance" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>日期</th>
                                            <th>交易流水号</th>
                                            <th>交易类型</th>
                                            <th>金额</th>
                                            <th>资金流向</th>
                                            <th>订单编号</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if summary_data.finance_records %}
                                            {% for item in summary_data.finance_records %}
                                            <tr>
                                                <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                                <td>{{ item.transaction_date }}</td>
                                                <td>{{ item.transaction_id }}</td>
                                                <td>{{ item.transaction_type }}</td>
                                                <td>{{ item.amount }}</td>
                                                <td>{{ item.flow_direction }}</td>
                                                <td>{{ item.order_number }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td class="text-center">暂无数据</td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {% else %}
            <div class="alert alert-warning auto-dismiss-alert" role="alert">
                无法获取客户订单汇总数据，请稍后再试。
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 标记页面需要图表支持 -->
<div data-needs-charts style="display: none;"></div>

<!-- 引入主JavaScript文件 -->
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('客户汇总页面初始化开始');
    
    // 初始化数据选项卡
    initializeTabs();
    
    // 注册侧边栏表单提交事件处理
    registerFormSubmitHandlers();
    
    // 设置自动消失提示
    setupAutoDismissAlerts();
    
    // 检测API状态
    checkApiStatus();
    
    // 初始化侧边栏管理器
    new SidebarManager();
    
    {% if summary_data %}
    // 图表数据
    const chartData = {% if chart_data is defined %}{{ chart_data|tojson|safe }}{% else %}{} {% endif %};
    console.log('客户汇总页面 - 原始图表数据:', chartData);
    
    // 首先加载Chart.js库，然后初始化
    if (typeof loadChartSupport === 'function' && Object.keys(chartData).length > 0) {
        loadChartSupport().then(function() {
            console.log('Chart.js库加载成功，开始初始化客户页面图表');
            
            // 重要：首先初始化表格
            setTimeout(function() {
                initCustomerTables();
            }, 200);
            
            // 然后初始化图表
            setTimeout(function() {
                initCharts(chartData);
            }, 400);
            
            // 设置导出图表功能
            setupExportFunction();
            
            // 监听标签页切换事件
            setupTabListeners();
            
        }).catch(function(error) {
            console.error('Chart.js库加载失败:', error);
            showChartLoadError();
            
            // 仍然初始化表格
            setTimeout(function() {
                initCustomerTables();
                setupTabListeners();
            }, 200);
        });
    } else {
        // 没有图表数据或加载函数不可用，只初始化表格
        console.log('无图表数据或loadChartSupport不可用，仅初始化表格');
        setTimeout(function() {
            initCustomerTables();
            setupTabListeners();
        }, 200);
    }
    {% endif %}
    
    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        clearTimeout(window.resizeTimer);
        window.resizeTimer = setTimeout(function() {
            console.log('窗口大小变化，重新优化表格');
            optimizeForMobile();
            refreshTables();
        }, 250);
    });
    
    // 最后应用移动设备优化
    setTimeout(function() {
        optimizeForMobile();
        console.log('客户汇总页面移动端优化完成');
    }, 500);
});

// 初始化客户页面所有表格
function initCustomerTables() {
    console.log('开始初始化客户页面表格');
    
    const tables = document.querySelectorAll('.data-table');
    console.log(`找到${tables.length}个表格需要初始化`);
    
    tables.forEach((table, index) => {
        console.log(`初始化表格 #${index+1}`);
        
        // 确保表格未被初始化或销毁现有实例
        if ($.fn.DataTable.isDataTable(table)) {
            $(table).DataTable().destroy();
            console.log(`表格 #${index+1} 已存在，已销毁`);
        }
        
        // 使用main.js中的标准初始化方法
        const dataTable = initDataTable(table);
        if (dataTable) {
            console.log(`表格 #${index+1} 初始化成功`);
        } else {
            console.error(`表格 #${index+1} 初始化失败`);
        }
    });
    
    // 应用增强功能
    enhanceDataTables();
    console.log('表格增强功能应用成功');
    
    // 计算待收期数台数统计
    setTimeout(function() {
        if (typeof calculatePeriodDevicesCount === 'function') {
            calculatePeriodDevicesCount();
            console.log('台数统计计算完成');
        }
    }, 500);
}

// 刷新所有表格
function refreshTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
        if ($.fn.DataTable.isDataTable(table)) {
            try {
                const dt = $(table).DataTable();
                dt.columns.adjust();
                if (dt.responsive) {
                    dt.responsive.recalc();
                }
                console.log('表格布局已刷新');
            } catch(err) {
                console.error('表格调整失败:', err);
            }
        }
    });
}

// 初始化图表
function initCharts(chartData) {
    console.log('开始初始化客户汇总页面图表');
    
    // 检查Chart.js是否可用
    if (typeof Chart === 'undefined') {
        console.error('Chart.js库未加载，无法创建图表');
        showChartLoadError();
        return;
    }
    
    // 检查图表数据
    if (!chartData || (typeof chartData === 'object' && Object.keys(chartData).length === 0)) {
        console.log('无图表数据，跳过图表初始化');
        return;
    }
    
    // 订单图表
    if (chartData && chartData.order_chart) {
        try {
            const orderCanvas = document.getElementById('orderChart');
            if (orderCanvas) {
                const orderCtx = orderCanvas.getContext('2d');
                const orderChart = new Chart(orderCtx, {
                    type: 'bar',
                    data: JSON.parse(chartData.order_chart),
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '订单金额分布'
                            },
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                console.log('订单图表初始化成功');
                window.orderChart = orderChart;
            } else {
                console.error('未找到订单图表canvas元素');
            }
        } catch(e) {
            console.error('订单图表初始化失败:', e);
        }
    }
    
    // 待收图表
    if (chartData && chartData.payment_chart) {
        try {
            const paymentCanvas = document.getElementById('paymentChart');
            if (paymentCanvas) {
                const paymentCtx = paymentCanvas.getContext('2d');
                const paymentChart = new Chart(paymentCtx, {
                    type: 'bar',
                    data: JSON.parse(chartData.payment_chart),
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '待收金额分布'
                            },
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                console.log('待收图表初始化成功');
                window.paymentChart = paymentChart;
            } else {
                console.error('未找到待收图表canvas元素');
            }
        } catch(e) {
            console.error('待收图表初始化失败:', e);
        }
    }
}

// 设置导出图表功能
function setupExportFunction() {
    // 导出图表函数
    window.exportCharts = function() {
        const charts = {
            'orderChart': '订单金额分布图.png',
            'paymentChart': '待收金额分布图.png'
        };
        
        Object.entries(charts).forEach(([chartId, fileName]) => {
            const canvas = document.getElementById(chartId);
            if (canvas) {
                const link = document.createElement('a');
                link.download = fileName;
                link.href = canvas.toDataURL('image/png');
                link.click();
            }
        });
    };
}

// 设置标签页切换监听
function setupTabListeners() {
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function (e) {
            const targetId = e.target.getAttribute('data-bs-target');
            const targetPane = document.querySelector(targetId);
            if (targetPane) {
                const table = targetPane.querySelector('.data-table');
                if (table && $.fn.DataTable.isDataTable(table)) {
                    try {
                        $(table).DataTable().columns.adjust().responsive.recalc();
                        console.log('表格响应式布局重新计算');
                    } catch(err) {
                        console.error('表格调整失败:', err);
                    }
                }
            }
        });
    });
}

// 检查API状态
function checkApiStatus() {
    var apiStatusElement = document.getElementById('apiStatus');
    if (!apiStatusElement) return;
    
    // 设置超时时间（5秒）
    const timeout = 5000;
    
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    fetch("{{ url_for('api.ping') }}", {
        signal: controller.signal
    })
    .then(function(response) {
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error('API响应状态: ' + response.status);
        }
        return response.json();
    })
    .then(function(data) {
        if (data.status === 'ok') {
            apiStatusElement.innerHTML = '<span class="badge bg-success">正常</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-warning">异常</span>';
        }
    })
    .catch(function(error) {
        clearTimeout(timeoutId);
        console.error('API状态检查失败:', error);
        if (error.name === 'AbortError') {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">超时</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
        }
        
        // 5秒后重试
        setTimeout(checkApiStatus, 5000);
    });
}

// 注册表单提交事件处理
function registerFormSubmitHandlers() {
    // 日期筛选表单提交
    const dateFilterForm = document.getElementById('dateFilterForm');
    if (dateFilterForm) {
        dateFilterForm.addEventListener('submit', function(event) {
            event.preventDefault();
            AppNavigation.filterByDate();
        });
    }
    
    // 客户搜索表单事件处理
    const customerSearchForm = document.getElementById('customerSearchForm');
    if (customerSearchForm) {
        customerSearchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            AppNavigation.searchCustomer();
        });
    }
    
    // 逾期订单查询按钮事件处理
    const overdueButton = document.getElementById('overdueButton');
    if (overdueButton) {
        overdueButton.addEventListener('click', function(event) {
            event.preventDefault();
            window.location.href = "{{ url_for('main.query.overdue_orders') }}";
        });
    }
}

// 显示图表加载错误信息
function showChartLoadError() {
    console.log('显示客户页面图表加载错误信息');
    
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(function(container) {
        container.innerHTML = `
            <div class="alert alert-warning text-center" role="alert">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>图表加载失败</strong><br>
                请刷新页面重试
            </div>
        `;
    });
}
</script>
{% endblock %}