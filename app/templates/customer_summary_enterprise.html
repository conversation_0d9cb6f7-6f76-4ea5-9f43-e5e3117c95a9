{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 客户订单汇总 - {{ customer_name }}{% endblock %}

{% block styles %}
<!-- 企业级客户汇总页面样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise/customer-summary-enterprise.css') }}">
<!-- 移动端适配专用样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise/mobile-adaptive.css') }}">
<!-- 新版桌面端数据网格样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise/desktop-data-grid.css') }}">
{% endblock %}

{% block content %}
<div class="enterprise-customer-summary">
    <div class="container-fluid">
        <div class="row">
            <!-- 引入侧边栏模板 -->
            {% include 'sidebar.html' %}

            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10 main-content">
                
                <!-- 页面标题区域 -->
                <div class="enterprise-page-header">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center">
                        <h1 class="enterprise-page-title">
                            <i class="bi bi-person-lines-fill"></i>
                            客户订单汇总: {{ customer_name }}
                        </h1>
                        <div class="enterprise-page-actions">
                            <a href="javascript:history.back()" class="enterprise-action-btn">
                                <i class="bi bi-arrow-left"></i>
                                返回
                            </a>
                            <a href="javascript:exportCharts()" class="enterprise-action-btn">
                                <i class="bi bi-download"></i>
                                导出图表
                            </a>
                            <a href="javascript:location.reload()" class="enterprise-action-btn">
                                <i class="bi bi-arrow-clockwise"></i>
                                刷新数据
                            </a>
                        </div>
                    </div>
                </div>

                {% if summary_data %}
                <!-- 客户基本信息卡片 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h5 class="enterprise-card-title">
                            <i class="bi bi-person-badge"></i>
                            客户基本信息
                        </h5>
                    </div>
                    <div class="enterprise-card-body">
                        <div class="enterprise-customer-info">
                            <div>
                                <table class="enterprise-info-table">
                                    <tbody>
                                        <tr>
                                            <th>客户名称</th>
                                            <td>{{ customer_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系电话</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('phones', [])|join(', ') if summary_data.get('basic_info', {}).get('phones') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>设备型号</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('device_models', [])|join(', ') if summary_data.get('basic_info', {}).get('device_models') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>总台数</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('total_devices_count', 0) or 0 }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div>
                                <table class="enterprise-info-table">
                                    <tbody>
                                        <tr>
                                            <th>客服归属</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('services', [])|join(', ') if summary_data.get('basic_info', {}).get('services') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>业务归属</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('business_affiliations', [])|join(', ') if summary_data.get('basic_info', {}).get('business_affiliations') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>客户备注</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('customer_remarks', '-') or '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>数据更新</th>
                                            <td>{{ moment().format('YYYY-MM-DD HH:mm') if moment else '实时数据' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单汇总统计卡片 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h5 class="enterprise-card-title">
                            <i class="bi bi-graph-up-arrow"></i>
                            订单汇总统计
                        </h5>
                    </div>
                    <div class="enterprise-card-body">
                        <!-- 统计卡片网格 -->
                        <div class="enterprise-stats-grid">
                            <div class="enterprise-stat-card stat-primary">
                                <div class="enterprise-stat-title">总订单数</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('total_count', 0) or 0 }}</div>
                            </div>
                            <div class="enterprise-stat-card stat-success">
                                <div class="enterprise-stat-title">总融资金额</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('total_amount', '¥0') or '¥0' }}</div>
                            </div>
                            <div class="enterprise-stat-card stat-warning">
                                <div class="enterprise-stat-title">已还款金额</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('repaid_amount', '¥0') or '¥0' }}</div>
                            </div>
                            <div class="enterprise-stat-card stat-secondary">
                                <div class="enterprise-stat-title">当前待收</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('current_receivable', '¥0') or '¥0' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据可视化图表 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h5 class="enterprise-card-title">
                            <i class="bi bi-bar-chart-line"></i>
                            数据可视化分析
                        </h5>
                    </div>
                    <div class="enterprise-card-body">
                        <div class="enterprise-charts-grid">
                            <!-- 订单金额分布图表 -->
                            <div class="enterprise-chart-container">
                                <div class="enterprise-chart-title">订单金额分布</div>
                                <div class="enterprise-chart-wrapper">
                                    <canvas id="orderAmountChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- 待收金额分布图表 -->
                            <div class="enterprise-chart-container">
                                <div class="enterprise-chart-title">待收金额分布</div>
                                <div class="enterprise-chart-wrapper">
                                    <canvas id="receivableAmountChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- 业务类型分布图表 -->
                            <div class="enterprise-chart-container">
                                <div class="enterprise-chart-title">业务类型分布</div>
                                <div class="enterprise-chart-wrapper">
                                    <canvas id="businessTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格区域 -->
                <div class="enterprise-data-tabs">
                    <!-- 统一标签页导航 -->
                    <div class="enterprise-tab-header">
                        <!-- 新版桌面端标签导航 - 简化版 -->
                        <div class="desktop-tab-nav d-none d-md-block">
                            <div class="nav-tabs-container">
                                <div class="nav-tab-item active" data-target="receivable">
                                    <i class="bi bi-currency-dollar"></i>
                                    <span>待收明细</span>
                                </div>
                                <div class="nav-tab-item" data-target="orders">
                                    <i class="bi bi-list-ul"></i>
                                    <span>订单详情</span>
                                </div>
                                <div class="nav-tab-item" data-target="finance">
                                    <i class="bi bi-receipt"></i>
                                    <span>财务流水</span>
                                </div>
                            </div>
                        </div>

                        <!-- 移动端下拉选择器 -->
                        <div class="mobile-tab-selector d-md-none">
                            <div class="mobile-tab-category">数据视图选择</div>
                            <select class="form-select mobile-tab-select" id="mobileTabSelect">
                                <option value="receivable" selected>待收明细</option>
                                <option value="orders">订单详情</option>
                                <option value="finance">财务流水</option>
                            </select>
                            <div class="mobile-tab-indicator">
                                <div class="indicator-content">
                                    <div class="indicator-icon">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                    <span class="indicator-text">待收明细</span>
                                    <span class="indicator-badge">5项</span>
                                </div>
                                <i class="bi bi-chevron-down dropdown-arrow"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 标签页内容 -->
                    <div class="tab-content enterprise-tab-content" id="customerTabsContent">
                        <!-- 待收明细 -->
                        <div class="tab-pane fade show active" id="receivable" role="tabpanel" 
                             aria-labelledby="receivable-tab">
                            
                            <!-- 新版桌面端数据网格 -->
                            <div class="desktop-data-grid d-none d-md-block">
                                <div class="data-grid-header">
                                    <h6 class="grid-title">待收明细数据</h6>
                                    <div class="grid-actions">
                                        <div class="search-container">
                                            <div class="search-input-wrapper">
                                                <input type="text" class="form-control search-input" 
                                                       placeholder="搜索期数、金额、状态..." 
                                                       data-tab="receivable">
                                                <i class="bi bi-search search-icon"></i>
                                                <button class="btn clear-search-btn" type="button" title="清除搜索">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            </div>
                                            <div class="search-stats">
                                                <span class="search-count">显示 <span class="visible-count">0</span> / <span class="total-count">0</span> 条</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" onclick="exportReceivableData()">
                                            <i class="bi bi-download"></i> 导出
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshReceivableData()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                
                                {% if summary_data.get('receivable_by_periods') %}
                                <div class="data-grid-container">
                                    {% for item in summary_data.get('receivable_by_periods', []) %}
                                    <div class="desktop-data-card receivable-card">
                                        <div class="card-header-section">
                                            <div class="period-info">
                                                <div class="period-number">第{{ item.get('period', '-') }}期</div>
                                                <div class="period-status">
                                                    {% if item.get('overdue_days') and item.get('overdue_days') > 0 %}
                                                        <span class="status-badge overdue">
                                                            <i class="bi bi-exclamation-triangle"></i>
                                                            逾期{{ item.get('overdue_days') }}天
                                                        </span>
                                                    {% else %}
                                                        <span class="status-badge normal">
                                                            <i class="bi bi-check-circle"></i>
                                                            正常
                                                        </span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body-section">
                                            <div class="data-row">
                                                <div class="data-item">
                                                    <div class="data-label">订单数量</div>
                                                    <div class="data-value">{{ item.get('order_count', 0) }}</div>
                                                </div>
                                                <div class="data-item">
                                                    <div class="data-label">设备台数</div>
                                                    <div class="data-value">{{ item.get('devices_count', 0) }}</div>
                                                </div>
                                                <div class="data-item primary">
                                                    <div class="data-label">待收金额</div>
                                                    <div class="data-value amount">{{ item.get('amount', '0') }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="empty-state">
                                    <i class="bi bi-inbox"></i>
                                    <h5>暂无待收明细数据</h5>
                                    <p>当前没有可显示的待收明细信息</p>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 移动端卡片视图 -->
                            <div class="mobile-card-view d-lg-none">
                                                            {% if summary_data.get('receivable_by_periods') %}
                                {% for item in summary_data.get('receivable_by_periods', []) %}
                                    <div class="mobile-data-card" data-category="receivable">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="bi bi-calendar-check"></i>
                                                第{{ item.period }}期
                                            </div>
                                            <div class="card-status">
                                                {% if item.overdue_days and item.overdue_days > 0 %}
                                                    <span class="status-badge status-overdue">
                                                        <i class="bi bi-exclamation-triangle"></i>
                                                        逾期{{ item.overdue_days }}天
                                                    </span>
                                                {% else %}
                                                    <span class="status-badge status-normal">
                                                        <i class="bi bi-check-circle"></i>
                                                        正常
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="card-data-grid">
                                                <div class="data-item">
                                                    <div class="data-label">订单数量</div>
                                                    <div class="data-value">{{ item.order_count }}</div>
                                                </div>
                                                <div class="data-item">
                                                    <div class="data-label">台数</div>
                                                    <div class="data-value">{{ item.devices_count }}</div>
                                                </div>
                                                <div class="data-item highlight">
                                                    <div class="data-label">待收金额</div>
                                                    <div class="data-value amount">{{ item.amount }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="showDetails(this)">
                                                <i class="bi bi-eye"></i> 查看详情
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="expandCard(this)">
                                                <i class="bi bi-arrows-expand"></i> 展开
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="mobile-empty-state">
                                        <i class="bi bi-inbox"></i>
                                        <h5>暂无待收明细数据</h5>
                                        <p>当前没有可显示的待收明细信息</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 订单详情 -->
                        <div class="tab-pane fade" id="orders" role="tabpanel" aria-labelledby="orders-tab">
                            
                            <!-- 新版桌面端数据网格 -->
                            <div class="desktop-data-grid d-none d-md-block">
                                <div class="data-grid-header">
                                    <h6 class="grid-title">订单详情数据</h6>
                                    <div class="grid-actions">
                                        <div class="search-container">
                                            <div class="search-input-wrapper">
                                                <input type="text" class="form-control search-input" 
                                                       placeholder="搜索订单号、客户、产品类型..." 
                                                       data-tab="orders">
                                                <i class="bi bi-search search-icon"></i>
                                                <button class="btn clear-search-btn" type="button" title="清除搜索">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            </div>
                                            <div class="search-stats">
                                                <span class="search-count">显示 <span class="visible-count">0</span> / <span class="total-count">0</span> 条</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" onclick="exportOrdersData()">
                                            <i class="bi bi-download"></i> 导出
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshOrdersData()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                
                                {% if summary_data.get('order_details') %}
                                <div class="data-grid-container">
                                    {% for item in summary_data.get('order_details', []) %}
                                    <div class="desktop-data-card order-card">
                                        <div class="card-header-section">
                                            <div class="order-header">
                                                <div class="order-number">
                                                    <i class="bi bi-file-earmark-text"></i>
                                                    {{ item.get('order_number', '订单编号') }}
                                                </div>
                                                <div class="order-date">
                                                    <i class="bi bi-calendar3"></i>
                                                    {{ item.get('order_date', '-') }}
                                                </div>
                                                <div class="business-type">
                                                    <span class="type-badge">{{ item.get('business_type', '-') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body-section">
                                            <div class="order-amounts">
                                                <div class="amount-item primary">
                                                    <div class="amount-label">总融资额</div>
                                                    <div class="amount-value">{{ item.get('total_finance', '-') }}</div>
                                                </div>
                                                <div class="amount-item secondary">
                                                    <div class="amount-label">当前待收</div>
                                                    <div class="amount-value">{{ item.get('current_receivable', '-') }}</div>
                                                </div>
                                            </div>
                                            <div class="order-details">
                                                <div class="detail-row">
                                                    <div class="detail-item">
                                                        <span class="detail-label">产品类型</span>
                                                        <span class="detail-value">{{ item.get('product_type', '-') }}</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">设备台数</span>
                                                        <span class="detail-value">{{ item.get('devices_count', '-') }} 台</span>
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item">
                                                        <span class="detail-label">总期数</span>
                                                        <span class="detail-value">{{ item.get('total_periods', '-') }} 期</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">剩余期数</span>
                                                        <span class="detail-value">{{ item.get('current_receivable_periods', '-') }} 期</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="empty-state">
                                    <i class="bi bi-file-earmark-x"></i>
                                    <h5>暂无订单详情数据</h5>
                                    <p>当前没有可显示的订单详情信息</p>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 移动端卡片视图 -->
                            <div class="mobile-card-view d-lg-none">
                                                            {% if summary_data.get('order_details') %}
                                {% for item in summary_data.get('order_details', []) %}
                                    <div class="mobile-data-card order-detail-card" data-category="orders">
                                        <div class="card-header">
                                            <div class="card-title-section">
                                                <div class="card-title">
                                                    <i class="bi bi-file-earmark-text"></i>
                                                    <span class="title-text">{{ item.get('order_number', '订单编号') }}</span>
                                                </div>
                                                <div class="card-date">
                                                    <i class="bi bi-calendar3"></i>
                                                    <span class="date-text">{{ item.get('order_date', '-') }}</span>
                                                </div>
                                            </div>
                                            <div class="card-status-section">
                                                <span class="status-badge status-business">
                                                    {{ item.get('business_type', '-') }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="order-summary-section">
                                                <div class="summary-item primary">
                                                    <div class="summary-label">总融资额</div>
                                                    <div class="summary-value amount-primary">{{ item.get('total_finance', '-') }}</div>
                                                </div>
                                                <div class="summary-item secondary">
                                                    <div class="summary-label">当前待收</div>
                                                    <div class="summary-value amount-secondary">{{ item.get('current_receivable', '-') }}</div>
                                                </div>
                                            </div>
                                            
                                            <div class="order-details-section">
                                                <div class="detail-group">
                                                    <div class="detail-row">
                                                        <div class="detail-item">
                                                            <div class="detail-label">产品类型</div>
                                                            <div class="detail-value">{{ item.get('product_type', '-') }}</div>
                                                        </div>
                                                        <div class="detail-item">
                                                            <div class="detail-label">设备台数</div>
                                                            <div class="detail-value">{{ item.get('devices_count', '-') }} 台</div>
                                                        </div>
                                                    </div>
                                                    <div class="detail-row">
                                                        <div class="detail-item">
                                                            <div class="detail-label">总期数</div>
                                                            <div class="detail-value">{{ item.get('total_periods', '-') }} 期</div>
                                                        </div>
                                                        <div class="detail-item">
                                                            <div class="detail-label">剩余期数</div>
                                                            <div class="detail-value">{{ item.get('current_receivable_periods', '-') }} 期</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="showOrderDetails(this)">
                                                <i class="bi bi-info-circle"></i> 详细信息
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="showPaymentHistory(this)">
                                                <i class="bi bi-clock-history"></i> 还款记录
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="mobile-empty-state">
                                        <i class="bi bi-file-earmark-x"></i>
                                        <h5>暂无订单详情数据</h5>
                                        <p>当前没有可显示的订单详情信息</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 财务流水 -->
                        <div class="tab-pane fade" id="finance" role="tabpanel" aria-labelledby="finance-tab">
                            
                            <!-- 新版桌面端数据网格 -->
                            <div class="desktop-data-grid d-none d-md-block">
                                <div class="data-grid-header">
                                    <h6 class="grid-title">财务流水数据</h6>
                                    <div class="grid-actions">
                                        <div class="search-container">
                                            <div class="search-input-wrapper">
                                                <input type="text" class="form-control search-input" 
                                                       placeholder="搜索流水号、交易类型、金额..." 
                                                       data-tab="finance">
                                                <i class="bi bi-search search-icon"></i>
                                                <button class="btn clear-search-btn" type="button" title="清除搜索">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            </div>
                                            <div class="search-stats">
                                                <span class="search-count">显示 <span class="visible-count">0</span> / <span class="total-count">0</span> 条</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" onclick="exportFinanceData()">
                                            <i class="bi bi-download"></i> 导出
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshFinanceData()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                
                                {% if summary_data.get('finance_records') %}
                                <div class="data-grid-container">
                                    {% for item in summary_data.get('finance_records', []) %}
                                    <div class="desktop-data-card finance-card">
                                        <div class="card-header-section">
                                            <div class="finance-header">
                                                <div class="transaction-type">
                                                    <i class="bi bi-credit-card"></i>
                                                    {{ item.transaction_type }}
                                                </div>
                                                <div class="transaction-date">
                                                    <i class="bi bi-calendar-event"></i>
                                                    {{ item.transaction_date }}
                                                </div>
                                                <div class="flow-direction">
                                                    <span class="flow-badge {{ 'flow-in' if '收入' in item.flow_direction else 'flow-out' }}">
                                                        <i class="bi {{ 'bi-arrow-down-circle' if '收入' in item.flow_direction else 'bi-arrow-up-circle' }}"></i>
                                                        {{ item.flow_direction }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body-section">
                                            <div class="transaction-amount">
                                                <div class="amount-section {{ 'positive' if '收入' in item.flow_direction else 'negative' }}">
                                                    <span class="amount-value">{{ item.amount }}</span>
                                                </div>
                                            </div>
                                            <div class="transaction-details">
                                                <div class="detail-row">
                                                    <div class="detail-item">
                                                        <span class="detail-label">交易流水号</span>
                                                        <span class="detail-value">{{ item.transaction_id }}</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">关联订单</span>
                                                        <span class="detail-value">{{ item.order_number }}</span>
                                                    </div>
                                                </div>
                                                {% if item.get('remarks') and item.get('remarks') != '-' %}
                                                <div class="detail-row">
                                                    <div class="detail-item full-width">
                                                        <span class="detail-label">备注</span>
                                                        <span class="detail-value">{{ item.remarks }}</span>
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="empty-state">
                                    <i class="bi bi-journal-x"></i>
                                    <h5>暂无财务流水数据</h5>
                                    <p>当前没有可显示的财务流水信息</p>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 移动端卡片视图 -->
                            <div class="mobile-card-view d-lg-none">
                                {% if summary_data.get('finance_records') %}
                                    {% for item in summary_data.get('finance_records', []) %}
                                    <div class="mobile-data-card" data-category="finance">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="bi bi-credit-card"></i>
                                                {{ item.transaction_type }}
                                            </div>
                                            <div class="card-meta">
                                                <span class="meta-date">{{ item.transaction_date }}</span>
                                                <span class="meta-flow {{ 'flow-in' if '收入' in item.flow_direction else 'flow-out' }}">
                                                    <i class="bi {{ 'bi-arrow-down-circle' if '收入' in item.flow_direction else 'bi-arrow-up-circle' }}"></i>
                                                    {{ item.flow_direction }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="card-main-info">
                                                <div class="main-amount {{ 'amount-positive' if '收入' in item.flow_direction else 'amount-negative' }}">
                                                    <span class="amount-value">{{ item.amount }}</span>
                                                </div>
                                                <div class="main-reference">
                                                    <span class="reference-label">关联订单</span>
                                                    <span class="reference-value">{{ item.order_number }}</span>
                                                </div>
                                            </div>
                                            <div class="card-transaction-details">
                                                <div class="transaction-id">
                                                    <span class="detail-label">交易流水号</span>
                                                    <span class="detail-value">{{ item.transaction_id }}</span>
                                                </div>
                                                {% if item.get('remarks') and item.get('remarks') != '-' %}
                                                <div class="transaction-remarks">
                                                    <span class="detail-label">备注</span>
                                                    <span class="detail-value">{{ item.remarks }}</span>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-info" onclick="showTransactionDetails(this)">
                                                <i class="bi bi-receipt"></i> 交易详情
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="copyTransactionId(this)">
                                                <i class="bi bi-clipboard"></i> 复制流水号
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="mobile-empty-state">
                                        <i class="bi bi-journal-x"></i>
                                        <h5>暂无财务流水数据</h5>
                                        <p>当前没有可显示的财务流水信息</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <!-- 无数据状态 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-body">
                        <div class="d-flex align-items-center justify-content-center py-5">
                            <div class="text-center">
                                <i class="bi bi-inbox" style="font-size: 4rem; color: #cbd5e1; margin-bottom: 1rem;"></i>
                                <h4 class="text-muted">暂无客户数据</h4>
                                <p class="text-muted">无法获取客户订单汇总数据，请检查客户信息或稍后再试。</p>
                                <button class="btn btn-primary" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    重新加载
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的图表数据元素 -->
{% if chart_data %}
<script id="chart-data" type="application/json">{{ chart_data|tojson|safe }}</script>
{% endif %}
{% endblock %}

{% block scripts %}
<!-- 企业级客户汇总页面脚本 -->
<script src="{{ url_for('static', filename='js/enterprise/customer-summary-enterprise.js') }}"></script>
<!-- 移动端适配专用脚本 -->
<script src="{{ url_for('static', filename='js/enterprise/mobile-adaptive.js') }}"></script>
<!-- 新版桌面端数据网格脚本 -->
<script src="{{ url_for('static', filename='js/enterprise/desktop-data-grid.js') }}"></script>

<!-- 页面初始化脚本 -->
<script>
// 设置全局图表数据
{% if chart_data %}
window.chartData = {{ chart_data|tojson|safe }};
{% else %}
window.chartData = null;
{% endif %}

// 提供页面级别的导出功能
window.exportCharts = function() {
    if (window.enterpriseCustomerSummaryManager) {
        window.enterpriseCustomerSummaryManager.setupChartExport();
        const event = new Event('click');
        window.exportCharts();
    } else {
        console.warn('客户汇总管理器未初始化');
        alert('导出功能暂时不可用，请稍后再试');
    }
};

// 提供页面级别的数据刷新功能
window.refreshCustomerData = function() {
    location.reload();
};

// 错误处理和降级方案
window.addEventListener('error', function(e) {
    console.error('页面发生错误:', e.error);
    
    // 简单的错误恢复
    if (e.error.message && e.error.message.includes('Chart')) {
        console.log('图表相关错误，尝试使用后备方案');
        const chartContainers = document.querySelectorAll('.enterprise-chart-wrapper');
        chartContainers.forEach(container => {
            if (!container.querySelector('canvas')) {
                container.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                        <div class="text-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                            <p class="mt-2 mb-0">图表加载失败</p>
                            <small>请刷新页面重试</small>
                        </div>
                    </div>
                `;
            }
        });
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && window.enterpriseCustomerSummaryManager) {
        // 页面重新可见时，刷新图表和表格
        setTimeout(() => {
            window.enterpriseCustomerSummaryManager.resizeCharts();
            window.enterpriseCustomerSummaryManager.refreshAllTables();
        }, 100);
    }
});

// 打印支持
window.addEventListener('beforeprint', function() {
    // 打印前的准备工作
    const charts = document.querySelectorAll('canvas');
    charts.forEach(canvas => {
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';
    });
});

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+R 或 F5: 刷新数据
    if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
        e.preventDefault();
        location.reload();
    }
    
    // Ctrl+E: 导出图表
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        if (typeof exportCharts === 'function') {
            exportCharts();
        }
    }
    
    // Ctrl+B: 返回上一页
    if (e.ctrlKey && e.key === 'b') {
        e.preventDefault();
        history.back();
    }
});

console.log('企业级客户汇总页面脚本加载完成');
</script>
{% endblock %}