{% extends "base.html" %}

{% block title %}太享查询_{{ version }}{% endblock %}

{% block styles %}
{{ super() }}
<!-- 引入企业级统一表格样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-unified-table.css') }}">

<!-- 期数字段视觉优化动画CSS -->
<style>
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    
    /* 期数字段容器优化 */
    .installment-field-container {
        background: #f8f9fa;
        padding: 6px;
        border-radius: 4px;
        text-align: center;
        min-width: 120px;
        line-height: 1.2;
    }
    
    .installment-date {
        font-size: 0.75rem;
        color: #6c757d;
        margin-bottom: 3px;
        font-family: 'Courier New', monospace;
    }
    
    .installment-status {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.7rem;
        font-weight: 500;
        display: inline-block;
    }
    
    .installment-status.pulse {
        animation: pulse 2s infinite;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">数据查询</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group mr-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportDataWithSearch('excel')">
                        <i class="bi bi-file-earmark-excel"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportDataWithSearch('csv')">
                        <i class="bi bi-file-earmark-text"></i> 导出CSV
                    </button>
                </div>
            </div>
        </div>

        <!-- 选项卡导航 -->
        <ul class="nav nav-tabs" id="dataTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="filter-tab" data-bs-toggle="tab" data-bs-target="#filter" type="button" role="tab">日期筛选结果</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer" type="button" role="tab">客户订单</button>
            </li>
        </ul>

        <!-- 选项卡内容 -->
        <div class="tab-content" id="dataTabsContent">
            <!-- 日期筛选结果 -->
            <div class="tab-pane fade show active" id="filter" role="tabpanel">
                {% if filter_results and filter_results.results and filter_results.results|length > 0 %}
                    <div class="pt-3">
                        <div class="alert alert-success auto-dismiss-alert">
                            共找到 {{ filter_results.results|length }} 条符合条件的记录
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table" style="width:100%" data-type="filter" id="filterTable">
                            <thead>
                                <tr>
                                    <th></th> <!-- 用于响应式详情控制 -->
                                    {% for column in filter_results.columns %}
                                    <th>{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in filter_results.results %}
                                <tr {% if '备注' in row %}data-status="{{ row['备注'] }}"{% endif %}>
                                    <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                    {% for column in filter_results.columns %}
                                    <td {% if column == '备注' %}class="{{ row.get(column, '') }}"{% endif %}
                                       {% if column in ['订单编号', '客户手机', '客户姓名'] %}style="white-space: nowrap; min-width: 120px;"{% endif %}
                                       {% if column in ['当前待收', '总待收', '成本', '金额'] %}class="amount-field"{% endif %}
                                       {% if column in ['业务', '客服', '产品', '产品类型'] %}class="category-field"{% endif %}
                                       {% if column in ['订单日期', '账单日期', '首次逾期日期', '日期'] %}class="date-field"{% endif %}
                                       {% if column in ['逾期天数', '逾期期数', '首次逾期期数', '状态'] %}class="status-field"{% endif %}>
                                        {% if column in row %}
                                            {% if column in ['当前待收', '总待收', '成本', '金额'] and row[column]|string|float(0) > 0 %}
                                                {{ "%.2f"|format(row[column]|float) }}
                                            {% elif column in ['逾期天数', '逾期期数'] and row[column]|string|int(0) > 0 %}
                                                <span class="badge bg-danger">{{ row[column] }}</span>
                                            {% elif column in ['业务', '客服'] %}
                                                <span class="badge bg-primary">{{ row[column] }}</span>
                                            {% elif column in ['产品', '产品类型'] %}
                                                {% if '电商' in row[column] %}
                                                    <span class="badge bg-info text-dark" data-product-type="电商">{{ row[column] }}</span>
                                                {% elif '租赁' in row[column] %}
                                                    <span class="badge bg-info text-dark" data-product-type="租赁">{{ row[column] }}</span>
                                                {% else %}
                                                    <span class="badge bg-info text-dark">{{ row[column] }}</span>
                                                {% endif %}
                                            {% elif column in ['订单日期', '账单日期', '首次逾期日期', '日期'] %}
                                                <span class="badge bg-secondary">{{ row[column] }}</span>
                                            {% else %}
                                                {{ row[column] }}
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% elif filter_results and 'error' in filter_results %}
                    <div class="alert alert-danger mt-3">
                        获取数据失败: {{ filter_results.error }}
                    </div>
                {% else %}
                    <div class="alert alert-info mt-3">
                        请选择日期并点击筛选按钮查询数据。
                    </div>
                {% endif %}
            </div>

            <!-- 客户订单 -->
            <div class="tab-pane fade" id="customer" role="tabpanel">
                {% if customer_results and customer_results.results and customer_results.results|length > 0 %}
                    <div class="d-flex justify-content-between align-items-center pt-3 mb-3">
                        <div class="alert alert-success mb-0 flex-grow-1 me-3 auto-dismiss-alert">
                            共找到 {{ customer_results.results|length }} 条 {{ customer_name }} 的订单记录
                        </div>
                        <button class="btn btn-primary ms-3" onclick="viewCustomerSummary('{{ customer_name }}')">
                            <i class="bi bi-pie-chart"></i> 查看{{ customer_name }}汇总数据
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table" style="width:100%" data-type="customer" id="customerTable">
                            <thead>
                                <tr>
                                    <th></th> <!-- 用于响应式详情控制 -->
                                    {% for column in customer_results.columns %}
                                    <th>{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in customer_results.results %}
                                <tr data-customer="{{ row.get('客户姓名', '') }}">
                                    <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                    {% for column in customer_results.columns %}
                                    <td {% if column.startswith('期数') or column.startswith('客户姓名') %}class="{{ row.get(column, '') | status_class if column.startswith('期数') else '' }}"{% endif %}
                                       {% if column in ['订单编号', '客户手机', '客户姓名'] %}style="white-space: nowrap; min-width: 120px;"{% endif %}
                                       {% if column in ['当前待收', '总待收', '成本', '金额'] %}class="amount-field"{% endif %}
                                       {% if column in ['业务', '客服', '产品', '产品类型'] %}class="category-field"{% endif %}
                                       {% if column in ['订单日期', '账单日期', '首次逾期日期', '日期'] %}class="date-field"{% endif %}
                                       {% if column in ['逾期天数', '逾期期数', '首次逾期期数', '状态'] %}class="status-field"{% endif %}
                                       {% if column.startswith('期数') and column != '期数' %}class="installment-field"{% endif %}>
                                        {% if column in row %}
                                            {% if column in ['当前待收', '总待收', '成本', '金额'] and row[column]|string|float(0) > 0 %}
                                                {{ "%.2f"|format(row[column]|float) }}
                                            {% elif column in ['逾期天数', '逾期期数'] and row[column]|string|int(0) > 0 %}
                                                <span class="badge bg-danger">{{ row[column] }}</span>
                                            {% elif column in ['业务', '客服'] %}
                                                <span class="badge bg-primary">{{ row[column] }}</span>
                                            {% elif column in ['产品', '产品类型'] %}
                                                {% if '电商' in row[column] %}
                                                    <span class="badge bg-info text-dark" data-product-type="电商">{{ row[column] }}</span>
                                                {% elif '租赁' in row[column] %}
                                                    <span class="badge bg-info text-dark" data-product-type="租赁">{{ row[column] }}</span>
                                                {% else %}
                                                    <span class="badge bg-info text-dark">{{ row[column] }}</span>
                                                {% endif %}
                                            {% elif column in ['订单日期', '账单日期', '首次逾期日期', '日期'] %}
                                                <span class="badge bg-secondary">{{ row[column] }}</span>
                                            {% elif column.startswith('期数') and column != '期数' %}
                                                <!-- 期数字段优化显示 - 简化测试版本 -->
                                                {% set installment_info = row[column]|string %}
                                                <div style="background: #f8f9fa; padding: 6px; border-radius: 4px; text-align: center; min-width: 120px;">
                                                    {% if installment_info and installment_info != '-' and installment_info.strip() != '' %}
                                                        {% set date_part = installment_info.split('(')[0] if '(' in installment_info else installment_info.split('（')[0] if '（' in installment_info else installment_info %}
                                                        
                                                        <div style="font-size: 0.75rem; color: #6c757d; margin-bottom: 3px;">
                                                            {{ date_part.strip() }}
                                                        </div>
                                                        
                                                        {% if '按时还款' in installment_info %}
                                                            <span style="background: #d1e7dd; color: #0f5132; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; font-weight: 500;">
                                                                ✓ 按时还款
                                                            </span>
                                                        {% elif '逾期未还' in installment_info %}
                                                            <span style="background: #f8d7da; color: #721c24; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; font-weight: 500; animation: pulse 2s infinite;">
                                                                ⚠ 逾期未还
                                                            </span>
                                                        {% elif '逾期还款' in installment_info %}
                                                            <span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; font-weight: 500;">
                                                                ⏰ 逾期还款
                                                            </span>
                                                        {% elif '提前还款' in installment_info %}
                                                            <span style="background: #d1ecf1; color: #0c5460; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; font-weight: 500;">
                                                                ⭐ 提前还款
                                                            </span>
                                                        {% else %}
                                                            <span style="background: #e9ecef; color: #495057; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem;">
                                                                {{ installment_info }}
                                                            </span>
                                                        {% endif %}
                                                    {% else %}
                                                        <span style="color: #6c757d;">-</span>
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                {{ row[column] }}
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% elif customer_results and 'error' in customer_results %}
                    <div class="alert alert-danger mt-3">
                        获取客户数据失败: {{ customer_results.error }}
                    </div>
                {% else %}
                    <div class="alert alert-info mt-3">
                        在侧边栏客户姓名搜索框中输入客户姓名并点击搜索按钮查询数据。
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 将后端数据存储到全局变量中，供前端JavaScript使用 -->
<script>
{% if filter_results %}
    window.filterResults = {{ filter_results|tojson|safe }};
    console.log('接收到筛选数据:', {
        resultCount: window.filterResults.results ? window.filterResults.results.length : 0,
        columns: window.filterResults.columns
    });
{% endif %}

{% if customer_results %}
    window.customerResults = {{ customer_results|tojson|safe }};
    console.log('接收到客户数据:', {
        resultCount: window.customerResults.results ? window.customerResults.results.length : 0,
        columns: window.customerResults.columns
    });
{% endif %}

// 页面加载时处理URL参数，激活对应的选项卡
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    
    // 如果URL中有tab参数，激活对应的选项卡
    if (tabParam) {
        console.log('检测到URL参数tab=' + tabParam);
        const tabElement = document.getElementById(tabParam + '-tab');
        if (tabElement) {
            // 使用Bootstrap的Tab API激活选项卡
            const tab = new bootstrap.Tab(tabElement);
            tab.show();
            console.log('已激活选项卡: ' + tabParam);
            
            // 如果是客户订单选项卡，确保表格正确初始化
            if (tabParam === 'customer') {
                setTimeout(function() {
                    const tableElement = document.querySelector('#customer .data-table');
                    if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                        $(tableElement).DataTable().columns.adjust().responsive.recalc();
                    }
                }, 100);
            }
        }
    }
    
    // 调试期数字段显示效果 - 性能优化版本
    setTimeout(function() {
        console.log('=== 期数字段调试开始 ===');
        
        // 使用文档片段避免频繁DOM操作
        const fragment = document.createDocumentFragment();
        
        // 一次性获取所有需要的元素，避免重复查询
        const customerTable = document.querySelector('#customerTable');
        
        if (customerTable) {
            console.log('✅ 找到客户表格');
            
            // 使用requestAnimationFrame优化DOM读取
            requestAnimationFrame(() => {
                const rows = customerTable.querySelectorAll('tbody tr');
                console.log('客户表格行数:', rows.length);
                
                if (rows.length > 0) {
                    const firstRow = rows[0];
                    const cells = firstRow.querySelectorAll('td');
                    console.log('第一行单元格数量:', cells.length);
                    
                    // 检查期数字段是否已优化
                    let installmentFound = 0;
                    let optimizedCount = 0;
                    
                    cells.forEach((cell, index) => {
                        const content = cell.textContent.trim();
                        if (content.includes('2025') || content.includes('2024')) {
                            installmentFound++;
                            
                            // 检查是否有分层结构
                            const hasOptimizedStructure = cell.querySelector('div[style*="background"]');
                            if (hasOptimizedStructure) {
                                optimizedCount++;
                                console.log(`✅ 单元格 ${index}: 期数字段已优化`);
                            } else {
                                console.log(`❌ 单元格 ${index}: ${content.substring(0, 30)}...`);
                            }
                        }
                    });
                    
                    console.log(`期数字段总数: ${installmentFound}, 已优化: ${optimizedCount}`);
                    
                    if (optimizedCount > 0) {
                        console.log('🎉 期数字段优化已成功生效！');
                    } else {
                        console.log('⚠️ 期数字段优化未生效，可能需要重启应用');
                    }
                }
            });
        } else {
            console.log('❌ 未找到客户表格，可能需要切换到客户订单标签页');
        }
        
        console.log('=== 期数字段调试结束 ===');
    }, 500); // 减少延迟时间
});
</script>

<!-- 简化JavaScript引用，减少性能开销 -->
<script src="{{ url_for('static', filename='js/controllers/data-query-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/app.js') }}"></script>

<!-- 企业级智能分页系统 -->
<script>
// 智能分页折叠管理器 - Dashboard专用版本
const DashboardPaginationManager = {
    // 初始化智能分页
    init: function() {
        console.log('初始化Dashboard智能分页系统');
        
        // 监听窗口大小变化
        this.setupResizeListener();
        
        // 监听表格重绘事件
        this.setupTableDrawCallback();
        
        // 初始执行一次优化
        setTimeout(() => {
            this.optimizeAllPagination();
        }, 300);
    },
    
    // 设置窗口大小变化监听器
    setupResizeListener: function() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.optimizeAllPagination();
            }, 150);
        });
    },
    
    // 设置表格重绘回调
    setupTableDrawCallback: function() {
        // 监听DataTables的draw事件
        $(document).on('draw.dt', '.data-table', (e) => {
            const table = e.target;
            setTimeout(() => {
                this.optimizeSingleTable(table);
            }, 50);
        });
    },
    
    // 优化所有表格的分页
    optimizeAllPagination: function() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            if ($.fn.DataTable.isDataTable(table)) {
                this.optimizeSingleTable(table);
            }
        });
    },
    
    // 优化单个表格的分页
    optimizeSingleTable: function(tableElement) {
        if (!$.fn.DataTable.isDataTable(tableElement)) return;
        
        const api = $(tableElement).DataTable();
        const info = api.page.info();
        
        // 如果只有一页，不需要分页优化
        if (info.pages <= 1) return;
        
        const wrapper = $(tableElement).closest('.dataTables_wrapper');
        const paginateContainer = wrapper.find('.dataTables_paginate');
        
        if (paginateContainer.length === 0) return;
        
        // 检测设备类型
        const isMobile = window.innerWidth <= 767;
        const isExtraSmall = window.innerWidth <= 480;
        
        if (isMobile) {
            this.applyMobileOptimization(paginateContainer, info, isExtraSmall);
        } else {
            this.applyDesktopOptimization(paginateContainer);
        }
    },
    
    // 应用移动端优化
    applyMobileOptimization: function(paginateContainer, pageInfo, isExtraSmall) {
        paginateContainer.addClass('mobile-pagination-active');
        
        if (isExtraSmall) {
            // 超小屏幕：只显示前后页按钮和页码指示器
            this.createCompactPagination(paginateContainer, pageInfo);
        } else {
            // 普通移动设备：智能隐藏页码按钮
            this.applySmartHiding(paginateContainer, pageInfo);
        }
        
        // 添加触摸滑动支持
        this.addTouchSupport(paginateContainer, pageInfo);
    },
    
    // 应用桌面端优化
    applyDesktopOptimization: function(paginateContainer) {
        paginateContainer.removeClass('mobile-pagination-active');
        
        // 移除移动端特有的类和元素
        paginateContainer.find('.paginate_button').removeClass('mobile-hidden');
        paginateContainer.find('.current-page-indicator').remove();
        paginateContainer.find('.ellipsis-indicator').remove();
    },
    
    // 创建紧凑分页（超小屏幕）
    createCompactPagination: function(paginateContainer, pageInfo) {
        // 隐藏所有页码按钮，只保留前后页按钮
        paginateContainer.find('.paginate_button:not(.previous):not(.next)').addClass('mobile-hidden');
        
        // 移除现有的页码指示器
        paginateContainer.find('.current-page-indicator').remove();
        
        // 创建页码指示器
        const pageIndicator = $('<span class="current-page-indicator">').html(
            `<i class="bi bi-layers"></i> ${pageInfo.page + 1} / ${pageInfo.pages}`
        );
        
        // 将页码指示器插入到前后页按钮之间
        const nextButton = paginateContainer.find('.paginate_button.next');
        if (nextButton.length > 0) {
            nextButton.before(pageIndicator);
        } else {
            paginateContainer.append(pageIndicator);
        }
    },
    
    // 应用智能隐藏（普通移动设备）
    applySmartHiding: function(paginateContainer, pageInfo) {
        const currentPage = pageInfo.page + 1; // DataTables页码从0开始
        const totalPages = pageInfo.pages;
        
        // 移除现有的指示器
        paginateContainer.find('.ellipsis-indicator').remove();
        
        // 智能显示页码按钮
        paginateContainer.find('.paginate_button:not(.previous):not(.next)').each(function() {
            const $button = $(this);
            const pageNum = parseInt($button.text());
            
            if (isNaN(pageNum)) return;
            
            // 显示逻辑：当前页 ± 2，首页，末页
            const shouldShow = (
                pageNum === 1 || 
                pageNum === totalPages || 
                Math.abs(pageNum - currentPage) <= 1
            );
            
            if (shouldShow) {
                $button.removeClass('mobile-hidden');
            } else {
                $button.addClass('mobile-hidden');
            }
        });
        
        // 添加省略号
        this.addEllipsis(paginateContainer, currentPage, totalPages);
    },
    
    // 添加省略号指示器
    addEllipsis: function(paginateContainer, currentPage, totalPages) {
        if (totalPages <= 5) return; // 页数少时不需要省略号
        
        const visibleButtons = paginateContainer.find('.paginate_button:not(.previous):not(.next):not(.mobile-hidden)');
        
        // 左侧省略号
        if (currentPage > 3) {
            const firstButton = visibleButtons.filter(':first');
            const secondButton = visibleButtons.filter(':eq(1)');
            
            if (firstButton.length && secondButton.length) {
                const firstPageNum = parseInt(firstButton.text());
                const secondPageNum = parseInt(secondButton.text());
                
                if (secondPageNum - firstPageNum > 1) {
                    const leftEllipsis = $('<span class="ellipsis-indicator">⋯</span>');
                    secondButton.before(leftEllipsis);
                }
            }
        }
        
        // 右侧省略号
        if (currentPage < totalPages - 2) {
            const lastButton = visibleButtons.filter(':last');
            const secondLastButton = visibleButtons.filter(':eq(-2)');
            
            if (lastButton.length && secondLastButton.length) {
                const lastPageNum = parseInt(lastButton.text());
                const secondLastPageNum = parseInt(secondLastButton.text());
                
                if (lastPageNum - secondLastPageNum > 1) {
                    const rightEllipsis = $('<span class="ellipsis-indicator">⋯</span>');
                    lastButton.before(rightEllipsis);
                }
            }
        }
    },
    
    // 添加触摸滑动支持
    addTouchSupport: function(paginateContainer, pageInfo) {
        const container = paginateContainer[0];
        if (!container || container.touchSupportAdded) return;
        
        let touchStartX = 0;
        let touchEndX = 0;
        
        container.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
        }, { passive: true });
        
        container.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].clientX;
            this.handleSwipe(paginateContainer, touchStartX, touchEndX, pageInfo);
        }, { passive: true });
        
        container.touchSupportAdded = true;
    },
    
    // 处理滑动手势
    handleSwipe: function(paginateContainer, startX, endX, pageInfo) {
        const minSwipeDistance = 80;
        const swipeDistance = endX - startX;
        
        if (Math.abs(swipeDistance) >= minSwipeDistance) {
            const tableElement = paginateContainer.closest('.dataTables_wrapper').find('.data-table')[0];
            if (!$.fn.DataTable.isDataTable(tableElement)) return;
            
            const api = $(tableElement).DataTable();
            
            if (swipeDistance > 0 && pageInfo.page > 0) {
                // 向右滑动：上一页
                api.page('previous').draw('page');
            } else if (swipeDistance < 0 && pageInfo.page < pageInfo.pages - 1) {
                // 向左滑动：下一页
                api.page('next').draw('page');
            }
        }
    }
};

// 当DOM加载完成后初始化智能分页系统
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保DataTables已完全加载
    setTimeout(() => {
        DashboardPaginationManager.init();
    }, 500);
});
</script>

<!-- 暂时注释掉可能引起性能问题的脚本 -->
<!-- <script src="{{ url_for('static', filename='js/controllers/style-controller.js') }}"></script> -->
<!-- <script src="{{ url_for('static', filename='js/controllers/loading-controller.js') }}"></script> -->
<!-- <script src="{{ url_for('static', filename='js/controllers/dashboard-adapter.js') }}"></script> -->
{% endblock %}