from flask import (
    Blueprint, render_template, request, redirect, url_for, flash, jsonify,
    session
)
from flask_login import login_user, logout_user, login_required

from app.models.user import User
from app.services.captcha_service import CaptchaService
from config import Config

# 创建认证蓝图
auth = Blueprint('auth', __name__)


@auth.route('/login', methods=['GET'])
def login():
    """用户登录页面 - 只处理GET请求显示登录页面"""
    # 生成验证码
    captcha_image = CaptchaService.generate_and_save()
    
    # 渲染登录页面
    return render_template(
        'login.html', 
        version=Config.VERSION, 
        captcha_image=captcha_image
    )


@auth.route('/login', methods=['POST'])
def login_form():
    """表单登录处理 - 处理传统表单POST请求"""
    password = request.form.get('password')
    captcha = request.form.get('captcha')
    
    # 验证验证码
    if not CaptchaService.validate(captcha):
        flash('验证码错误，请重试。', 'error')
        return render_template(
            'login.html', 
            version=Config.VERSION, 
            captcha_image=CaptchaService.generate_and_save()
        )
    
    # 获取用户级别
    user_level = Config.USER_LEVELS.get(password)
    
    if user_level:
        # 创建用户实例
        user = User(password, user_level)
        
        # 将会话标记为永久性，确保会话超时设置生效
        session.permanent = True
        
        # 设置会话超时时间为8小时
        session.permanent_lifetime = 28800
        
        # 记录用户登录状态
        login_user(user, remember=True)
        
        # 获取下一个链接（如果有）
        next_page = request.args.get('next')
        # 重定向到工作台页面或请求的页面
        return redirect(next_page or url_for('main.homepage'))
    else:
        flash('密码错误，请重试。', 'error')
        
    # 生成验证码
    captcha_image = CaptchaService.generate_and_save()
    
    # 渲染登录页面
    return render_template(
        'login.html', 
        version=Config.VERSION, 
        captcha_image=captcha_image
    )


@auth.route('/refresh-captcha', methods=['GET'])
def refresh_captcha():
    """刷新验证码"""
    captcha_image = CaptchaService.generate_and_save()
    return jsonify({'captcha_image': captcha_image})


@auth.route('/captcha', methods=['GET'])
def get_captcha():
    """获取验证码 - 为前端Vue应用提供API接口"""
    captcha_image = CaptchaService.generate_and_save()
    return jsonify({
        'captcha_url': captcha_image,
        'captcha_key': 'session_based'
    })


@auth.route('/api/login', methods=['POST'])
def api_login():
    """API登录接口 - 处理JSON格式的登录请求"""
    # 检查请求内容类型
    if request.is_json:
        data = request.get_json()
        password = data.get('password')
        captcha = data.get('captcha')
        username = data.get('username')  # 前端可能发送用户名
    else:
        # 兼容表单数据
        password = request.form.get('password')
        captcha = request.form.get('captcha')
        username = request.form.get('username')
    
    # 验证验证码
    session_captcha = session.get('captcha_code')
    print(f"Debug - 用户输入: '{captcha}', Session: '{session_captcha}'")
    if not CaptchaService.validate(captcha):
        print("Debug - 验证码验证失败")
        return jsonify({
            'code': 400,
            'message': '验证码错误，请重试',
            'success': False
        }), 400
    
    # 获取用户级别
    user_level = Config.USER_LEVELS.get(password)
    
    if user_level:
        # 创建用户实例
        user = User(password, user_level)
        
        # 设置会话
        session.permanent = True
        session.permanent_lifetime = 28800  # 8小时
        
        # 记录用户登录状态
        login_user(user, remember=True)
        
        # 返回成功响应
        return jsonify({
            'code': 200,
            'message': '登录成功',
            'success': True,
            'token': f'session_{user.id}',  # 基于session的token
            'user': {
                'username': username or 'user',
                'permission_level': user_level,
                'id': user.id
            }
        })
    else:
        return jsonify({
            'code': 401,
            'message': '密码错误，请重试',
            'success': False
        }), 401


@auth.route('/logout')
@login_required
def logout():
    """用户注销处理"""
    logout_user()
    flash('您已成功退出登录。', 'info')
    return redirect(url_for('auth.login'))