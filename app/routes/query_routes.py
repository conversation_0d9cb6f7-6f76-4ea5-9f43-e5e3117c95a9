"""
查询相关路由模块
"""
from flask import Blueprint, request, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
import datetime
import logging

from app.services.data_service import (
    get_filtered_data, 
    get_overdue_orders, 
    get_orders_by_customer,
    get_customer_summary
)
from app.services.route_handler import <PERSON><PERSON><PERSON><PERSON>, DataValidator
from config import Config

logger = logging.getLogger(__name__)

# 创建查询蓝图
query_bp = Blueprint('query', __name__)


@query_bp.route('/filter', methods=['GET', 'POST'])
@login_required
def filter_data():
    """日期筛选视图"""
    if request.method == 'POST':
        date = request.form.get('date')
        logger.info(f"收到筛选请求，日期: {date}")
        
        # 使用路由处理器处理请求
        handler = RouteHandler()
        return handler._handle_filter_request(date)
    
    return redirect(url_for('main.index'))


@query_bp.route('/overdue')
@login_required
def overdue_orders():
    """逾期订单视图 - 支持本地分页和强制刷新"""
    if current_user.has_permission('standard'):
        logger.info("收到逾期订单查询请求")
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        force_refresh = request.args.get('refresh', '0') == '1'
        search_query = request.args.get('search', '')
        
        logger.info(f"请求参数: 页码={page}, 每页数量={limit}, "
                   f"强制刷新={force_refresh}, 搜索关键词={search_query}")
        
        # 获取逾期订单数据
        results = get_overdue_orders(
            page=page, 
            limit=limit, 
            force_refresh=force_refresh, 
            search_query=search_query
        )
        
        # 使用路由处理器标准化数据格式
        handler = RouteHandler()
        standardized_results = handler._standardize_data_format(results)
        
        # 提取缓存和分页信息
        cache_info = results.get('cache_info', {})
        pagination = results.get('pagination', {})
        
        # 格式化时间信息
        last_update = cache_info.get('last_update')
        next_update = cache_info.get('next_update')
        
        last_update_str = (last_update.strftime('%Y-%m-%d %H:%M:%S') 
                          if last_update else "未知")
        next_update_str = (next_update.strftime('%Y-%m-%d %H:%M:%S') 
                          if next_update else "未知")
        
        # 分页信息
        current_page = pagination.get('page', page)
        total_pages = pagination.get('pages', 1)
        total_records = pagination.get('total', len(results.get('results', [])))
        page_limit = pagination.get('limit', limit)
        
        return render_template(
            'overdue_orders.html', 
            user=current_user,
            overdue_results=standardized_results,
            current_page=current_page,
            total_pages=total_pages,
            total_records=total_records,
            page_limit=page_limit,
            last_update=last_update_str,
            next_update=next_update_str,
            today=datetime.date.today().strftime('%Y-%m-%d'),
            version=Config.VERSION,
            force_refresh=force_refresh,
            search_query=search_query
        )
    else:
        flash('您没有权限访问此页面。', 'error')
        return redirect(url_for('main.index'))


@query_bp.route('/customer/<customer_name>')
@login_required
def customer_orders(customer_name):
    """客户订单视图"""
    logger.info(f"收到客户订单查询请求，客户: {customer_name}")
    
    # 使用路由处理器处理请求
    handler = RouteHandler()
    return handler._handle_customer_request(customer_name)


@query_bp.route('/customer_summary/<customer_name>')
@login_required
def customer_summary(customer_name):
    """客户订单汇总视图"""
    if current_user.has_permission('standard'):
        logger.info(f"收到客户汇总查询请求，客户: {customer_name}")
        
        # 获取客户汇总数据
        summary_data = get_customer_summary(customer_name)
        
        # 生成图表数据
        chart_data = {}
        
        if 'error' in summary_data:
            logger.error(f"客户汇总数据获取错误: {summary_data['error']}")
            flash(f"数据获取失败: {summary_data['error']}", "error")
        else:
            logger.info("客户汇总数据获取成功")
            
            # 生成企业级图表数据
            try:
                from app.services.chart_service import prepare_enterprise_customer_summary_chart_data
                chart_data = prepare_enterprise_customer_summary_chart_data(summary_data)
                logger.info("使用企业级图表数据服务生成真实数据")
            except ImportError:
                logger.warning("图表服务未找到，跳过图表数据生成")
                chart_data = {}
        
        return render_template(
            'customer_summary_enterprise.html',
            user=current_user,
            customer_name=customer_name,
            summary_data=summary_data,
            chart_data=chart_data,
            today=datetime.date.today().strftime('%Y-%m-%d'),
            version=Config.VERSION
        )
    else:
        flash('您没有权限访问此页面。', 'error')
        return redirect(url_for('main.index')) 