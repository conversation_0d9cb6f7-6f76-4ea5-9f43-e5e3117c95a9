"""
异步路由模块 - 处理异步任务的API端点
"""
from flask import Blueprint, request, jsonify
from app.services.async_task_handler import task_handler
from app.services.smart_cache import query_cache
from app.utils.performance_monitor import (
    performance_monitor, 
    get_performance_dashboard
)
import logging

logger = logging.getLogger(__name__)

async_bp = Blueprint('async', __name__, url_prefix='/api/async')


@async_bp.route('/tasks', methods=['GET'])
def get_all_tasks():
    """获取所有异步任务状态"""
    try:
        tasks = task_handler.get_all_tasks()
        return jsonify({
            'success': True,
            'data': tasks,
            'total': len(tasks)
        })
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取任务列表失败: {str(e)}'
        }), 500


@async_bp.route('/tasks/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取特定任务状态"""
    try:
        task_status = task_handler.get_task_status(task_id)
        
        if task_status is None:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': task_status
        })
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取任务状态失败: {str(e)}'
        }), 500


@async_bp.route('/tasks/<task_id>/result', methods=['GET'])
def get_task_result(task_id):
    """获取任务结果"""
    try:
        result = task_handler.get_task_result(task_id)
        
        if result is None:
            task_status = task_handler.get_task_status(task_id)
            if task_status is None:
                return jsonify({
                    'success': False,
                    'message': '任务不存在'
                }), 404
            elif task_status['status'] in ['pending', 'running']:
                return jsonify({
                    'success': False,
                    'message': '任务还在进行中',
                    'status': task_status['status']
                }), 202
            else:
                return jsonify({
                    'success': False,
                    'message': '任务执行失败',
                    'error': task_status.get('error')
                }), 500
        
        # 处理文件下载结果
        if isinstance(result, dict) and 'file_data' in result:
            from flask import send_file
            import io
            
            file_data = result['file_data']
            filename = result.get('filename', 'export.xlsx')
            mimetype = result.get('mimetype', 'application/octet-stream')
            
            return send_file(
                io.BytesIO(file_data),
                as_attachment=True,
                download_name=filename,
                mimetype=mimetype
            )
        
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        logger.error(f"获取任务结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取任务结果失败: {str(e)}'
        }), 500


@async_bp.route('/tasks/<task_id>/cancel', methods=['POST'])
def cancel_task(task_id):
    """取消任务"""
    try:
        success = task_handler.cancel_task(task_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '任务已取消'
            })
        else:
            return jsonify({
                'success': False,
                'message': '无法取消任务（任务不存在或已完成）'
            }), 400
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'取消任务失败: {str(e)}'
        }), 500


@async_bp.route('/export/async', methods=['POST'])
def async_export():
    """异步导出数据"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        export_data = data.get('data', [])
        export_type = data.get('type', 'excel')
        search_query = data.get('search_query', '')
        
        if not export_data:
            return jsonify({
                'success': False,
                'message': '导出数据不能为空'
            }), 400
        
        # 提交异步导出任务
        from app.services.async_task_handler import async_export_data
        task_id = async_export_data(export_data, export_type, search_query)
        
        return jsonify({
            'success': True,
            'message': '导出任务已提交',
            'task_id': task_id
        })
    except Exception as e:
        logger.error(f"异步导出失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'异步导出失败: {str(e)}'
        }), 500


@async_bp.route('/cache/stats', methods=['GET'])
def get_cache_stats():
    """获取缓存统计信息"""
    try:
        from app.services.smart_cache import smart_cache
        stats = smart_cache.get_stats()
        
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取缓存统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取缓存统计失败: {str(e)}'
        }), 500


@async_bp.route('/cache/clear', methods=['POST'])
def clear_cache():
    """清空缓存"""
    try:
        query_cache.invalidate_data_cache()
        
        return jsonify({
            'success': True,
            'message': '缓存已清空'
        })
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'清空缓存失败: {str(e)}'
        }), 500


@async_bp.route('/performance/dashboard', methods=['GET'])
def performance_dashboard():
    """获取性能仪表板数据"""
    try:
        dashboard_data = get_performance_dashboard()
        
        return jsonify({
            'success': True,
            'data': dashboard_data
        })
    except Exception as e:
        logger.error(f"获取性能数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取性能数据失败: {str(e)}'
        }), 500


@async_bp.route('/performance/metrics', methods=['GET'])
def get_performance_metrics():
    """获取性能指标"""
    try:
        endpoint = request.args.get('endpoint')
        time_window = int(request.args.get('time_window', 3600))
        
        api_stats = performance_monitor.get_api_stats(endpoint, time_window)
        system_stats = performance_monitor.get_system_stats()
        error_summary = performance_monitor.get_error_summary(time_window)
        
        return jsonify({
            'success': True,
            'data': {
                'api_stats': api_stats,
                'system_stats': system_stats,
                'error_summary': error_summary
            }
        })
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取性能指标失败: {str(e)}'
        }), 500 