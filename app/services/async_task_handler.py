"""
异步任务处理器 - 处理耗时的文件生成和数据导出任务
"""
import threading
from typing import Dict, Any, Callable, Optional
from concurrent.futures import ThreadPoolExecutor
import logging
import uuid
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class TaskStatus:
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class AsyncTaskHandler:
    """异步任务处理器"""
    
    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.cleanup_interval = 3600  # 1小时清理一次完成的任务
        self._start_cleanup_thread()
    
    def submit_task(self, 
                   task_func: Callable, 
                   task_args: tuple = (), 
                   task_kwargs: dict = None,
                   task_name: str = "未命名任务") -> str:
        """提交异步任务"""
        if task_kwargs is None:
            task_kwargs = {}
        
        task_id = str(uuid.uuid4())
        
        # 记录任务信息
        self.tasks[task_id] = {
            'id': task_id,
            'name': task_name,
            'status': TaskStatus.PENDING,
            'created_at': datetime.now(),
            'started_at': None,
            'completed_at': None,
            'result': None,
            'error': None,
            'progress': 0
        }
        
        # 提交任务
        future = self.executor.submit(
            self._execute_task, task_id, task_func, task_args, task_kwargs
        )
        self.tasks[task_id]['future'] = future
        
        logger.info(f"提交异步任务: {task_name} (ID: {task_id})")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        # 移除future对象，避免序列化问题
        result = task.copy()
        result.pop('future', None)
        
        # 处理结果中的bytes数据，避免JSON序列化问题
        if result.get('result') and isinstance(result['result'], dict):
            task_result = result['result'].copy()
            if 'file_data' in task_result and isinstance(task_result['file_data'], bytes):
                # 将bytes数据转换为可序列化的信息
                task_result['file_size'] = len(task_result['file_data'])
                task_result['file_ready'] = True
                task_result.pop('file_data', None)  # 移除bytes数据
            result['result'] = task_result
        
        return result
    
    def get_task_result(self, task_id: str) -> Optional[Any]:
        """获取任务结果"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        if task['status'] == TaskStatus.COMPLETED:
            return task['result']
        elif task['status'] == TaskStatus.FAILED:
            raise Exception(task['error'])
        else:
            return None  # 任务还在进行中
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        future = task.get('future')
        if future and not future.done():
            cancelled = future.cancel()
            if cancelled:
                task['status'] = TaskStatus.FAILED
                task['error'] = "任务已被取消"
                logger.info(f"任务已取消: {task_id}")
            return cancelled
        
        return False
    
    def _execute_task(self, task_id: str, task_func: Callable, 
                     args: tuple, kwargs: dict):
        """执行任务的内部方法"""
        task = self.tasks[task_id]
        
        try:
            # 更新任务状态
            task['status'] = TaskStatus.RUNNING
            task['started_at'] = datetime.now()
            
            logger.info(f"开始执行任务: {task['name']} (ID: {task_id})")
            
            # 执行任务
            result = task_func(*args, **kwargs)
            
            # 任务完成
            task['status'] = TaskStatus.COMPLETED
            task['completed_at'] = datetime.now()
            task['result'] = result
            task['progress'] = 100
            
            logger.info(f"任务执行完成: {task['name']} (ID: {task_id})")
            return result
            
        except Exception as e:
            # 任务失败
            task['status'] = TaskStatus.FAILED
            task['completed_at'] = datetime.now()
            task['error'] = str(e)
            
            logger.error(f"任务执行失败: {task['name']} (ID: {task_id}), "
                        f"错误: {str(e)}")
            raise
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    self._cleanup_old_tasks()
                    threading.Event().wait(self.cleanup_interval)
                except Exception as e:
                    logger.error(f"清理任务时出错: {str(e)}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_old_tasks(self):
        """清理旧任务"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        tasks_to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task['status'] in [TaskStatus.COMPLETED, TaskStatus.FAILED] and 
                task.get('completed_at', datetime.now()) < cutoff_time):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            logger.info(f"清理旧任务: {task_id}")
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        result = {}
        for task_id, task in self.tasks.items():
            task_copy = task.copy()
            task_copy.pop('future', None)  # 移除不可序列化的对象
            
            # 处理结果中的bytes数据，避免JSON序列化问题
            if task_copy.get('result') and isinstance(task_copy['result'], dict):
                task_result = task_copy['result'].copy()
                if 'file_data' in task_result and isinstance(task_result['file_data'], bytes):
                    # 将bytes数据转换为可序列化的信息
                    task_result['file_size'] = len(task_result['file_data'])
                    task_result['file_ready'] = True
                    task_result.pop('file_data', None)  # 移除bytes数据
                task_copy['result'] = task_result
            
            result[task_id] = task_copy
        return result


# 全局任务处理器实例
task_handler = AsyncTaskHandler()


def async_export_data(data: list, export_type: str, 
                     search_query: str = "") -> str:
    """异步导出数据"""
    import pandas as pd
    import io
    from datetime import datetime
    
    def export_task():
        # 处理数据
        df = pd.DataFrame(data)
        output = io.BytesIO()
        
        if export_type == 'excel':
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='数据导出')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"数据导出_{timestamp}.xlsx"
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        else:  # csv
            df.to_csv(output, index=False, encoding='utf-8-sig')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"数据导出_{timestamp}.csv"
            mimetype = 'text/csv'
        
        output.seek(0)
        return {
            'file_data': output.getvalue(),
            'filename': filename,
            'mimetype': mimetype
        }
    
    task_name = f"导出{export_type.upper()}数据 ({len(data)}条记录)"
    return task_handler.submit_task(export_task, task_name=task_name)


def async_generate_contracts(excel_data: bytes, contract_type: str, 
                           version: str) -> str:
    """异步生成合同"""
    def contract_task():
        # 这里应该调用实际的合同生成逻辑
        # 为了演示，我们模拟一个耗时操作
        import time
        time.sleep(2)  # 模拟处理时间
        
        return {
            'files': ['contract1.docx', 'contract2.docx'],
            'message': f'成功生成{contract_type}合同'
        }
    
    task_name = f"生成{contract_type}合同"
    return task_handler.submit_task(contract_task, task_name=task_name) 