import requests
import logging
from flask import current_app
import json
import hashlib
from app import cache

logger = logging.getLogger(__name__)

# 备用API路径配置
FALLBACK_ENDPOINTS = {
    # 基础路径: 备用路径的映射
    'query': 'filter_data_db',
    'overdue': 'filter_overdue_orders_db',
    'customer': 'filter_orders_by_customer_name_db',
    'summary': 'summary_data_db'
}

# 直接尝试的端点列表
DIRECT_ENDPOINTS = [
    '',                                 # 空路径 - 可能使用参数区分
    'filter_data_db',                   # 日期筛选
    'filter_overdue_orders_db',         # 逾期订单
    'filter_orders_by_customer_name_db' # 客户订单
]

class ApiClient:
    """API客户端类，用于处理与后端API的通信"""
    
    def __init__(self):
        """初始化API客户端"""
        self.logger = logging.getLogger(__name__)
    
    def get_filter_data(self, start_date, end_date):
        """
        获取筛选数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            筛选数据结果
        """
        self.logger.info(f"获取筛选数据: start_date={start_date}, end_date={end_date}")
        params = {}
        
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        
        # 添加当前时间戳，防止缓存
        params['_t'] = hashlib.md5(str(current_app.config['API_KEY']).encode()).hexdigest()
        
        return make_api_request('GET', 'filter_data_db', params)
    
    def get_customer_data(self, customer_name):
        """
        获取客户数据
        
        Args:
            customer_name: 客户姓名
            
        Returns:
            客户数据结果
        """
        self.logger.info(f"获取客户数据: customer_name={customer_name}")
        params = {'customer_name': customer_name}
        
        # 添加当前时间戳，防止缓存
        params['_t'] = hashlib.md5(str(current_app.config['API_KEY']).encode()).hexdigest()
        
        return make_api_request('GET', 'filter_orders_by_customer_name_db', params)
    
    def get_overdue_data(self):
        """
        获取逾期数据
        
        Returns:
            逾期数据结果
        """
        self.logger.info("获取逾期数据")
        params = {'api_key': current_app.config.get('API_KEY')}
        
        return make_api_request('GET', 'filter_overdue_orders_db', params)
    
    def check_status(self):
        """
        检查API状态
        
        Returns:
            API状态信息
        """
        self.logger.info("检查API状态")
        try:
            # 直接检查本地ping端点，不需要外部API
            import requests
            from flask import current_app
            
            # 获取当前应用的基础URL
            with current_app.app_context():
                base_url = current_app.config.get('SERVER_NAME') or 'localhost:5000'
                if not base_url.startswith('http'):
                    base_url = f'http://{base_url}'
                
                ping_url = f'{base_url}/api/ping'
                
                response = requests.get(ping_url, timeout=5)
                response.raise_for_status()
                
                data = response.json()
                if data.get('status') == 'ok':
                    return {'status': 'online', 'message': 'API服务正常'}
                else:
                    return {'status': 'offline', 'message': 'API服务异常'}
        except Exception as e:
            self.logger.error(f"检查API状态失败: {str(e)}")
            return {'status': 'offline', 'message': f'无法连接API服务: {str(e)}'}

def generate_cache_key(method, endpoint, params, data):
    """
    根据请求内容生成唯一的缓存键
    
    Args:
        method: 请求方法
        endpoint: API端点
        params: 请求参数
        data: 请求数据
        
    Returns:
        str: 唯一的缓存键
    """
    # 创建包含所有请求信息的字符串
    key_parts = [
        method.upper(),
        endpoint,
        str(sorted(params.items())) if params else "",
        str(data) if data else ""
    ]
    key_str = "|".join(key_parts)
    
    # 使用MD5生成固定长度的缓存键
    return hashlib.md5(key_str.encode()).hexdigest()

def ensure_db_endpoint(endpoint):
    """
    确保API端点名称带有_db后缀（如果需要）
    
    Args:
        endpoint: 原始API端点名称
        
    Returns:
        str: 添加了_db后缀的端点名称（如果需要）
    """
    # 如果已经有_db后缀或是特殊端点则不做修改
    if endpoint.endswith('_db') or endpoint in ['', 'ping', 'version', 'status']:
        return endpoint
        
    # 将旧端点映射到新端点
    endpoint_mapping = {
        'filter_data': 'filter_data_db',
        'filter_overdue_orders': 'filter_overdue_orders_db',
        'filter_orders_by_customer_name': 'filter_orders_by_customer_name_db',
        'customer_summary': 'customer_summary_db',
        'summary_data': 'summary_data_db',
        'order_summary': 'order_summary_db',
        'get_order_details': 'get_order_details_db',
        'delete_order': 'delete_order_db',
        'overdue_summary': 'overdue_summary_db'
    }
    
    # 如果是已知的端点，使用映射
    if endpoint in endpoint_mapping:
        logger.info(f"将端点 {endpoint} 映射到 {endpoint_mapping[endpoint]}")
        return endpoint_mapping[endpoint]
        
    # 对于未知端点，直接添加_db后缀
    return f"{endpoint}_db"

@cache.memoize(timeout=300)  # 缓存5分钟
def make_api_request(method, endpoint, params=None, data=None, use_fallback=False, url_index=0, direct_endpoint_index=0):
    """
    发送API请求到原始API服务器，带缓存功能
    
    Args:
        method: 请求方法 ('GET', 'POST' 等)
        endpoint: API端点路径
        params: URL参数字典
        data: POST数据
        use_fallback: 是否使用备用路径
        url_index: 当前尝试的URL索引
        direct_endpoint_index: 直接尝试的端点索引
    
    Returns:
        响应数据的字典或列表
    """
    try:
        # 确保端点格式正确（添加_db后缀）
        endpoint = ensure_db_endpoint(endpoint)
        
        # 获取当前应该使用的基础URL
        if url_index == 0:
            base_url = current_app.config['API_BASE_URL']
        else:
            # 尝试从备用URL列表获取
            fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
            if url_index - 1 < len(fallback_urls):
                base_url = fallback_urls[url_index - 1]
            else:
                logger.error(f"已尝试所有可用的API基础URL，仍无法连接")
                return {"error": "无法连接到任何可用的API服务器"}
        
        # 决定使用哪个端点路径
        if use_fallback and endpoint in FALLBACK_ENDPOINTS:
            actual_endpoint = FALLBACK_ENDPOINTS[endpoint]
            logger.info(f"使用备用端点: {actual_endpoint} 代替 {endpoint}")
        elif direct_endpoint_index > 0 and direct_endpoint_index <= len(DIRECT_ENDPOINTS):
            # 尝试直接端点列表中的端点
            actual_endpoint = DIRECT_ENDPOINTS[direct_endpoint_index - 1]
            logger.info(f"尝试直接端点: {actual_endpoint} (尝试 #{direct_endpoint_index})")
        else:
            actual_endpoint = endpoint
            
        url = f"{base_url}/{actual_endpoint.lstrip('/')}"
        
        # 确保API密钥始终包含在请求中
        if params is None:
            params = {}
        
        # 使用配置中的API密钥
        api_key = current_app.config.get('API_KEY', 'lxw8025031')
        params['api_key'] = api_key
        
        # 记录请求信息，便于调试
        logger.info(f"[URL#{url_index}] 发送 {method} 请求到: {url}")
        logger.info(f"请求参数: {params}")
        if data:
            logger.info(f"请求数据: {data}")
        
        # 显式禁用代理，避免代理连接问题（按照API端建议）
        proxies = {'http': None, 'https': None}
        
        # 发出请求
        if method.upper() == 'GET':
            response = requests.get(url, params=params, timeout=30, proxies=proxies)
        elif method.upper() == 'POST':
            response = requests.post(url, params=params, json=data, timeout=30, proxies=proxies)
        else:
            raise ValueError(f"不支持的请求方法: {method}")
        
        # 记录响应状态
        logger.info(f"[URL#{url_index}] 响应状态码: {response.status_code}")
        logger.info(f"[URL#{url_index}] 响应头: {response.headers}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 尝试解析响应
        try:
            resp_data = response.json()
            # 记录响应内容
            resp_str = str(resp_data)
            if len(resp_str) > 200:
                logger.info(f"响应数据: {resp_str[:200]}...")
            else:
                logger.info(f"响应数据: {resp_str}")
            
            # 检查响应数据结构
            if isinstance(resp_data, dict):
                logger.info(f"响应数据键: {list(resp_data.keys())}")
                if 'error' in resp_data:
                    logger.error(f"API返回错误: {resp_data['error']}")
                elif 'results' in resp_data:
                    logger.info(f"响应结果数量: {len(resp_data['results'])}")
                    if resp_data['results']:
                        first_item = resp_data['results'][0]
                        logger.info(f"第一条结果示例: {first_item}")
                        # 检查是否包含新字段
                        if isinstance(first_item, dict) and ('devices_count' in first_item or '台数' in first_item):
                            logger.info(f"检测到台数字段: devices_count={first_item.get('devices_count')} 或 台数={first_item.get('台数')}")
            elif isinstance(resp_data, list):
                logger.info(f"响应数据列表长度: {len(resp_data)}")
                if resp_data:
                    first_item = resp_data[0]
                    logger.info(f"第一条数据示例: {first_item}")
                    # 检查是否包含新字段
                    if isinstance(first_item, dict) and ('devices_count' in first_item or '台数' in first_item):
                        logger.info(f"检测到台数字段: devices_count={first_item.get('devices_count')} 或 台数={first_item.get('台数')}")
            
            return resp_data
        except json.JSONDecodeError:
            # 如果响应不是JSON格式，记录原始内容
            content = response.text
            logger.error(f"无法解析JSON响应: {content[:200]}...")
            return {"error": "服务器返回了无法解析的响应"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] API请求失败，错误: {str(e)}")
        return {"error": f"API请求失败: {str(e)}"}
    
    except Exception as e:
        logger.error(f"[URL#{url_index}] 未知错误: {str(e)}")
        return {"error": f"未知错误: {str(e)}"}
    
    except requests.exceptions.Timeout as e:
        logger.error(f"[URL#{url_index}] API请求超时，错误: {str(e)}")
        
        # 尝试下一个URL
        next_url_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_url_index - 1 < len(fallback_urls) or next_url_index == 1:
            logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return make_api_request(method, endpoint, params, data, use_fallback, next_url_index, direct_endpoint_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return make_api_request(method, endpoint, params, data, True, 0, 0)