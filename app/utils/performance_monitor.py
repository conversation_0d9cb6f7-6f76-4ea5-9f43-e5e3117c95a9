"""
性能监控系统 - 监控API响应时间、内存使用和系统性能
"""
import time
import psutil
import threading
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from functools import wraps
from collections import deque, defaultdict

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.api_metrics = defaultdict(lambda: deque(maxlen=max_records))
        self.system_metrics = deque(maxlen=max_records)
        self.error_metrics = deque(maxlen=max_records)
        self.lock = threading.RLock()
        self._start_system_monitor()
    
    def record_api_call(self, endpoint: str, method: str, 
                       response_time: float, status_code: int,
                       request_size: int = 0, response_size: int = 0):
        """记录API调用指标"""
        with self.lock:
            metric = {
                'timestamp': datetime.now(),
                'endpoint': endpoint,
                'method': method,
                'response_time': response_time,
                'status_code': status_code,
                'request_size': request_size,
                'response_size': response_size
            }
            
            key = f"{method}:{endpoint}"
            self.api_metrics[key].append(metric)
            
            # 记录错误
            if status_code >= 400:
                self.error_metrics.append(metric)
    
    def record_error(self, endpoint: str, error_type: str, 
                    error_message: str):
        """记录错误信息"""
        with self.lock:
            error_metric = {
                'timestamp': datetime.now(),
                'endpoint': endpoint,
                'error_type': error_type,
                'error_message': error_message
            }
            self.error_metrics.append(error_metric)
    
    def get_api_stats(self, endpoint: str = None, 
                     time_window: int = 3600) -> Dict[str, Any]:
        """获取API统计信息"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(seconds=time_window)
            stats = {}
            
            if endpoint:
                # 获取特定端点的统计
                metrics = []
                for key, records in self.api_metrics.items():
                    if endpoint in key:
                        metrics.extend([r for r in records 
                                      if r['timestamp'] > cutoff_time])
            else:
                # 获取所有端点的统计
                metrics = []
                for records in self.api_metrics.values():
                    metrics.extend([r for r in records 
                                  if r['timestamp'] > cutoff_time])
            
            if not metrics:
                return {'total_requests': 0}
            
            # 计算统计信息
            response_times = [m['response_time'] for m in metrics]
            status_codes = [m['status_code'] for m in metrics]
            
            stats = {
                'total_requests': len(metrics),
                'avg_response_time': sum(response_times) / len(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'success_rate': len([s for s in status_codes if s < 400]) / len(status_codes) * 100,
                'error_rate': len([s for s in status_codes if s >= 400]) / len(status_codes) * 100,
                'requests_per_minute': len(metrics) / (time_window / 60)
            }
            
            return stats
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        with self.lock:
            if not self.system_metrics:
                return {}
            
            latest = self.system_metrics[-1]
            return {
                'cpu_percent': latest['cpu_percent'],
                'memory_percent': latest['memory_percent'],
                'memory_used_mb': latest['memory_used_mb'],
                'disk_usage_percent': latest['disk_usage_percent'],
                'timestamp': latest['timestamp']
            }
    
    def get_error_summary(self, time_window: int = 3600) -> Dict[str, Any]:
        """获取错误摘要"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(seconds=time_window)
            recent_errors = [e for e in self.error_metrics 
                           if e['timestamp'] > cutoff_time]
            
            if not recent_errors:
                return {'total_errors': 0}
            
            # 按错误类型分组
            error_types = defaultdict(int)
            error_endpoints = defaultdict(int)
            
            for error in recent_errors:
                error_types[error.get('error_type', 'unknown')] += 1
                error_endpoints[error.get('endpoint', 'unknown')] += 1
            
            return {
                'total_errors': len(recent_errors),
                'error_types': dict(error_types),
                'error_endpoints': dict(error_endpoints),
                'recent_errors': recent_errors[-10:]  # 最近10个错误
            }
    
    def _start_system_monitor(self):
        """启动系统监控线程"""
        def monitor_worker():
            while True:
                try:
                    # 收集系统指标
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    disk = psutil.disk_usage('/')
                    
                    system_metric = {
                        'timestamp': datetime.now(),
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'memory_used_mb': memory.used / 1024 / 1024,
                        'disk_usage_percent': disk.percent
                    }
                    
                    with self.lock:
                        self.system_metrics.append(system_metric)
                    
                    time.sleep(60)  # 每分钟收集一次
                    
                except Exception as e:
                    logger.error(f"系统监控出错: {str(e)}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()


# 全局性能监控实例
performance_monitor = PerformanceMetrics()


def monitor_performance(endpoint_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            endpoint = endpoint_name or func.__name__
            
            try:
                result = func(*args, **kwargs)
                response_time = time.time() - start_time
                
                # 记录成功的API调用
                performance_monitor.record_api_call(
                    endpoint=endpoint,
                    method='FUNCTION',
                    response_time=response_time,
                    status_code=200
                )
                
                return result
                
            except Exception as e:
                response_time = time.time() - start_time
                
                # 记录失败的API调用
                performance_monitor.record_api_call(
                    endpoint=endpoint,
                    method='FUNCTION',
                    response_time=response_time,
                    status_code=500
                )
                
                # 记录错误详情
                performance_monitor.record_error(
                    endpoint=endpoint,
                    error_type=type(e).__name__,
                    error_message=str(e)
                )
                
                raise
        
        return wrapper
    return decorator


class PerformanceAlert:
    """性能告警系统"""
    
    def __init__(self):
        self.thresholds = {
            'response_time': 5.0,      # 响应时间阈值（秒）
            'error_rate': 10.0,        # 错误率阈值（%）
            'cpu_usage': 80.0,         # CPU使用率阈值（%）
            'memory_usage': 85.0,      # 内存使用率阈值（%）
        }
        self.alert_history = deque(maxlen=100)
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        
        # 检查API性能
        api_stats = performance_monitor.get_api_stats(time_window=300)  # 5分钟窗口
        if api_stats.get('avg_response_time', 0) > self.thresholds['response_time']:
            alerts.append({
                'type': 'high_response_time',
                'message': f"平均响应时间过高: {api_stats['avg_response_time']:.2f}s",
                'severity': 'warning',
                'timestamp': datetime.now()
            })
        
        if api_stats.get('error_rate', 0) > self.thresholds['error_rate']:
            alerts.append({
                'type': 'high_error_rate',
                'message': f"错误率过高: {api_stats['error_rate']:.2f}%",
                'severity': 'critical',
                'timestamp': datetime.now()
            })
        
        # 检查系统性能
        system_stats = performance_monitor.get_system_stats()
        if system_stats.get('cpu_percent', 0) > self.thresholds['cpu_usage']:
            alerts.append({
                'type': 'high_cpu_usage',
                'message': f"CPU使用率过高: {system_stats['cpu_percent']:.1f}%",
                'severity': 'warning',
                'timestamp': datetime.now()
            })
        
        if system_stats.get('memory_percent', 0) > self.thresholds['memory_usage']:
            alerts.append({
                'type': 'high_memory_usage',
                'message': f"内存使用率过高: {system_stats['memory_percent']:.1f}%",
                'severity': 'critical',
                'timestamp': datetime.now()
            })
        
        # 记录告警历史
        for alert in alerts:
            self.alert_history.append(alert)
        
        return alerts


# 全局告警系统实例
performance_alert = PerformanceAlert()


def get_performance_dashboard() -> Dict[str, Any]:
    """获取性能仪表板数据"""
    return {
        'api_stats': performance_monitor.get_api_stats(),
        'system_stats': performance_monitor.get_system_stats(),
        'error_summary': performance_monitor.get_error_summary(),
        'active_alerts': performance_alert.check_alerts(),
        'alert_history': list(performance_alert.alert_history)[-10:]
    } 