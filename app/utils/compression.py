"""
Flask响应压缩中间件
"""
import gzip
import io
from flask import request, g
from functools import wraps


def gzip_response(f):
    """
    装饰器：对响应进行gzip压缩
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查客户端是否支持gzip
        if 'gzip' not in request.headers.get('Accept-Encoding', ''):
            return f(*args, **kwargs)
        
        # 获取原始响应
        response = f(*args, **kwargs)
        
        # 如果是Flask响应对象
        if hasattr(response, 'get_data'):
            # 获取响应数据
            data = response.get_data()
            
            # 只压缩大于1KB的文本内容
            if len(data) > 1024 and response.content_type.startswith(('text/', 'application/json', 'application/javascript')):
                # 压缩数据
                buffer = io.BytesIO()
                with gzip.GzipFile(fileobj=buffer, mode='wb') as gz_file:
                    gz_file.write(data)
                compressed_data = buffer.getvalue()
                
                # 设置响应
                response.data = compressed_data
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(compressed_data)
                response.headers['Vary'] = 'Accept-Encoding'
        
        return response
    return decorated_function


def init_compression(app):
    """
    初始化压缩中间件
    """
    @app.after_request
    def compress_response(response):
        # 跳过静态文件 - 让web服务器处理
        if request.endpoint == 'static':
            return response
            
        # 检查是否应该压缩
        if (response.status_code < 200 or 
            response.status_code >= 300 or 
            'Content-Encoding' in response.headers or
            'gzip' not in request.headers.get('Accept-Encoding', '')):
            return response
        
        # 检查响应是否是直接传递模式
        if hasattr(response, 'direct_passthrough') and response.direct_passthrough:
            return response
            
        # 检查内容类型
        content_type = response.headers.get('Content-Type', '')
        compressible_types = [
            'text/html',
            'text/css', 
            'text/javascript',
            'application/javascript',
            'application/json',
            'application/xml',
            'text/xml'
        ]
        
        if not any(content_type.startswith(ct) for ct in compressible_types):
            return response
        
        try:
            # 获取响应数据
            data = response.get_data()
            
            # 只压缩大于1KB的内容
            if len(data) < 1024:
                return response
            
            # 压缩数据
            buffer = io.BytesIO()
            with gzip.GzipFile(fileobj=buffer, mode='wb') as gz_file:
                gz_file.write(data)
            compressed_data = buffer.getvalue()
            
            # 如果压缩后更小，则使用压缩版本
            if len(compressed_data) < len(data):
                response.data = compressed_data
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(compressed_data)
                response.headers['Vary'] = 'Accept-Encoding'
        
        except (RuntimeError, AttributeError):
            # 如果无法获取数据，跳过压缩
            pass
        
        return response
    
    return app