/**
 * 通用侧边栏修复器
 * 确保侧边栏折叠/展开功能在所有页面都能正常工作
 */

class UniversalSidebarFix {
    constructor() {
        this.sidebar = null;
        this.mainContent = null;
        this.collapseToggle = null;
        this.expandToggle = null;
        this.initialized = false;
        
        // 等待DOM加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }
    
    init() {
        if (this.initialized) return;
        
        // 查找DOM元素
        this.findElements();
        
        // 如果找不到必要的元素，则不初始化
        if (!this.sidebar || !this.collapseToggle) {
            console.log('侧边栏修复器：未找到必要的元素，跳过初始化');
            return;
        }
        
        // 确保侧边栏默认处于展开状态
        this.ensureDefaultExpanded();
        
        // 绑定事件
        this.bindEvents();
        
        // 恢复之前的状态（如果用户之前明确折叠过）
        this.restoreState();
        
        this.initialized = true;
        console.log('通用侧边栏修复器已初始化，默认展开状态');
    }
    
    findElements() {
        // 查找侧边栏
        this.sidebar = document.querySelector('.sidebar');
        
        // 查找主内容区域（可能有多种类名）
        this.mainContent = document.querySelector('.main-content') || 
                          document.querySelector('.container-fluid') ||
                          document.querySelector('main') ||
                          document.body;
        
        // 查找折叠按钮
        this.collapseToggle = document.getElementById('collapseToggle');
        
        // 查找展开按钮
        this.expandToggle = document.getElementById('expandToggle') || 
                           document.querySelector('.sidebar-expand-btn');
        
        console.log('侧边栏修复器元素查找结果:', {
            sidebar: !!this.sidebar,
            mainContent: !!this.mainContent,
            collapseToggle: !!this.collapseToggle,
            expandToggle: !!this.expandToggle
        });
    }
    
    bindEvents() {
        // 绑定折叠按钮点击事件
        if (this.collapseToggle) {
            this.collapseToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
            });
        }
        
        // 绑定展开按钮点击事件
        if (this.expandToggle) {
            this.expandToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.expandSidebar();
            });
        }
        
        // 窗口大小变化时的处理
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    toggleSidebar() {
        if (!this.sidebar) return;
        
        const isCollapsed = this.sidebar.classList.contains('sidebar-collapsed');
        
        if (isCollapsed) {
            this.expandSidebar();
        } else {
            this.collapseSidebar();
        }
        
        // 保存状态
        this.saveState();
    }
    
    collapseSidebar() {
        if (!this.sidebar) return;
        
        // 添加折叠类
        this.sidebar.classList.add('sidebar-collapsed');
        
        // 调整主内容区域
        if (this.mainContent) {
            this.mainContent.classList.add('main-content-expanded');
        }
        
        // 更新折叠按钮图标
        this.updateCollapseIcon(true);
        
        // 隐藏文本元素
        this.toggleTextElements(false);
        
        // 显示展开按钮
        this.showExpandButton();
        
        console.log('侧边栏已折叠');
    }
    
    expandSidebar() {
        if (!this.sidebar) return;
        
        // 移除折叠类
        this.sidebar.classList.remove('sidebar-collapsed');
        
        // 恢复主内容区域
        if (this.mainContent) {
            this.mainContent.classList.remove('main-content-expanded');
        }
        
        // 更新折叠按钮图标
        this.updateCollapseIcon(false);
        
        // 显示文本元素
        this.toggleTextElements(true);
        
        // 隐藏展开按钮
        this.hideExpandButton();
        
        console.log('侧边栏已展开');
    }
    
    updateCollapseIcon(isCollapsed) {
        if (!this.collapseToggle) return;
        
        const icon = this.collapseToggle.querySelector('i');
        if (icon) {
            if (isCollapsed) {
                icon.classList.remove('bi-chevron-left');
                icon.classList.add('bi-chevron-right');
            } else {
                icon.classList.remove('bi-chevron-right');
                icon.classList.add('bi-chevron-left');
            }
        }
    }
    
    toggleTextElements(show) {
        if (!this.sidebar) return;
        
        // 查找所有文本元素
        const textElements = this.sidebar.querySelectorAll('.nav-text, .sidebar-header h4, .sidebar-header .user-info, .sidebar-footer, .sidebar-search, .api-status-container');
        
        textElements.forEach(el => {
            el.style.display = show ? '' : 'none';
        });
        
        // 调整按钮样式
        const buttons = this.sidebar.querySelectorAll('.nav-link, .btn');
        buttons.forEach(btn => {
            if (show) {
                btn.classList.remove('text-center');
                btn.classList.add('text-start');
            } else {
                btn.classList.add('text-center');
                btn.classList.remove('text-start');
            }
        });
    }
    
    showExpandButton() {
        if (this.expandToggle && window.innerWidth > 768) {
            this.expandToggle.classList.remove('d-none');
            this.expandToggle.style.display = 'block';
        }
    }
    
    hideExpandButton() {
        if (this.expandToggle) {
            this.expandToggle.classList.add('d-none');
            this.expandToggle.style.display = 'none';
        }
    }
    
    ensureDefaultExpanded() {
        if (!this.sidebar) return;
        
        // 移除可能存在的折叠类，确保默认展开
        this.sidebar.classList.remove('sidebar-collapsed');
        
        // 确保主内容区域也处于正常状态
        if (this.mainContent) {
            this.mainContent.classList.remove('main-content-expanded');
        }
        
        // 确保折叠按钮图标正确
        this.updateCollapseIcon(false);
        
        // 确保文本元素可见
        this.toggleTextElements(true);
        
        // 隐藏展开按钮
        this.hideExpandButton();
        
        console.log('侧边栏已设置为默认展开状态');
    }
    
    handleResize() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移动端始终展开
            this.expandSidebar();
            this.hideExpandButton();
        } else {
            // 桌面端恢复保存的状态
            this.restoreState();
        }
    }
    
    saveState() {
        if (!this.sidebar || window.innerWidth <= 768) return;
        
        const isCollapsed = this.sidebar.classList.contains('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
    }
    
    restoreState() {
        if (!this.sidebar || window.innerWidth <= 768) return;
        
        // 只在用户明确设置了折叠状态时才恢复，否则默认保持展开
        const savedState = localStorage.getItem('sidebarCollapsed');
        
        // 只有当用户明确折叠过侧边栏时才应用折叠状态
        if (savedState === 'true') {
            this.collapseSidebar();
        } else {
            // 默认展开状态，确保侧边栏完全展开
            this.expandSidebar();
        }
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.universalSidebarFix = new UniversalSidebarFix();
} 