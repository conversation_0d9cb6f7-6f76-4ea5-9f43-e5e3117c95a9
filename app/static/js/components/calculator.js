/**
 * Calculator Component - 计算器组件
 * 独立可复用的计算器模块
 */

class CalculatorModule {
    constructor(triggerElement) {
        this.triggerElement = triggerElement;
        this.modal = null;
        this.display = null;
        this.buttons = null;
        
        // 计算器状态
        this.state = {
            currentInput: '0',
            previousInput: null,
            operator: null,
            waitingForNext: false,
            expression: ''
        };
        
        this.init();
    }

    init() {
        this.bindTrigger();
        this.setupModal();
    }

    bindTrigger() {
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }
    }

    setupModal() {
        this.modal = document.getElementById('calculatorModal');
        if (this.modal) {
            this.display = this.modal.querySelector('#calcDisplay');
            this.buttons = this.modal.querySelectorAll('.calc-buttons button');
            
            this.bindModalEvents();
            this.bindCalculatorEvents();
            this.bindKeyboardEvents();
        }
    }

    bindModalEvents() {
        // 模态框事件处理
        this.modal.addEventListener('show.bs.modal', () => {
            this.reset();
        });

        this.modal.addEventListener('shown.bs.modal', () => {
            if (this.display) {
                this.display.focus();
            }
        });

        this.modal.addEventListener('hide.bs.modal', () => {
            this.clearFocus();
        });

        this.modal.addEventListener('hidden.bs.modal', () => {
            this.reset();
            this.restoreFocus();
        });

        // 关闭按钮特殊处理
        const closeButtons = this.modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.clearFocus();
            });
        });
    }

    bindCalculatorEvents() {
        this.buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleButtonClick(e.target);
            });
        });
    }

    bindKeyboardEvents() {
        // 只在模态框打开时响应键盘事件
        document.addEventListener('keydown', (event) => {
            if (!this.modal.classList.contains('show')) return;
            
            this.handleKeyboardInput(event);
        });
    }

    handleButtonClick(button) {
        const value = button.textContent;
        
        // 视觉反馈
        this.addButtonFeedback(button);
        
        // 处理不同类型的输入
        if (value >= '0' && value <= '9') {
            this.inputNumber(value);
        } else if (value === '.') {
            this.inputDecimal();
        } else if (['+', '-', '×', '÷'].includes(value)) {
            this.inputOperator(value);
        } else if (value === '=') {
            this.performCalculation();
        } else if (value === 'C') {
            this.clear();
        } else if (value === '±') {
            this.toggleSign();
        } else if (value === '%') {
            this.percentage();
        }
    }

    handleKeyboardInput(event) {
        const key = event.key;
        
        if (key >= '0' && key <= '9') {
            this.inputNumber(key);
        } else if (key === '.') {
            this.inputDecimal();
        } else if (key === '+') {
            this.inputOperator('+');
        } else if (key === '-') {
            this.inputOperator('-');
        } else if (key === '*') {
            this.inputOperator('×');
        } else if (key === '/') {
            event.preventDefault();
            this.inputOperator('÷');
        } else if (key === 'Enter' || key === '=') {
            event.preventDefault();
            this.performCalculation();
        } else if (key === 'Escape' || key === 'c' || key === 'C') {
            this.clear();
        } else if (key === 'Backspace') {
            this.backspace();
        }
    }

    // 计算器逻辑方法
    inputNumber(num) {
        if (this.state.waitingForNext) {
            this.state.currentInput = num;
            this.state.waitingForNext = false;
        } else {
            this.state.currentInput = this.state.currentInput === '0' ? num : this.state.currentInput + num;
        }
        this.updateDisplay();
    }

    inputDecimal() {
        if (this.state.waitingForNext) {
            this.state.currentInput = '0.';
            this.state.waitingForNext = false;
        } else if (this.state.currentInput.indexOf('.') === -1) {
            this.state.currentInput += '.';
        }
        this.updateDisplay();
    }

    inputOperator(nextOperator) {
        const inputValue = parseFloat(this.state.currentInput);
        
        if (this.state.previousInput === null) {
            this.state.previousInput = inputValue;
        } else if (this.state.operator) {
            const currentValue = this.state.previousInput || 0;
            const newValue = this.calculate(currentValue, inputValue, this.state.operator);
            
            this.state.currentInput = String(newValue);
            this.state.previousInput = newValue;
        } else {
            this.state.previousInput = inputValue;
        }
        
        this.state.waitingForNext = true;
        this.state.operator = nextOperator;
        this.updateDisplay();
    }

    performCalculation() {
        const inputValue = parseFloat(this.state.currentInput);
        
        if (this.state.previousInput !== null && this.state.operator) {
            const fullExpression = `${this.state.previousInput} ${this.state.operator} ${inputValue}`;
            const newValue = this.calculate(this.state.previousInput, inputValue, this.state.operator);
            
            // 显示完整表达式和结果
            this.display.value = `${fullExpression} = ${newValue}`;
            
            // 延迟显示最终结果
            setTimeout(() => {
                this.state.currentInput = String(newValue);
                this.state.previousInput = null;
                this.state.operator = null;
                this.state.waitingForNext = true;
                this.state.expression = '';
                this.updateDisplay();
            }, 1000);
        }
    }

    calculate(firstValue, secondValue, operator) {
        switch (operator) {
            case '+':
                return firstValue + secondValue;
            case '-':
                return firstValue - secondValue;
            case '×':
                return firstValue * secondValue;
            case '÷':
                return secondValue !== 0 ? firstValue / secondValue : 0;
            case '%':
                return firstValue % secondValue;
            default:
                return secondValue;
        }
    }

    toggleSign() {
        if (this.state.currentInput !== '0') {
            const oldValue = this.state.currentInput;
            this.state.currentInput = this.state.currentInput.charAt(0) === '-' 
                ? this.state.currentInput.slice(1) 
                : '-' + this.state.currentInput;
            
            // 显示操作过程
            this.display.value = `±(${oldValue}) = ${this.state.currentInput}`;
            setTimeout(() => {
                this.updateDisplay();
            }, 800);
        }
    }

    percentage() {
        const value = parseFloat(this.state.currentInput);
        const oldValue = this.state.currentInput;
        this.state.currentInput = String(value / 100);
        
        // 显示百分号运算过程
        this.display.value = `${oldValue}% = ${this.state.currentInput}`;
        setTimeout(() => {
            this.updateDisplay();
        }, 800);
    }

    backspace() {
        if (this.state.currentInput.length > 1) {
            this.state.currentInput = this.state.currentInput.slice(0, -1);
        } else {
            this.state.currentInput = '0';
        }
        this.updateDisplay();
    }

    clear() {
        this.state.currentInput = '0';
        this.state.previousInput = null;
        this.state.operator = null;
        this.state.waitingForNext = false;
        this.state.expression = '';
        this.updateDisplay();
    }

    reset() {
        this.clear();
    }

    updateDisplay() {
        if (!this.display) return;
        
        let displayText = '';
        
        // 显示完整表达式或当前值
        if (this.state.operator && !this.state.waitingForNext) {
            displayText = `${this.state.previousInput} ${this.state.operator} ${this.state.currentInput}`;
        } else if (this.state.operator && this.state.waitingForNext) {
            displayText = `${this.state.previousInput} ${this.state.operator}`;
        } else {
            displayText = this.state.currentInput;
        }
        
        // 限制显示长度
        if (displayText.length > 20) {
            displayText = '...' + displayText.slice(-17);
        }
        
        this.display.value = displayText;
    }

    addButtonFeedback(button) {
        // 视觉反馈
        button.style.transform = 'scale(0.95)';
        button.style.transition = 'transform 0.1s';
        
        setTimeout(() => {
            button.style.transform = '';
        }, 100);
        
        // 运算符按钮特殊反馈
        if (['+', '-', '×', '÷'].includes(button.textContent)) {
            const originalBg = button.style.backgroundColor;
            button.style.backgroundColor = '#d1780a';
            setTimeout(() => {
                button.style.backgroundColor = originalBg;
            }, 200);
        }
    }

    show() {
        const calculatorModal = new bootstrap.Modal(this.modal);
        calculatorModal.show();
    }

    hide() {
        const calculatorModal = bootstrap.Modal.getInstance(this.modal);
        if (calculatorModal) {
            calculatorModal.hide();
        }
    }

    clearFocus() {
        const focusedElement = document.activeElement;
        if (focusedElement && this.modal.contains(focusedElement)) {
            focusedElement.blur();
        }
    }

    restoreFocus() {
        if (this.triggerElement && document.activeElement === document.body) {
            setTimeout(() => {
                this.triggerElement.focus();
            }, 100);
        }
    }

    destroy() {
        // 清理事件监听器
        if (this.triggerElement) {
            this.triggerElement.removeEventListener('click', this.show);
        }
        
        // 清理模态框事件
        if (this.modal) {
            this.modal.removeEventListener('show.bs.modal', this.reset);
            this.modal.removeEventListener('shown.bs.modal', this.focusDisplay);
            this.modal.removeEventListener('hide.bs.modal', this.clearFocus);
            this.modal.removeEventListener('hidden.bs.modal', this.restoreFocus);
        }
        
        // 清理按钮事件
        if (this.buttons) {
            this.buttons.forEach(button => {
                button.removeEventListener('click', this.handleButtonClick);
            });
        }
    }
}

// 导出模块
window.CalculatorModule = CalculatorModule;