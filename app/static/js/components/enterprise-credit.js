/**
 * Enterprise Credit Component - 企业信用查询组件
 * 基于Coze API的企业信用查询模块
 */

class EnterpriseCreditModule {
    constructor(triggerElement) {
        this.triggerElement = triggerElement;
        this.modal = null;
        this.form = null;
        this.enterpriseInput = null;
        this.queryBtn = null;
        this.clearBtn = null;
        this.clearResultBtn = null;
        this.toggleViewBtn = null;
        this.outputArea = null;
        this.statusDot = null;
        this.statusText = null;
        this.eventCountElement = null;
        this.queryTimeElement = null;
        this.streamingToggle = null;
        this.speedSetting = null;
        this.speedValue = null;
        this.textView = null;
        this.tableView = null;
        this.enterpriseTable = null;
        this.enterpriseTableBody = null;

        this.eventCount = 0;
        this.isQuerying = false;
        this.currentView = 'text'; // 'text' or 'table'
        this.queryStartTime = null;
        this.tableData = [];

        this.init();
    }

    init() {
        this.bindTrigger();
        this.setupModal();
    }

    bindTrigger() {
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }
    }

    setupModal() {
        this.modal = document.getElementById('enterpriseCreditModal');
        if (this.modal) {
            this.initializeElements();
            this.bindModalEvents();
            this.bindFormEvents();
        }
    }

    initializeElements() {
        this.form = this.modal.querySelector('#enterpriseCreditForm');
        this.enterpriseInput = this.modal.querySelector('#enterpriseInput');
        this.queryBtn = this.modal.querySelector('#queryEnterpriseBtn');
        this.clearBtn = this.modal.querySelector('#clearEnterpriseBtn');
        this.clearResultBtn = this.modal.querySelector('#clearResultBtn');
        this.toggleViewBtn = this.modal.querySelector('#toggleViewBtn');
        this.outputArea = this.modal.querySelector('#enterpriseOutputArea');
        this.statusDot = this.modal.querySelector('#enterpriseStatusDot');
        this.statusText = this.modal.querySelector('#enterpriseStatusText');
        this.eventCountElement = this.modal.querySelector('#enterpriseEventCount');
        this.queryTimeElement = this.modal.querySelector('#queryTime');
        this.streamingToggle = this.modal.querySelector('#streamingToggle');
        this.speedSetting = this.modal.querySelector('#speedSetting');
        this.speedValue = this.modal.querySelector('#speedValue');
        this.textView = this.modal.querySelector('#textView');
        this.tableView = this.modal.querySelector('#tableView');
        this.enterpriseTable = this.modal.querySelector('#enterpriseTable');
        this.enterpriseTableBody = this.modal.querySelector('#enterpriseTableBody');
    }

    bindModalEvents() {
        // 模态框事件处理
        this.modal.addEventListener('show.bs.modal', () => {
            this.resetForm();
        });

        this.modal.addEventListener('shown.bs.modal', () => {
            if (this.enterpriseInput) {
                this.enterpriseInput.focus();
            }
        });

        this.modal.addEventListener('hide.bs.modal', () => {
            this.clearFocus();
        });

        this.modal.addEventListener('hidden.bs.modal', () => {
            this.resetForm();
            this.restoreFocus();
        });

        // 关闭按钮特殊处理
        const closeButtons = this.modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.clearFocus();
            });
        });
    }

    bindFormEvents() {
        if (this.queryBtn) {
            this.queryBtn.addEventListener('click', () => {
                this.queryEnterpriseCredit();
            });
        }

        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => {
                this.clearInput();
            });
        }

        if (this.clearResultBtn) {
            this.clearResultBtn.addEventListener('click', () => {
                this.clearOutput();
            });
        }

        if (this.toggleViewBtn) {
            this.toggleViewBtn.addEventListener('click', () => {
                this.toggleView();
            });
        }

        if (this.speedSetting) {
            this.speedSetting.addEventListener('input', (e) => {
                this.updateSpeedDisplay(e.target.value);
            });
        }

        if (this.enterpriseInput) {
            this.enterpriseInput.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    this.queryEnterpriseCredit();
                }
            });
        }

        // 初始化速度显示
        if (this.speedSetting && this.speedValue) {
            this.updateSpeedDisplay(this.speedSetting.value);
        }
    }

    async queryEnterpriseCredit() {
        const input = this.enterpriseInput.value.trim();
        if (!input) {
            this.showMessage('请输入企业名称或统一社会信用代码！', 'warning');
            return;
        }

        if (this.isQuerying) {
            this.showMessage('正在查询中，请稍候...', 'info');
            return;
        }

        this.isQuerying = true;
        this.queryStartTime = new Date();
        this.queryBtn.disabled = true;
        this.queryBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>查询中...';
        this.updateStatus('connecting', '连接中...');
        
        // 给用户设置预期
        this.showMessage('企业信用查询可能需要几分钟时间，请耐心等待...', 'info');

        // 清空输出区域和表格数据
        this.clearOutput();
        this.tableData = [];
        this.eventCount = 0;

        // 显示加载状态
        this.showLoadingState();

        const payload = {
            input: input,
            streaming: this.streamingToggle ? this.streamingToggle.checked : true,
            speed: this.speedSetting ? parseFloat(this.speedSetting.value) : 0.001
        };

        try {
            const response = await fetch('/api/enterprise-credit/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                // 尝试读取错误消息
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    if (errorData.message) {
                        errorMessage = errorData.message;
                    } else if (errorData.error) {
                        errorMessage = errorData.error;
                    }
                } catch (e) {
                    // 如果无法解析JSON，使用默认错误消息
                }
                throw new Error(errorMessage);
            }

            this.updateStatus('', '接收数据中...');

            if (payload.streaming) {
                await this.processStreamResponse(response);
            } else {
                await this.processDirectResponse(response);
            }

        } catch (error) {
            this.updateStatus('error', '查询失败');
            
            // 改善错误提示
            let errorMessage = '';
            if (error.name === 'TypeError' && error.message.includes('network')) {
                errorMessage = '❌ 网络连接错误，请检查网络连接后重试';
            } else if (error.message.includes('timeout') || error.message.includes('超时')) {
                errorMessage = '⏱️ 查询超时（超过10分钟），企业信用查询处理时间较长，请稍后重试';
            } else if (error.message.includes('504')) {
                errorMessage = '⏱️ API响应超时，请稍后重试（服务器处理时间较长）';
            } else if (error.message.includes('502')) {
                errorMessage = '🔌 服务连接错误，请稍后重试';
            } else {
                errorMessage = `❌ 查询失败: ${error.message}`;
            }
            
            this.showError(errorMessage);
        } finally {
            this.isQuerying = false;
            this.queryBtn.disabled = false;
            this.queryBtn.innerHTML = '<i class="bi bi-search me-2"></i>开始查询';
            this.updateStatus('', '就绪');
            this.updateQueryTime();
        }
    }

    async processStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        // 清空输出区域，准备显示流式内容
        this.outputArea.innerHTML = '';

        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;

                buffer += decoder.decode(value, { stream: true });

                // 按行处理数据
                while (buffer.includes('\n')) {
                    const lineEnd = buffer.indexOf('\n');
                    const line = buffer.slice(0, lineEnd).trim();
                    buffer = buffer.slice(lineEnd + 1);

                    if (!line) continue;

                    // 处理SSE格式数据
                    if (line.startsWith('data: ')) {
                        const dataStr = line.slice(6).trim();
                        
                        if (dataStr === '[DONE]') {
                            this.appendToOutput('\n✅ 查询完成', 'success');
                            break;
                        }

                        if (dataStr && dataStr !== 'null') {
                            await this.processEventData(dataStr);
                        }
                    }
                }
            }
        } catch (error) {
            this.showError(`❌ 处理响应时发生错误: ${error.message}`);
        }
    }

    async processEventData(dataStr) {
        this.updateEventCount();

        try {
            const eventData = JSON.parse(dataStr);
            
            // 检查是否有直接的content字段
            if (eventData.content) {
                let content = eventData.content;
                
                // 尝试解析content中的JSON
                try {
                    const contentJson = JSON.parse(content);
                    if (contentJson.as) {
                        content = contentJson.as;
                    }
                } catch (e) {
                    // 如果不是JSON，使用原始content
                }
                
                if (content.trim()) {
                    await this.typewriterEffect(content);
                    return;
                }
            }

            // 处理其他事件类型
            const eventType = eventData.event || 'unknown';
            const dataPayload = eventData.data || {};

            if (['message', 'content', 'delta'].includes(eventType)) {
                const content = dataPayload.content || '';
                if (content) {
                    await this.typewriterEffect(content);
                }
            } else if (eventType === 'error') {
                this.showError(`❌ 错误: ${JSON.stringify(dataPayload)}`);
            } else {
                this.appendToOutput(`📋 事件: ${eventType}`, 'info');
            }

        } catch (e) {
            // 如果不是JSON，直接作为文本处理
            if (dataStr.trim()) {
                await this.typewriterEffect(dataStr);
            }
        }
    }

    async typewriterEffect(text) {
        const speed = this.speedSetting ? parseFloat(this.speedSetting.value) * 1000 : 20; // 转换为毫秒

        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const textNode = document.createTextNode(char);
            this.outputArea.appendChild(textNode);

            // 自动滚动到底部
            this.outputArea.scrollTop = this.outputArea.scrollHeight;

            // 解析并添加到表格数据
            this.parseTextForTable(char);

            await new Promise(resolve => setTimeout(resolve, speed));
        }

        this.appendToOutput('\n', 'content');
    }

    parseTextForTable(char) {
        // 简单的文本解析逻辑，可以根据实际数据格式调整
        if (!this.currentLine) {
            this.currentLine = '';
        }

        if (char === '\n') {
            if (this.currentLine.trim()) {
                this.processLineForTable(this.currentLine.trim());
            }
            this.currentLine = '';
        } else {
            this.currentLine += char;
        }
    }

    processLineForTable(line) {
        // 解析包含冒号的行作为键值对
        const colonIndex = line.indexOf('：') || line.indexOf(':');
        if (colonIndex > 0) {
            const key = line.substring(0, colonIndex).trim();
            const value = line.substring(colonIndex + 1).trim();

            if (key && value) {
                this.tableData.push({ field: key, content: value });
                this.updateTableView();
            }
        }
    }

    updateTableView() {
        if (!this.enterpriseTableBody) return;

        if (this.tableData.length === 0) {
            this.enterpriseTableBody.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center text-muted py-4">
                        <i class="bi bi-table display-6 mb-2 d-block"></i>
                        暂无数据
                    </td>
                </tr>
            `;
            return;
        }

        this.enterpriseTableBody.innerHTML = this.tableData.map(item => `
            <tr>
                <td class="fw-semibold">${this.escapeHtml(item.field)}</td>
                <td>${this.escapeHtml(item.content)}</td>
            </tr>
        `).join('');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    appendToOutput(text, type = 'content') {
        const element = document.createElement('span');
        element.textContent = text;
        
        switch (type) {
            case 'error':
                element.style.color = '#dc3545';
                break;
            case 'success':
                element.style.color = '#28a745';
                break;
            case 'info':
                element.style.color = '#17a2b8';
                break;
            default:
                element.style.color = '#212529';
        }
        
        this.outputArea.appendChild(element);
        this.outputArea.scrollTop = this.outputArea.scrollHeight;
    }

    showError(message) {
        this.outputArea.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }

    updateStatus(status, text) {
        if (this.statusDot) {
            this.statusDot.className = `status-dot ${status}`;
        }
        if (this.statusText) {
            this.statusText.textContent = text;
        }
    }

    updateEventCount() {
        this.eventCount++;
        if (this.eventCountElement) {
            this.eventCountElement.textContent = `查询次数: ${this.eventCount}`;
        }
    }

    clearInput() {
        if (this.enterpriseInput) {
            this.enterpriseInput.value = '';
            this.enterpriseInput.focus();
        }
    }

    clearOutput() {
        if (this.outputArea) {
            this.outputArea.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <h6 class="empty-title">等待查询</h6>
                    <p class="empty-text">请输入企业信息并点击查询按钮</p>
                </div>
            `;
        }

        // 清空表格数据
        this.tableData = [];
        this.updateTableView();

        this.eventCount = 0;
        if (this.eventCountElement) {
            this.eventCountElement.textContent = '查询次数: 0';
        }

        if (this.queryTimeElement) {
            this.queryTimeElement.textContent = '';
        }

        this.currentLine = '';
    }

    showLoadingState() {
        if (this.currentView === 'text') {
            this.outputArea.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">查询中...</span>
                    </div>
                    <p class="text-muted">正在查询企业信用信息，请稍候...</p>
                </div>
            `;
        } else {
            this.enterpriseTableBody.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center py-4">
                        <div class="spinner-border text-primary mb-2" role="status">
                            <span class="visually-hidden">查询中...</span>
                        </div>
                        <div class="text-muted">正在查询企业信用信息...</div>
                    </td>
                </tr>
            `;
        }
    }

    toggleView() {
        if (this.currentView === 'text') {
            this.currentView = 'table';
            this.textView.classList.remove('active');
            this.tableView.classList.add('active');
            this.toggleViewBtn.innerHTML = '<i class="bi bi-file-text me-1"></i>文本视图';
        } else {
            this.currentView = 'text';
            this.tableView.classList.remove('active');
            this.textView.classList.add('active');
            this.toggleViewBtn.innerHTML = '<i class="bi bi-table me-1"></i>表格视图';
        }
    }

    updateSpeedDisplay(value) {
        if (this.speedValue) {
            this.speedValue.textContent = `${value}s`;
        }
    }

    updateQueryTime() {
        if (this.queryStartTime && this.queryTimeElement) {
            const duration = ((new Date() - this.queryStartTime) / 1000).toFixed(2);
            this.queryTimeElement.textContent = `查询耗时: ${duration}秒`;
        }
    }

    async processDirectResponse(response) {
        try {
            const data = await response.json();
            if (data.content) {
                this.outputArea.innerHTML = '';
                this.appendToOutput(data.content, 'content');
                this.processTextForTable(data.content);
            }
        } catch (error) {
            this.showError(`❌ 处理响应时发生错误: ${error.message}`);
        }
    }

    processTextForTable(text) {
        const lines = text.split('\n');
        lines.forEach(line => {
            if (line.trim()) {
                this.processLineForTable(line.trim());
            }
        });
    }

    resetForm() {
        this.clearInput();
        this.clearOutput();
        this.updateStatus('', '就绪');
    }

    // 模态框控制方法
    show() {
        const modal = new bootstrap.Modal(this.modal);
        modal.show();
    }

    hide() {
        const modal = bootstrap.Modal.getInstance(this.modal);
        if (modal) {
            modal.hide();
        }
    }

    clearFocus() {
        const focusedElement = document.activeElement;
        if (focusedElement && this.modal.contains(focusedElement)) {
            focusedElement.blur();
        }
    }

    restoreFocus() {
        if (this.triggerElement && document.activeElement === document.body) {
            setTimeout(() => {
                this.triggerElement.focus();
            }, 100);
        }
    }

    // 消息提示方法
    showMessage(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 自动淡出效果
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }

    // 销毁方法
    destroy() {
        // 清理事件监听器
        if (this.triggerElement) {
            this.triggerElement.removeEventListener('click', this.show);
        }
        
        if (this.queryBtn) {
            this.queryBtn.removeEventListener('click', this.queryEnterpriseCredit);
        }
        
        if (this.clearBtn) {
            this.clearBtn.removeEventListener('click', this.clearInput);
        }
        
        if (this.clearResultBtn) {
            this.clearResultBtn.removeEventListener('click', this.clearOutput);
        }
        
        if (this.enterpriseInput) {
            this.enterpriseInput.removeEventListener('keydown', this.handleKeyDown);
        }
    }
}

// 导出模块
window.EnterpriseCreditModule = EnterpriseCreditModule;
