/**
 * 数据验证和JSON解析工具
 * 提供健壮的数据处理和验证功能
 */

class DataValidator {
    /**
     * 安全的JSON解析，带有详细错误处理
     * @param {string} jsonString - 要解析的JSON字符串
     * @param {*} defaultValue - 解析失败时的默认值
     * @param {string} context - 解析上下文，用于错误日志
     * @returns {*} 解析后的对象或默认值
     */
    static safeJsonParse(jsonString, defaultValue = null, context = 'Unknown') {
        if (typeof jsonString !== 'string') {
            console.warn(`[DataValidator] ${context}: 输入不是字符串类型:`, typeof jsonString);
            return defaultValue;
        }
        
        if (jsonString.trim() === '') {
            console.warn(`[DataValidator] ${context}: 输入为空字符串`);
            return defaultValue;
        }
        
        try {
            const parsed = JSON.parse(jsonString);
            console.log(`[DataValidator] ${context}: JSON解析成功`);
            return parsed;
        } catch (error) {
            console.error(`[DataValidator] ${context}: JSON解析失败`, {
                error: error.message,
                input: jsonString.substring(0, 200) + (jsonString.length > 200 ? '...' : ''),
                inputLength: jsonString.length
            });
            return defaultValue;
        }
    }
    
    /**
     * 安全的响应JSON处理
     * @param {Response} response - Fetch API响应对象
     * @param {string} context - 请求上下文
     * @returns {Promise} 解析后的数据或错误
     */
    static async safeResponseJson(response, context = 'API Request') {
        try {
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 检查内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.warn(`[DataValidator] ${context}: 响应不是JSON格式，Content-Type: ${contentType}`);
                
                // 尝试获取文本内容以便调试
                const text = await response.text();
                console.warn(`[DataValidator] ${context}: 响应内容:`, text.substring(0, 500));
                
                throw new Error('服务器响应不是JSON格式');
            }
            
            // 解析JSON
            const data = await response.json();
            console.log(`[DataValidator] ${context}: JSON响应解析成功`);
            return data;
            
        } catch (error) {
            console.error(`[DataValidator] ${context}: 响应处理失败`, {
                error: error.message,
                status: response.status,
                statusText: response.statusText,
                url: response.url
            });
            throw new Error(`数据处理失败: ${error.message}`);
        }
    }
    
    /**
     * 验证图表数据格式
     * @param {*} data - 要验证的图表数据
     * @param {string} chartType - 图表类型
     * @returns {Object} 验证结果
     */
    static validateChartData(data, chartType = 'chart') {
        const result = {
            valid: false,
            errors: [],
            warnings: []
        };
        
        // 基本类型检查
        if (!data || typeof data !== 'object') {
            result.errors.push('图表数据必须是对象类型');
            return result;
        }
        
        // 检查必需的属性
        if (!data.labels || !Array.isArray(data.labels)) {
            result.errors.push('缺少有效的labels数组');
        } else if (data.labels.length === 0) {
            result.warnings.push('labels数组为空');
        }
        
        if (!data.datasets || !Array.isArray(data.datasets)) {
            result.errors.push('缺少有效的datasets数组');
        } else if (data.datasets.length === 0) {
            result.warnings.push('datasets数组为空');
        } else {
            // 验证每个数据集
            data.datasets.forEach((dataset, index) => {
                if (!dataset.data || !Array.isArray(dataset.data)) {
                    result.errors.push(`数据集${index + 1}缺少有效的data数组`);
                } else if (dataset.data.length !== data.labels.length) {
                    result.errors.push(`数据集${index + 1}的数据长度与标签长度不匹配`);
                }
                
                if (!dataset.label) {
                    result.warnings.push(`数据集${index + 1}缺少标签`);
                }
            });
        }
        
        result.valid = result.errors.length === 0;
        
        if (result.valid) {
            console.log(`[DataValidator] ${chartType}图表数据验证通过`, {
                labelsCount: data.labels.length,
                datasetsCount: data.datasets.length,
                warnings: result.warnings
            });
        } else {
            console.error(`[DataValidator] ${chartType}图表数据验证失败`, result.errors);
        }
        
        return result;
    }
    
    /**
     * 验证表格数据格式
     * @param {*} data - 要验证的表格数据
     * @param {string} tableType - 表格类型
     * @returns {Object} 验证结果
     */
    static validateTableData(data, tableType = 'table') {
        const result = {
            valid: false,
            errors: [],
            warnings: []
        };
        
        if (!data || typeof data !== 'object') {
            result.errors.push('表格数据必须是对象类型');
            return result;
        }
        
        // 检查headers
        if (!data.headers || !Array.isArray(data.headers)) {
            result.errors.push('缺少有效的headers数组');
        } else if (data.headers.length === 0) {
            result.warnings.push('headers数组为空');
        }
        
        // 检查summary/data
        const dataArray = data.summary || data.data;
        if (!dataArray || !Array.isArray(dataArray)) {
            result.errors.push('缺少有效的数据数组');
        } else if (dataArray.length === 0) {
            result.warnings.push('数据数组为空');
        } else if (data.headers) {
            // 检查数据行的列数是否与headers匹配
            const headerCount = data.headers.length;
            dataArray.forEach((row, index) => {
                if (!Array.isArray(row)) {
                    result.errors.push(`第${index + 1}行数据不是数组类型`);
                } else if (row.length !== headerCount) {
                    result.warnings.push(`第${index + 1}行数据列数(${row.length})与headers(${headerCount})不匹配`);
                }
            });
        }
        
        result.valid = result.errors.length === 0;
        
        if (result.valid) {
            console.log(`[DataValidator] ${tableType}表格数据验证通过`, {
                headersCount: data.headers?.length || 0,
                rowsCount: dataArray?.length || 0,
                warnings: result.warnings
            });
        } else {
            console.error(`[DataValidator] ${tableType}表格数据验证失败`, result.errors);
        }
        
        return result;
    }
    
    /**
     * 验证API响应数据
     * @param {*} data - API响应数据
     * @param {string} expectedType - 期望的数据类型
     * @param {Array} requiredFields - 必需的字段
     * @returns {Object} 验证结果
     */
    static validateApiResponse(data, expectedType = 'object', requiredFields = []) {
        const result = {
            valid: false,
            errors: [],
            warnings: []
        };
        
        // 基本类型检查
        if (expectedType === 'object' && (!data || typeof data !== 'object')) {
            result.errors.push(`期望对象类型，实际类型: ${typeof data}`);
            return result;
        }
        
        if (expectedType === 'array' && !Array.isArray(data)) {
            result.errors.push(`期望数组类型，实际类型: ${typeof data}`);
            return result;
        }
        
        // 检查必需字段
        if (expectedType === 'object' && requiredFields.length > 0) {
            requiredFields.forEach(field => {
                if (!(field in data)) {
                    result.errors.push(`缺少必需字段: ${field}`);
                } else if (data[field] === null || data[field] === undefined) {
                    result.warnings.push(`字段 ${field} 值为空`);
                }
            });
        }
        
        result.valid = result.errors.length === 0;
        
        console.log(`[DataValidator] API响应验证${result.valid ? '通过' : '失败'}`, {
            expectedType,
            requiredFields,
            errors: result.errors,
            warnings: result.warnings
        });
        
        return result;
    }
    
    /**
     * 显示用户友好的错误消息
     * @param {string} context - 错误上下文
     * @param {string} message - 错误消息
     * @param {string} type - 错误类型 ('error', 'warning', 'info')
     */
    static showUserMessage(context, message, type = 'error') {
        // 创建或获取消息容器
        let messageContainer = document.getElementById('data-validator-messages');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'data-validator-messages';
            messageContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(messageContainer);
        }
        
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show`;
        messageEl.style.cssText = 'margin-bottom: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);';
        
        messageEl.innerHTML = `
            <strong>${context}:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        messageContainer.appendChild(messageEl);
        
        // 自动移除消息
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, type === 'error' ? 8000 : 5000);
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.DataValidator = DataValidator;
}

// 如果在Node.js环境中
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataValidator;
}

console.log('数据验证工具已加载');