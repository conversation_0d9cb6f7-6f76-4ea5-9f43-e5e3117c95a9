/**
 * 性能增强工具
 * 提升用户体验和页面性能
 */

window.PerformanceEnhancer = {
    
    // 性能监控配置
    config: {
        enableLazyLoading: true,
        enableVirtualization: true,
        enableDebouncing: true,
        enablePreloading: true,
        debounceDelay: 300,
        performanceLogging: true
    },
    
    // 性能指标收集
    metrics: {
        pageLoadTime: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        cumulativeLayoutShift: 0,
        
        init: function() {
            // 页面加载时间
            window.addEventListener('load', () => {
                this.pageLoadTime = performance.now();
                console.log(`页面加载时间: ${this.pageLoadTime.toFixed(2)}ms`);
            });
            
            // 监控Core Web Vitals
            this.observeWebVitals();
        },
        
        observeWebVitals: function() {
            // 监控Largest Contentful Paint (LCP)
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.largestContentfulPaint = lastEntry.startTime;
                    console.log(`LCP: ${this.largestContentfulPaint.toFixed(2)}ms`);
                });
                
                try {
                    observer.observe({ entryTypes: ['largest-contentful-paint'] });
                } catch (error) {
                    console.warn('LCP监控不支持:', error);
                }
            }
        },
        
        // 记录自定义性能指标
        recordMetric: function(name, value) {
            if (this.performanceLogging) {
                console.log(`性能指标 ${name}: ${value}`);
            }
        }
    },
    
    // 防抖处理工具
    debounce: {
        timers: new Map(),
        
        create: function(func, delay = PerformanceEnhancer.config.debounceDelay) {
            return function(...args) {
                const key = func.name || 'anonymous';
                const existingTimer = PerformanceEnhancer.debounce.timers.get(key);
                
                if (existingTimer) {
                    clearTimeout(existingTimer);
                }
                
                const timer = setTimeout(() => {
                    func.apply(this, args);
                    PerformanceEnhancer.debounce.timers.delete(key);
                }, delay);
                
                PerformanceEnhancer.debounce.timers.set(key, timer);
            };
        },
        
        cleanup: function() {
            for (const [key, timer] of this.timers) {
                clearTimeout(timer);
            }
            this.timers.clear();
        }
    },
    
    // 懒加载实现
    lazyLoading: {
        observer: null,
        
        init: function() {
            if (!PerformanceEnhancer.config.enableLazyLoading) return;
            
            // 检查浏览器支持
            if ('IntersectionObserver' in window) {
                this.observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.loadElement(entry.target);
                            this.observer.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.1
                });
                
                // 观察所有懒加载元素
                this.observeElements();
            } else {
                // 降级处理：立即加载所有元素
                this.loadAllElements();
            }
        },
        
        observeElements: function() {
            const lazyElements = document.querySelectorAll('[data-lazy]');
            lazyElements.forEach(element => {
                this.observer.observe(element);
            });
        },
        
        loadElement: function(element) {
            const src = element.getAttribute('data-lazy');
            if (src) {
                if (element.tagName === 'IMG') {
                    element.src = src;
                } else if (element.tagName === 'IFRAME') {
                    element.src = src;
                } else {
                    // 处理其他类型的懒加载
                    element.innerHTML = src;
                }
                
                element.removeAttribute('data-lazy');
                element.classList.add('loaded');
            }
        },
        
        loadAllElements: function() {
            const lazyElements = document.querySelectorAll('[data-lazy]');
            lazyElements.forEach(element => {
                this.loadElement(element);
            });
        }
    },
    
    // 资源预加载
    preloading: {
        init: function() {
            if (!PerformanceEnhancer.config.enablePreloading) return;
            
            // 预加载关键资源
            this.preloadCriticalResources();
            
            // 预加载可能访问的页面
            this.preloadLikelyPages();
        },
        
        preloadCriticalResources: function() {
            const criticalResources = [
                '/static/js/core/api-cache-manager.js',
                '/static/js/modules/api-data-manager.js',
                '/static/js/modules/table-manager.js'
            ];
            
            criticalResources.forEach(resource => {
                this.preloadResource(resource, 'script');
            });
        },
        
        preloadLikelyPages: function() {
            // 监听链接悬停事件，预加载页面
            document.addEventListener('mouseover', (event) => {
                if (event.target.tagName === 'A') {
                    const href = event.target.getAttribute('href');
                    if (href && href.startsWith('/') && !href.startsWith('//')) {
                        this.preloadResource(href, 'document');
                    }
                }
            });
        },
        
        preloadResource: function(url, type) {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = url;
            link.as = type;
            document.head.appendChild(link);
        }
    },
    
    // 表格虚拟化
    virtualization: {
        init: function() {
            if (!PerformanceEnhancer.config.enableVirtualization) return;
            
            // 为大型表格启用虚拟化
            this.enableTableVirtualization();
        },
        
        enableTableVirtualization: function() {
            const largeTables = document.querySelectorAll('table tbody tr');
            
            // 如果表格行数超过100行，启用虚拟化
            if (largeTables.length > 100) {
                console.log('检测到大型表格，启用虚拟化...');
                this.virtualizeTable(largeTables[0].closest('table'));
            }
        },
        
        virtualizeTable: function(table) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const visibleRows = 50; // 可见行数
            let scrollTop = 0;
            
            // 创建虚拟化容器
            const virtualContainer = document.createElement('div');
            virtualContainer.style.overflow = 'auto';
            virtualContainer.style.height = '400px';
            
            // 创建占位符
            const placeholder = document.createElement('div');
            placeholder.style.height = `${rows.length * 40}px`; // 假设每行40px
            
            // 渲染可见行
            const renderVisibleRows = () => {
                const startIndex = Math.floor(scrollTop / 40);
                const endIndex = Math.min(startIndex + visibleRows, rows.length);
                
                tbody.innerHTML = '';
                for (let i = startIndex; i < endIndex; i++) {
                    tbody.appendChild(rows[i].cloneNode(true));
                }
                
                tbody.style.transform = `translateY(${startIndex * 40}px)`;
            };
            
            virtualContainer.addEventListener('scroll', () => {
                scrollTop = virtualContainer.scrollTop;
                renderVisibleRows();
            });
            
            // 初始渲染
            renderVisibleRows();
            
            // 替换原始表格
            table.parentNode.replaceChild(virtualContainer, table);
            virtualContainer.appendChild(table);
            virtualContainer.appendChild(placeholder);
        }
    },
    
    // 用户体验优化
    ux: {
        init: function() {
            // 添加加载状态指示器
            this.addLoadingIndicators();
            
            // 优化表单体验
            this.enhanceFormExperience();
            
            // 添加键盘导航支持
            this.addKeyboardNavigation();
            
            // 优化移动端体验
            this.optimizeForMobile();
        },
        
        addLoadingIndicators: function() {
            // 为Ajax请求添加加载状态
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                PerformanceEnhancer.ux.showLoadingIndicator();
                
                return originalFetch.apply(this, args)
                    .then(response => {
                        PerformanceEnhancer.ux.hideLoadingIndicator();
                        return response;
                    })
                    .catch(error => {
                        PerformanceEnhancer.ux.hideLoadingIndicator();
                        throw error;
                    });
            };
        },
        
        showLoadingIndicator: function() {
            let indicator = document.getElementById('global-loading-indicator');
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.id = 'global-loading-indicator';
                indicator.innerHTML = `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                `;
                indicator.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    z-index: 9999;
                    background: rgba(255, 255, 255, 0.9);
                    padding: 10px;
                    border-radius: 5px;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                `;
                document.body.appendChild(indicator);
            }
            indicator.style.display = 'block';
        },
        
        hideLoadingIndicator: function() {
            const indicator = document.getElementById('global-loading-indicator');
            if (indicator) {
                indicator.style.display = 'none';
            }
        },
        
        enhanceFormExperience: function() {
            // 自动保存表单数据
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.addEventListener('input', PerformanceEnhancer.debounce.create(() => {
                        this.saveFormData(form);
                    }, 1000));
                });
            });
        },
        
        saveFormData: function(form) {
            const formData = new FormData(form);
            const data = {};
            for (const [key, value] of formData) {
                data[key] = value;
            }
            
            const formId = form.id || 'form_' + Date.now();
            sessionStorage.setItem('form_data_' + formId, JSON.stringify(data));
        },
        
        addKeyboardNavigation: function() {
            // 添加键盘快捷键支持
            document.addEventListener('keydown', (event) => {
                if (event.ctrlKey || event.metaKey) {
                    switch (event.key) {
                        case 'k':
                            event.preventDefault();
                            this.focusSearchInput();
                            break;
                        case 'r':
                            event.preventDefault();
                            this.refreshCurrentView();
                            break;
                    }
                }
            });
        },
        
        focusSearchInput: function() {
            const searchInput = document.querySelector('input[type="search"], input[name="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        },
        
        refreshCurrentView: function() {
            if (typeof refreshCurrentData === 'function') {
                refreshCurrentData();
            } else {
                location.reload();
            }
        },
        
        optimizeForMobile: function() {
            // 移动端优化
            if (window.innerWidth <= 768) {
                // 优化表格显示
                const tables = document.querySelectorAll('table');
                tables.forEach(table => {
                    table.style.fontSize = '12px';
                    table.classList.add('table-responsive');
                });
                
                // 优化按钮大小
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(button => {
                    button.style.minHeight = '44px';
                });
            }
        }
    },
    
    // 初始化性能增强器
    init: function() {
        console.log('初始化性能增强器...');
        
        // 启动性能监控
        this.metrics.init();
        
        // 启用各种优化
        this.lazyLoading.init();
        this.preloading.init();
        this.virtualization.init();
        this.ux.init();
        
        console.log('性能增强器初始化完成');
    },
    
    // 清理资源
    cleanup: function() {
        this.debounce.cleanup();
        
        if (this.lazyLoading.observer) {
            this.lazyLoading.observer.disconnect();
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    PerformanceEnhancer.init();
});

// 页面卸载前清理
window.addEventListener('beforeunload', function() {
    PerformanceEnhancer.cleanup();
});

console.log('性能增强工具已加载');