/**
 * 内存管理工具
 * 防止内存泄漏和优化内存使用
 */

window.MemoryManager = {
    
    // 内存监控配置
    config: {
        maxCacheSize: 50 * 1024 * 1024, // 50MB
        cleanupInterval: 5 * 60 * 1000, // 5分钟
        warningThreshold: 80, // 80%内存使用率警告
        enableMonitoring: true
    },
    
    // 缓存管理
    cache: {
        data: new Map(),
        maxSize: 100, // 最大缓存项数
        
        set: function(key, value, ttl = 300000) { // 默认5分钟TTL
            // 检查缓存大小
            if (this.data.size >= this.maxSize) {
                this.cleanup();
            }
            
            this.data.set(key, {
                value: value,
                timestamp: Date.now(),
                ttl: ttl
            });
        },
        
        get: function(key) {
            const item = this.data.get(key);
            if (!item) return null;
            
            // 检查是否过期
            if (Date.now() - item.timestamp > item.ttl) {
                this.data.delete(key);
                return null;
            }
            
            return item.value;
        },
        
        cleanup: function() {
            const now = Date.now();
            let cleanedCount = 0;
            
            for (const [key, item] of this.data) {
                if (now - item.timestamp > item.ttl) {
                    this.data.delete(key);
                    cleanedCount++;
                }
            }
            
            // 如果清理后仍然超过限制，删除最旧的项
            if (this.data.size >= this.maxSize) {
                const sortedEntries = Array.from(this.data.entries())
                    .sort((a, b) => a[1].timestamp - b[1].timestamp);
                
                const toDelete = sortedEntries.slice(0, Math.floor(this.maxSize * 0.3));
                toDelete.forEach(([key]) => {
                    this.data.delete(key);
                    cleanedCount++;
                });
            }
            
            console.log(`缓存清理完成，删除了 ${cleanedCount} 个项目`);
        },
        
        clear: function() {
            this.data.clear();
            console.log('缓存已清空');
        }
    },
    
    // 事件监听器管理
    eventListeners: {
        registry: new Map(),
        
        add: function(element, event, handler, options = {}) {
            const key = this.generateKey(element, event, handler);
            
            if (!this.registry.has(key)) {
                element.addEventListener(event, handler, options);
                this.registry.set(key, {
                    element: element,
                    event: event,
                    handler: handler,
                    options: options
                });
            }
        },
        
        remove: function(element, event, handler) {
            const key = this.generateKey(element, event, handler);
            const listener = this.registry.get(key);
            
            if (listener) {
                element.removeEventListener(event, handler);
                this.registry.delete(key);
            }
        },
        
        generateKey: function(element, event, handler) {
            return `${element.tagName || 'unknown'}_${event}_${handler.name || 'anonymous'}`;
        },
        
        cleanup: function() {
            let cleanedCount = 0;
            
            for (const [key, listener] of this.registry) {
                try {
                    // 检查元素是否为有效的Node对象且仍在DOM中
                    if (listener.element && 
                        listener.element.nodeType && 
                        !document.contains(listener.element)) {
                        listener.element.removeEventListener(listener.event, listener.handler);
                        this.registry.delete(key);
                        cleanedCount++;
                    }
                } catch (error) {
                    // 如果检查失败，直接删除这个监听器记录
                    console.warn(`清理事件监听器时出错: ${key}`, error);
                    this.registry.delete(key);
                    cleanedCount++;
                }
            }
            
            console.log(`事件监听器清理完成，删除了 ${cleanedCount} 个监听器`);
        },
        
        removeAll: function() {
            for (const [key, listener] of this.registry) {
                listener.element.removeEventListener(listener.event, listener.handler);
            }
            this.registry.clear();
            console.log('所有事件监听器已移除');
        }
    },
    
    // 定时器管理
    timers: {
        intervals: new Set(),
        timeouts: new Set(),
        
        setInterval: function(callback, delay) {
            const id = setInterval(callback, delay);
            this.intervals.add(id);
            return id;
        },
        
        setTimeout: function(callback, delay) {
            const id = setTimeout(() => {
                this.timeouts.delete(id);
                callback();
            }, delay);
            this.timeouts.add(id);
            return id;
        },
        
        clearInterval: function(id) {
            clearInterval(id);
            this.intervals.delete(id);
        },
        
        clearTimeout: function(id) {
            clearTimeout(id);
            this.timeouts.delete(id);
        },
        
        cleanup: function() {
            // 清理所有定时器
            for (const id of this.intervals) {
                clearInterval(id);
            }
            for (const id of this.timeouts) {
                clearTimeout(id);
            }
            
            console.log(`定时器清理完成，清除了 ${this.intervals.size} 个intervals和 ${this.timeouts.size} 个timeouts`);
            
            this.intervals.clear();
            this.timeouts.clear();
        }
    },
    
    // 内存监控
    monitoring: {
        isRunning: false,
        
        start: function() {
            if (this.isRunning) return;
            
            this.isRunning = true;
            this.monitoringInterval = MemoryManager.timers.setInterval(() => {
                this.checkMemoryUsage();
            }, MemoryManager.config.cleanupInterval);
            
            console.log('内存监控已启动');
        },
        
        stop: function() {
            if (!this.isRunning) return;
            
            this.isRunning = false;
            MemoryManager.timers.clearInterval(this.monitoringInterval);
            console.log('内存监控已停止');
        },
        
        checkMemoryUsage: function() {
            try {
                // 检查性能信息（如果可用）
                if (performance.memory) {
                    const memory = performance.memory;
                    const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                    const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
                    const usagePercent = Math.round((usedMB / totalMB) * 100);
                    
                    console.log(`内存使用: ${usedMB}MB / ${totalMB}MB (${usagePercent}%)`);
                    
                    // 检查是否需要清理
                    if (usagePercent > MemoryManager.config.warningThreshold) {
                        console.warn('内存使用率过高，开始清理...');
                        MemoryManager.cleanup();
                        
                        if (typeof DataValidator !== 'undefined') {
                            DataValidator.showUserMessage('系统提示', '正在进行内存优化...', 'info');
                        }
                    }
                }
                
                // 检查缓存大小
                const cacheSize = MemoryManager.cache.data.size;
                if (cacheSize > MemoryManager.cache.maxSize * 0.8) {
                    console.log('缓存使用率过高，开始清理缓存...');
                    MemoryManager.cache.cleanup();
                }
                
            } catch (error) {
                console.error('内存监控检查失败:', error);
            }
        }
    },
    
    // 初始化内存管理
    init: function() {
        console.log('初始化内存管理器...');
        
        // 启动内存监控
        if (this.config.enableMonitoring) {
            this.monitoring.start();
        }
        
        // 页面卸载时清理
        this.eventListeners.add(window, 'beforeunload', () => {
            this.cleanup();
        });
        
        // 页面可见性变化时清理
        this.eventListeners.add(document, 'visibilitychange', () => {
            if (document.hidden) {
                this.cleanup();
            }
        });
        
        console.log('内存管理器初始化完成');
    },
    
    // 全面清理
    cleanup: function() {
        console.log('开始内存清理...');
        
        try {
            // 清理缓存
            this.cache.cleanup();
            
            // 清理事件监听器
            this.eventListeners.cleanup();
            
            // 清理定时器
            this.timers.cleanup();
            
            // 清理全局变量
            this.cleanupGlobalVariables();
            
            // 强制垃圾回收（如果可用）
            if (window.gc) {
                window.gc();
            }
            
            console.log('内存清理完成');
            
        } catch (error) {
            console.error('内存清理失败:', error);
        }
    },
    
    // 清理全局变量
    cleanupGlobalVariables: function() {
        // 清理已知的缓存变量
        if (window.apiDataCache) {
            window.apiDataCache = {};
        }
        
        // 清理图表实例
        if (window.orderChart) {
            try {
                window.orderChart.destroy();
                window.orderChart = null;
            } catch (e) {
                console.warn('清理订单图表失败:', e);
            }
        }
        
        if (window.overdueChart) {
            try {
                window.overdueChart.destroy();
                window.overdueChart = null;
            } catch (e) {
                console.warn('清理逾期图表失败:', e);
            }
        }
        
        // 清理其他缓存
        if (window.sessionStorage) {
            // 清理过期的sessionStorage项
            const keys = Object.keys(sessionStorage);
            keys.forEach(key => {
                if (key.startsWith('api_cache_')) {
                    try {
                        const data = JSON.parse(sessionStorage.getItem(key));
                        if (data.timestamp && Date.now() - data.timestamp > 24 * 60 * 60 * 1000) {
                            sessionStorage.removeItem(key);
                        }
                    } catch (e) {
                        // 无效的缓存项，删除
                        sessionStorage.removeItem(key);
                    }
                }
            });
        }
    },
    
    // 获取内存使用情况
    getMemoryInfo: function() {
        if (!performance.memory) {
            return { available: false };
        }
        
        const memory = performance.memory;
        return {
            available: true,
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
            usage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
        };
    }
};

// 页面加载时初始化内存管理
document.addEventListener('DOMContentLoaded', function() {
    MemoryManager.init();
});

console.log('内存管理工具已加载');