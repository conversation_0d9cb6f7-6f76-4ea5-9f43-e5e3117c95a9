/**
 * 简化版图表实现
 * 替换复杂的图表系统，提供简单可靠的数据可视化
 */

class SimpleChart {
    constructor() {
        this.charts = new Map();
        this.isChartJsLoaded = false;
        this.loadChartJs();
    }

    /**
     * 加载Chart.js库
     */
    async loadChartJs() {
        if (typeof Chart !== 'undefined') {
            this.isChartJsLoaded = true;
            console.log('Chart.js已加载');
            return;
        }

        try {
            // 使用本地Chart.js文件，避免CDN依赖
            const script = document.createElement('script');
            script.src = '/static/vendor/chart.js/chart.min.js';
            script.onload = () => {
                this.isChartJsLoaded = true;
                console.log('Chart.js本地文件加载成功');
                // 触发自定义事件通知图表可以初始化
                document.dispatchEvent(new CustomEvent('chartJsReady'));
            };
            script.onerror = () => {
                console.error('Chart.js本地文件加载失败');
                this.showFallbackMessage();
            };
            document.head.appendChild(script);
        } catch (error) {
            console.error('加载Chart.js时出错:', error);
            this.showFallbackMessage();
        }
    }

    /**
     * 等待Chart.js加载完成
     */
    async waitForChartJs() {
        if (this.isChartJsLoaded) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Chart.js加载超时'));
            }, 10000);

            document.addEventListener('chartJsReady', () => {
                clearTimeout(timeout);
                resolve();
            }, { once: true });
        });
    }

    /**
     * 创建图表
     */
    async createChart(canvasId, data, options = {}) {
        try {
            await this.waitForChartJs();

            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                throw new Error(`Canvas元素 ${canvasId} 不存在`);
            }

            // 销毁已存在的图表
            if (this.charts.has(canvasId)) {
                this.charts.get(canvasId).destroy();
            }

            const ctx = canvas.getContext('2d');
            const chartConfig = {
                type: options.type || 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: options.title || '数据图表'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    ...options.chartOptions
                }
            };

            const chart = new Chart(ctx, chartConfig);
            this.charts.set(canvasId, chart);
            
            console.log(`图表 ${canvasId} 创建成功`);
            return chart;
        } catch (error) {
            console.error(`创建图表 ${canvasId} 失败:`, error);
            this.showErrorMessage(canvasId, '图表加载失败');
            return null;
        }
    }

    /**
     * 更新图表类型
     */
    updateChartType(canvasId, newType) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.config.type = newType;
            chart.update();
        }
    }

    /**
     * 销毁图表
     */
    destroyChart(canvasId) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.destroy();
            this.charts.delete(canvasId);
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(canvasId, message) {
        const canvas = document.getElementById(canvasId);
        if (canvas) {
            const container = canvas.parentElement;
            container.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        <p class="mt-2 mb-0">${message}</p>
                        <small>请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 显示降级消息
     */
    showFallbackMessage() {
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            container.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="bi bi-info-circle"></i>
                    <p class="mb-0">图表功能暂时不可用</p>
                    <small>请检查网络连接或刷新页面</small>
                </div>
            `;
        });
    }

    /**
     * 生成默认数据
     */
    generateDefaultData(type = 'order') {
        const labels = ['1月', '2月', '3月', '4月', '5月', '6月'];
        const data = type === 'order' ? [120, 190, 300, 500, 200, 300] : [20, 30, 40, 50, 30, 40];
        const label = type === 'order' ? '订单数量' : '逾期数量';
        const color = type === 'order' ? 'rgba(54, 162, 235, 0.6)' : 'rgba(255, 99, 132, 0.6)';
        const borderColor = type === 'order' ? 'rgba(54, 162, 235, 1)' : 'rgba(255, 99, 132, 1)';

        return {
            labels: labels,
            datasets: [{
                label: label,
                data: data,
                backgroundColor: color,
                borderColor: borderColor,
                borderWidth: 1
            }]
        };
    }
}

// 创建全局实例
window.SimpleChart = new SimpleChart();

// 汇总页面图表初始化函数
window.initSummaryCharts = function() {
    console.log('开始初始化汇总页面图表');
    
    // 获取服务器数据或使用默认数据
    const orderData = window.orderChartData && window.orderChartData.labels ? 
        window.orderChartData : window.SimpleChart.generateDefaultData('order');
    
    const overdueData = window.overdueChartData && window.overdueChartData.labels ? 
        window.overdueChartData : window.SimpleChart.generateDefaultData('overdue');
    
    // 创建订单图表
    window.SimpleChart.createChart('orderChart', orderData, {
        title: '订单月度汇总',
        type: 'bar'
    });
    
    // 创建逾期图表
    window.SimpleChart.createChart('overdueChart', overdueData, {
        title: '逾期月度汇总',
        type: 'bar'
    });
    
    // 设置图表类型切换器
    setupChartTypeSelectors();
};

// 图表类型切换器
function setupChartTypeSelectors() {
    const selectors = document.querySelectorAll('.chart-type-selector');
    selectors.forEach(selector => {
        selector.addEventListener('change', function() {
            const chartId = this.getAttribute('data-chart');
            const newType = this.value;
            window.SimpleChart.updateChartType(chartId, newType);
        });
    });
}

// 导出图表功能
window.exportCharts = function() {
    const charts = ['orderChart', 'overdueChart'];
    charts.forEach(chartId => {
        const chart = window.SimpleChart.charts.get(chartId);
        if (chart) {
            const url = chart.toBase64Image();
            const link = document.createElement('a');
            link.download = `${chartId}.png`;
            link.href = url;
            link.click();
        }
    });
};

console.log('简化版图表系统已加载');