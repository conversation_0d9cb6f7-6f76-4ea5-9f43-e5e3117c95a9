/**
 * 表格管理模块
 * 负责处理汇总页面所有表格的初始化、响应式处理和增强功能
 */

class TableManager {
    constructor() {
        this.tables = new Map();
        this.isInitialized = false;
        
        // 绑定方法上下文
        this.handleResize = this.handleResize.bind(this);
        this.handleCellClick = this.handleCellClick.bind(this);
    }

    /**
     * 初始化表格管理器
     */
    initialize() {
        console.log('TableManager: 开始初始化表格管理器');
        
        // 延迟初始化以确保DOM完全加载
        setTimeout(() => {
            this.initializeTables();
            this.enhanceTableCells();
            this.setupEventListeners();
            this.isInitialized = true;
        }, 200);
    }

    /**
     * 初始化所有表格
     */
    initializeTables() {
        console.log('TableManager: 开始初始化表格');
        
        const tables = document.querySelectorAll('.data-table');
        console.log(`TableManager: 找到 ${tables.length} 个表格需要初始化`);
        
        tables.forEach((table, index) => {
            this.initializeTable(table, index);
        });
    }

    /**
     * 初始化单个表格
     * @param {HTMLElement} table - 表格元素
     * @param {number} index - 表格索引
     */
    initializeTable(table, index) {
        console.log(`TableManager: 初始化表格 #${index + 1}`);
        
        try {
            // 检查表格是否已被初始化
            if ($.fn.DataTable.isDataTable(table)) {
                $(table).DataTable().destroy();
                console.log(`TableManager: 表格 #${index + 1} 已存在，已销毁旧实例`);
            }

            // 生成唯一的表格ID
            const tableId = `data-table-${index}`;
            if (!table.id) {
                table.id = tableId;
            }

            // 初始化DataTable
            const dataTable = $(table).DataTable({
                responsive: {
                    details: {
                        type: 'column',
                        target: 0
                    }
                },
                columnDefs: [
                    {
                        className: 'dtr-control',
                        orderable: false,
                        targets: 0,
                        width: "40px"
                    },
                    {
                        targets: '_all',
                        className: 'dt-head-center dt-body-center'
                    }
                ],
                dom: '<"dataTable-top"lf>rt<"dataTable-bottom"ip>',
                pageLength: 25,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]],
                language: {
                    search: "搜索:",
                    lengthMenu: "显示 _MENU_ 条数据",
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    },
                    emptyTable: "表中数据为空",
                    zeroRecords: "没有找到符合条件的数据"
                },
                order: [[1, 'asc']], // 按第二列排序，避开控制列
                autoWidth: false,
                processing: true,
                deferRender: true
            });

            // 存储表格实例
            this.tables.set(tableId, dataTable);
            
            console.log(`TableManager: 表格 #${index + 1} 初始化成功`);
        } catch (err) {
            console.error(`TableManager: 表格 #${index + 1} 初始化失败:`, err);
        }
    }

    /**
     * 增强表格单元格功能
     * 添加单元格折叠/展开功能
     */
    enhanceTableCells() {
        console.log('TableManager: 开始增强表格单元格');
        
        try {
            // 为所有数据单元格添加增强功能
            const dataCells = document.querySelectorAll('.data-table td:not(.dtr-control)');
            
            dataCells.forEach(cell => {
                // 跳过已处理的单元格
                if (cell.querySelector('.cell-content')) {
                    return;
                }
                
                const originalContent = cell.innerHTML.trim();
                
                // 只对内容较长的单元格进行处理
                if (originalContent.length > 20) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'cell-content';
                    wrapper.innerHTML = originalContent;
                    wrapper.title = cell.textContent; // 添加工具提示
                    
                    // 清空原始内容并添加包装器
                    cell.innerHTML = '';
                    cell.appendChild(wrapper);
                }
            });
            
            console.log('TableManager: 表格单元格增强完成');
        } catch (err) {
            console.error('TableManager: 表格单元格增强失败:', err);
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 单元格点击事件
        document.addEventListener('click', this.handleCellClick);
        
        // 窗口大小变化事件
        window.addEventListener('resize', this.handleResize);
    }

    /**
     * 处理单元格点击事件
     * @param {Event} event - 点击事件
     */
    handleCellClick(event) {
        const cellContent = event.target.closest('.cell-content');
        if (!cellContent) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // 切换展开/折叠状态
        cellContent.classList.toggle('expanded');
        
        // 重新计算表格布局
        setTimeout(() => {
            this.tables.forEach(table => {
                try {
                    table.columns.adjust().responsive.recalc();
                } catch (err) {
                    console.warn('TableManager: 调整表格布局时出错:', err);
                }
            });
        }, 100);
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            try {
                // 重新计算所有表格布局
                this.tables.forEach((table, tableId) => {
                    table.columns.adjust().responsive.recalc();
                });
                
                // 重新应用移动端优化
                this.optimizeForMobile();
                
                console.log('TableManager: 窗口大小变化处理完成');
            } catch (err) {
                console.error('TableManager: 窗口大小变化处理失败:', err);
            }
        }, 250);
    }

    /**
     * 移动端优化
     */
    optimizeForMobile() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移动端特殊处理
            document.querySelectorAll('.cell-content').forEach(cell => {
                if (!cell.classList.contains('mobile-optimized')) {
                    cell.style.maxWidth = '120px';
                    cell.classList.add('mobile-optimized');
                }
            });
            
            // 调整表格布局
            document.querySelectorAll('.data-table').forEach(table => {
                table.style.fontSize = '0.85rem';
            });
        } else {
            // 桌面端恢复
            document.querySelectorAll('.cell-content').forEach(cell => {
                if (cell.classList.contains('mobile-optimized')) {
                    cell.style.maxWidth = '150px';
                    cell.classList.remove('mobile-optimized');
                }
            });
            
            document.querySelectorAll('.data-table').forEach(table => {
                table.style.fontSize = '';
            });
        }
    }

    /**
     * 刷新所有表格
     */
    refreshTables() {
        console.log('TableManager: 刷新所有表格');
        
        this.tables.forEach((table, tableId) => {
            try {
                table.ajax.reload(null, false); // 保持当前页
            } catch (err) {
                // 如果不是AJAX表格，则重新绘制
                table.draw();
            }
        });
    }

    /**
     * 获取表格实例
     * @param {string} tableId - 表格ID
     * @returns {Object|null} DataTable实例
     */
    getTable(tableId) {
        return this.tables.get(tableId) || null;
    }

    /**
     * 添加新表格
     * @param {HTMLElement} tableElement - 表格元素
     * @returns {string} 表格ID
     */
    addTable(tableElement) {
        const index = this.tables.size;
        this.initializeTable(tableElement, index);
        return tableElement.id;
    }

    /**
     * 移除表格
     * @param {string} tableId - 表格ID
     */
    removeTable(tableId) {
        const table = this.tables.get(tableId);
        if (table) {
            try {
                table.destroy();
                this.tables.delete(tableId);
                console.log(`TableManager: 表格 ${tableId} 已移除`);
            } catch (err) {
                console.error(`TableManager: 移除表格 ${tableId} 失败:`, err);
            }
        }
    }

    /**
     * 导出表格数据
     * @param {string} tableId - 表格ID
     * @param {string} format - 导出格式 ('csv', 'excel')
     */
    exportTable(tableId, format = 'csv') {
        const table = this.tables.get(tableId);
        if (!table) {
            console.error(`TableManager: 找不到表格 ${tableId}`);
            return;
        }

        try {
            const data = table.data().toArray();
            const headers = table.columns().header().toArray().map(th => th.textContent);
            
            if (format === 'csv') {
                this.exportAsCSV(data, headers, tableId);
            } else if (format === 'excel') {
                this.exportAsExcel(data, headers, tableId);
            }
        } catch (err) {
            console.error('TableManager: 导出表格数据失败:', err);
        }
    }

    /**
     * 导出为CSV
     * @param {Array} data - 表格数据
     * @param {Array} headers - 表头
     * @param {string} tableId - 表格ID
     */
    exportAsCSV(data, headers, tableId) {
        let csvContent = "data:text/csv;charset=utf-8,\uFEFF"; // BOM for UTF-8
        
        // 添加表头
        csvContent += headers.join(",") + "\r\n";
        
        // 添加数据行
        data.forEach(row => {
            const cleanedRow = Array.from(row).map(cell => {
                return typeof cell === 'string' ? cell.replace(/"/g, '""') : cell;
            });
            csvContent += cleanedRow.join(",") + "\r\n";
        });
        
        // 创建下载链接
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `${tableId}_${new Date().toISOString().slice(0, 10)}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * 销毁所有表格实例
     */
    destroy() {
        console.log('TableManager: 开始销毁所有表格实例');
        
        // 移除事件监听器
        document.removeEventListener('click', this.handleCellClick);
        window.removeEventListener('resize', this.handleResize);
        
        // 销毁所有表格
        this.tables.forEach((table, tableId) => {
            try {
                table.destroy();
            } catch (err) {
                console.warn(`TableManager: 销毁表格 ${tableId} 时出错:`, err);
            }
        });
        
        this.tables.clear();
        this.isInitialized = false;
        
        console.log('TableManager: 所有表格实例已销毁');
    }

    /**
     * 获取管理器状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            tableCount: this.tables.size,
            tables: Array.from(this.tables.keys())
        };
    }
}

// 导出模块
window.TableManager = TableManager;