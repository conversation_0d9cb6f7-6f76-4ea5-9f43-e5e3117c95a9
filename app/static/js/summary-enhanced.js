/**
 * 增强型数据汇总展示功能
 * 提供多视图、分组展示和数据可视化功能
 */

// 业务指标分组定义
const metricGroups = {
    'order': ['总台数', '电商订单数', '租赁订单数', '逾期订单数', '已完成订单'],
    'receivables': ['总待收', '租赁待收', '电商待收'],
    'fees': ['增值费', '延保服务', '首付款', '租金', '尾款'],
    'business': ['放款', '复投', '租赁', '电商', '供应商利润', '成本'],
    'performance': ['电商业绩', '租赁业绩', '实际出资'],
    'overdue': ['逾期本金', '逾期总待收']
};

// 获取分组名称的翻译
function getGroupName(groupKey) {
    const groupNames = {
        'order': '订单指标',
        'receivables': '收款指标',
        'fees': '费用指标',
        'business': '业务指标',
        'performance': '业绩指标',
        'overdue': '逾期指标'
    };
    return groupNames[groupKey] || groupKey;
}

// 数据格式化工具函数
function formatNumber(value) {
    if (value === 0 || value === '0' || value === null || value === undefined) return '-';
    return new Intl.NumberFormat('zh-CN').format(value);
}

function formatCurrency(value) {
    if (value === 0 || value === '0' || value === null || value === undefined) return '-';
    // 保留两位小数并使用千分位分隔符
    return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
}

function isInteger(value) {
    return Number.isInteger(Number(value));
}

// 计算指标组的总和
function getTotalByGroup(group, rowData, headers) {
    let total = 0;
    metricGroups[group].forEach(metric => {
        const index = headers.indexOf(metric);
        if (index > 0) {
            total += parseFloat(rowData[index]) || 0;
        }
    });
    return total;
}

// 判断值是否为负数并添加类名
function getValueClass(value) {
    return parseFloat(value) < 0 ? 'negative-value' : '';
}

// 初始化函数
function initSummaryData(data) {
    // 转换数据结构
    const headers = data.headers;
    const summary = data.summary;
    const timingStats = data.timing_stats || {};
    
    // 提取所有店铺名称，包括累计行
    const allStores = [...new Set(summary.map(row => row[0]))];
    
    // 区分普通店铺和累计店铺
    const regularStores = allStores.filter(store => !store.includes('累计'));
    const accumulatedStores = allStores.filter(store => store.includes('累计'));
    
    // 将所有店铺合并，确保累计数据排在后面
    const stores = [...regularStores, ...accumulatedStores];
    
    // 设置全局数据
    window.summaryData = {
        headers: headers,
        summary: summary,
        stores: stores,
        storeData: stores.map(store => 
            summary.find(row => row[0] === store)
        ),
        timingStats: timingStats
    };
    
    // 默认显示店铺选项卡视图
    switchDataView('tabs');
    
    // 为视图选择器添加事件监听
    document.querySelectorAll('[data-view]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelectorAll('[data-view]').forEach(el => {
                el.parentElement.classList.remove('active');
            });
            this.parentElement.classList.add('active');
            switchDataView(this.getAttribute('data-view'));
        });
    });
    
    // 初始化导出功能
    document.getElementById('exportSummaryData')?.addEventListener('click', exportSummaryData);
}

// 视图切换处理函数
function switchDataView(viewType) {
    const container = document.getElementById('summaryDataContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (viewType === 'tabs') {
        renderTabsView(container);
    } else if (viewType === 'table') {
        renderTableView(container);
    } else if (viewType === 'cards') {
        renderCardsView(container);
    }
}

// 渲染API执行时间统计数据
function renderTimingStats(timingStats) {
    if (!timingStats || Object.keys(timingStats).length === 0) {
        return '';
    }
    
    let html = `
        <div class="card border-light">
            <div class="card-header bg-light text-muted">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="mb-0">API执行统计数据</small>
                    <span class="badge bg-secondary">总耗时: ${timingStats['总耗时'] || 'N/A'}</span>
                </div>
            </div>
            <div class="card-body p-2">
                <div class="row">
    `;
    
    // 生成每项统计数据
    Object.entries(timingStats).forEach(([key, value]) => {
        if (key === '总耗时') return; // 总耗时已在标题中显示
        
        html += `
            <div class="col-lg-3 col-md-4 col-sm-6 mb-2">
                <div class="d-flex justify-content-between">
                    <small class="text-muted">${key}:</small>
                    <small class="text-secondary">${value}</small>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;
    
    return html;
}

// 选项卡视图渲染函数
function renderTabsView(container) {
    const { headers, stores, storeData } = window.summaryData;
    
    let html = `
        <!-- 店铺导航选项卡 -->
        <ul class="nav nav-tabs" id="storesTabs" role="tablist">
    `;
    
    // 生成店铺选项卡
    stores.forEach((store, index) => {
        html += `
            <li class="nav-item" role="presentation">
                <button class="nav-link ${index === 0 ? 'active' : ''}" 
                        id="store-${index}-tab" 
                        data-bs-toggle="tab" 
                        data-bs-target="#store-${index}" 
                        type="button" role="tab">
                    ${store}
                </button>
            </li>
        `;
    });
    
    html += `
        </ul>
        
        <!-- 选项卡内容 -->
        <div class="tab-content mt-3" id="storesTabsContent">
    `;
    
    // 生成每个店铺的内容
    stores.forEach((store, storeIndex) => {
        html += `
            <div class="tab-pane fade ${storeIndex === 0 ? 'show active' : ''}" 
                 id="store-${storeIndex}" 
                 role="tabpanel">
                
                <!-- 关键指标卡片 -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white mb-2" style="font-size: 1.1rem; font-weight: 500; letter-spacing: 0.5px;">总台数</h6>
                                        <h3 class="mt-2 mb-0 text-white" style="font-size: 1.8rem; font-weight: 600;">${formatNumber(storeData[storeIndex][headers.indexOf('总台数')])}</h3>
                                    </div>
                                    <div class="stat-icon">
                                        <i class="bi bi-cart-check text-white-50" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white mb-2" style="font-size: 1.1rem; font-weight: 500; letter-spacing: 0.5px;">总待收</h6>
                                        <h3 class="mt-2 mb-0 text-white" style="font-size: 1.8rem; font-weight: 600;">¥${formatCurrency(storeData[storeIndex][headers.indexOf('总待收')])}</h3>
                                    </div>
                                    <div class="stat-icon">
                                        <i class="bi bi-cash-stack text-white-50" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white mb-2" style="font-size: 1.1rem; font-weight: 500; letter-spacing: 0.5px;">实际出资</h6>
                                        <h3 class="mt-2 mb-0 text-white" style="font-size: 1.8rem; font-weight: 600;">¥${formatCurrency(
                                            parseFloat(storeData[storeIndex][headers.indexOf('实际出资')]) || 0
                                        )}</h3>
                                    </div>
                                    <div class="stat-icon">
                                        <i class="bi bi-graph-up text-white-50" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white mb-2" style="font-size: 1.1rem; font-weight: 500; letter-spacing: 0.5px;">逾期本金</h6>
                                        <h3 class="mt-2 mb-0 text-white" style="font-size: 1.8rem; font-weight: 600;">¥${formatCurrency(Math.abs(parseFloat(storeData[storeIndex][headers.indexOf('逾期本金')]) || 0))}</h3>
                                    </div>
                                    <div class="stat-icon">
                                        <i class="bi bi-exclamation-triangle text-white-50" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 指标分组选项卡 -->
                <ul class="nav nav-pills mb-3" id="metrics-${storeIndex}-tabs" role="tablist">
        `;
        
        // 生成指标分组选项卡
        Object.keys(metricGroups).forEach((group, groupIndex) => {
            // 为不同类型的指标选项卡添加不同的颜色样式
            let tabColorClass = '';
            if (group === 'order') {
                tabColorClass = 'text-primary';  // 蓝色 - 订单指标
            } else if (group === 'receivables') {
                tabColorClass = 'text-success';  // 绿色 - 收款指标
            } else if (group === 'fees') {
                tabColorClass = 'text-info';     // 浅蓝色 - 费用指标
            } else if (group === 'business') {
                tabColorClass = 'text-secondary'; // 灰色 - 业务指标
            } else if (group === 'performance') {
                tabColorClass = 'text-warning';  // 黄色 - 业绩指标
            } else if (group === 'overdue') {
                tabColorClass = 'text-danger';   // 红色 - 逾期指标
            }
            
            html += `
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${groupIndex === 0 ? 'active' : ''} ${tabColorClass}" 
                            id="${group}-${storeIndex}-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#${group}-${storeIndex}" 
                            type="button" role="tab">
                        ${getGroupName(group)}
                    </button>
                </li>
            `;
        });
        
        html += `
                </ul>
                
                <!-- 指标分组内容 -->
                <div class="tab-content" id="metrics-${storeIndex}-content">
        `;
        
        // 生成每个指标分组的内容
        Object.keys(metricGroups).forEach((group, groupIndex) => {
            html += `
                <div class="tab-pane fade ${groupIndex === 0 ? 'show active' : ''}" 
                     id="${group}-${storeIndex}" 
                     role="tabpanel">
                    
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>指标</th>
                                    <th class="text-end">数值</th>
                                    <th class="text-end">占比</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            // 生成每个指标的行
            metricGroups[group].forEach(metric => {
                const metricIndex = headers.indexOf(metric);
                if (metricIndex > 0) {
                    const value = storeData[storeIndex][metricIndex];
                    let percentage = 0;
                    
                    // 计算占比逻辑，根据指标类型确定分母
                    if (group === 'receivables') {
                        percentage = (parseFloat(value) / parseFloat(storeData[storeIndex][2])) * 100;
                    } else if (group === 'order') {
                        // 订单指标组的占比计算
                        const totalOrders = parseFloat(storeData[storeIndex][headers.indexOf('总台数')]) || 1;
                        percentage = (parseFloat(value) / totalOrders) * 100;
                    } else if (group === 'fees' || group === 'business' || group === 'performance') {
                        const total = getTotalByGroup(group, storeData[storeIndex], headers);
                        if (total !== 0) {
                            percentage = (parseFloat(value) / total) * 100;
                        }
                    }
                    
                    html += `
                        <tr>
                            <td>${metric}</td>
                            <td class="text-end ${getValueClass(value)}">${isNaN(value) ? '-' : isInteger(value) ? formatNumber(value) : formatCurrency(value)}</td>
                            <td class="text-end">
                    `;
                    
                    if (percentage !== 0 && !isNaN(percentage)) {
                        html += `
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" role="progressbar" style="width: ${Math.min(Math.abs(percentage), 100)}%" 
                                     aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            ${percentage.toFixed(1)}%
                        `;
                    } else {
                        html += '-';
                    }
                    
                    html += `
                            </td>
                        </tr>
                    `;
                }
            });
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    });
    
    html += `
        </div>
        
        <!-- API性能统计 -->
        <div class="mt-4">
            ${renderTimingStats(window.summaryData.timingStats)}
        </div>
    `;
    
    container.innerHTML = html;
    
    // 初始化Bootstrap选项卡
    const tabElements = container.querySelectorAll('[data-bs-toggle="tab"]');
    tabElements.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('data-bs-target'));
            
            // 移除同级所有选项卡的active类
            const parent = this.parentElement.parentElement;
            parent.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            
            // 移除同级所有内容的active类
            const targetParent = target.parentElement;
            targetParent.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });
            
            // 添加当前选项卡和内容的active类
            this.classList.add('active');
            target.classList.add('show', 'active');
        });
    });
}

// 表格视图渲染函数
function renderTableView(container) {
    const { headers, summary } = window.summaryData;
    
    // 创建一个新的headers数组，包含控制列
    const tableHeaders = ['', ...headers];
    
    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover data-table" id="fullSummaryTable" style="width:100%">
                <thead>
                    <tr>
    `;
    
    // 生成表头
    tableHeaders.forEach((header, index) => {
        if (index === 0) {
            html += `<th style="width: 40px"></th>`;
        } else {
            html += `<th>${header}</th>`;
        }
    });
    
    html += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    // 生成数据行
    summary.forEach(row => {
        html += `<tr>`;
        // 首先添加控制列
        html += `<td></td>`;
        
        // 然后添加数据列
        row.forEach((cell, cellIndex) => {
            const isNumeric = cellIndex > 0 && !isNaN(parseFloat(cell));
            
            if (isInteger(cell)) {
                // 整数列
                html += `<td class="${isNumeric ? 'text-end' : ''} ${getValueClass(cell)}">${formatNumber(cell)}</td>`;
            } else if (isNumeric) {
                // 金额列
                html += `<td class="${isNumeric ? 'text-end' : ''} ${getValueClass(cell)}">${formatCurrency(cell)}</td>`;
            } else {
                // 其他类型
                html += `<td class="${isNumeric ? 'text-end' : ''} ${getValueClass(cell)}">${cell}</td>`;
            }
        });
        html += `</tr>`;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = html;
    
    // 初始化DataTable
    if ($.fn.DataTable) {
        try {
            const table = $('#fullSummaryTable').DataTable({
                responsive: {
                    details: {
                        type: 'column',
                        target: 0
                    }
                },
                columnDefs: [
                    {
                        className: 'dtr-control',
                        orderable: false,
                        targets: 0,
                        width: "40px"
                    },
                    {
                        targets: '_all',
                        className: 'dt-head-center dt-body-center'
                    }
                ],
                dom: '<"dataTable-top"lf>rt<"dataTable-bottom"ip>',
                language: {
                    search: "搜索:",
                    lengthMenu: "显示 _MENU_ 条数据",
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    }
                },
                order: [[1, 'asc']]  // 按照第二列（索引为1）排序，而不是第一列（控制列）
            });
            
            // 应用优化的数字格式和响应式设计
            if (window.enhanceDataTables) {
                window.enhanceDataTables();
            }
        } catch (e) {
            console.error('初始化DataTable失败:', e);
        }
    }
}

// 卡片视图渲染函数
function renderCardsView(container) {
    const { headers, stores, storeData } = window.summaryData;
    
    let html = `
        <div class="store-selector mb-4">
            <label class="form-label">选择店铺:</label>
            <select class="form-select" id="storeSelector">
    `;
    
    // 生成店铺选择器
    stores.forEach((store, index) => {
        html += `<option value="${index}">${store}</option>`;
    });
    
    html += `
            </select>
        </div>
        
        <div id="metricsCards" class="row">
    `;
    
    // 默认展示第一个店铺的所有指标
    const firstStoreIndex = 0;
    
    // 生成所有指标的卡片
    headers.forEach((header, headerIndex) => {
        if (headerIndex === 0) return; // 跳过店铺名称列
        
        const value = storeData[firstStoreIndex][headerIndex];
        const isNegative = parseFloat(value) < 0;
        
        html += `
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4" data-metric="${headerIndex}">
                <div class="metric-card">
                    <div class="card-body">
                        <h6 class="card-title text-muted">${header}</h6>
                        <div class="metric-value my-3 ${isNegative ? 'negative-value' : ''}">
                            ${headerIndex === 1 ? '' : '¥'}${isInteger(value) ? formatNumber(value) : formatCurrency(value)}
                        </div>
                        <div class="small text-muted">
                            ${generateMetricDescription(header, value)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
        </div>
    `;
    
    container.innerHTML = html;
    
    // 添加店铺选择器事件监听
    document.getElementById('storeSelector')?.addEventListener('change', function() {
        updateCardsData(parseInt(this.value));
    });
}

// 更新卡片数据
function updateCardsData(storeIndex) {
    const { headers, storeData } = window.summaryData;
    const cards = document.querySelectorAll('#metricsCards [data-metric]');
    
    cards.forEach(card => {
        const metricIndex = parseInt(card.getAttribute('data-metric'));
        const value = storeData[storeIndex][metricIndex];
        const isNegative = parseFloat(value) < 0;
        
        const valueElement = card.querySelector('.metric-value');
        if (valueElement) {
            valueElement.className = `metric-value my-3 ${isNegative ? 'negative-value' : ''}`;
            valueElement.innerHTML = `${metricIndex === 1 ? '' : '¥'}${isInteger(value) ? formatNumber(value) : formatCurrency(value)}`;
        }
        
        const descElement = card.querySelector('.small');
        if (descElement) {
            descElement.innerHTML = generateMetricDescription(headers[metricIndex], value);
        }
    });
}

// 生成指标描述
function generateMetricDescription(metricName, value) {
    // 根据指标类型生成不同的描述文本
    if (metricName === '总订单数') {
        return '总订单统计';
    } else if (metricName.includes('待收')) {
        return '待收账款总额';
    } else if (metricName.includes('业绩')) {
        return '本期业绩计算';
    } else if (metricName === '逾期金额') {
        return parseFloat(value) < 0 ? `<span class="text-danger">有逾期风险</span>` : '无逾期风险';
    } else {
        return '数据统计';
    }
}

// 导出汇总数据
function exportSummaryData() {
    const { headers, summary } = window.summaryData;
    
    // 创建CSV内容
    let csvContent = "data:text/csv;charset=utf-8,\uFEFF"; // 添加BOM支持中文
    
    // 添加表头
    csvContent += headers.join(",") + "\r\n";
    
    // 添加数据行
    summary.forEach(row => {
        // 清理每个单元格中可能的HTML标签
        const cleanedRow = row.map(cell => stripHtml(cell));
        csvContent += cleanedRow.join(",") + "\r\n";
    });
    
    // 创建下载链接
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `汇总数据_${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    
    // 模拟点击下载
    link.click();
    
    // 移除链接
    document.body.removeChild(link);
}

// 从HTML内容中提取纯文本
function stripHtml(html) {
    // 如果不是字符串，直接返回
    if (typeof html !== 'string') {
        return html;
    }
    
    // 创建临时DOM元素
    const tempElement = document.createElement('div');
    tempElement.innerHTML = html;
    
    // 获取元素中的纯文本
    return tempElement.textContent || tempElement.innerText || html;
}

// 初始化函数
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有Bootstrap dropdown组件
    try {
        var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
        dropdownElementList.forEach(function(dropdownToggleEl) {
            if (typeof bootstrap !== 'undefined') {
                new bootstrap.Dropdown(dropdownToggleEl);
            }
        });
    } catch(e) {
        console.error('初始化下拉菜单失败:', e);
    }
    
    // 检查是否存在汇总数据
    const summaryDataElement = document.getElementById('summaryData');
    if (summaryDataElement) {
        try {
            let data;
            if (typeof DataValidator !== 'undefined') {
                data = DataValidator.safeJsonParse(summaryDataElement.textContent, null, 'Summary-Enhanced汇总数据');
                
                if (data) {
                    // 验证数据格式
                    const validation = DataValidator.validateTableData(data, 'Summary-Enhanced');
                    if (!validation.valid) {
                        console.error('Summary-Enhanced汇总数据验证失败:', validation.errors);
                        DataValidator.showUserMessage('数据验证', 'Summary-Enhanced汇总数据格式错误', 'warning');
                    }
                }
            } else {
                // 降级处理
                data = JSON.parse(summaryDataElement.textContent);
            }
            
            if (data) {
                initSummaryData(data);
            }
        } catch (e) {
            console.error('解析汇总数据失败:', e);
            if (typeof DataValidator !== 'undefined') {
                DataValidator.showUserMessage('数据解析', 'Summary-Enhanced汇总数据解析失败', 'error');
            }
        }
    }
});
