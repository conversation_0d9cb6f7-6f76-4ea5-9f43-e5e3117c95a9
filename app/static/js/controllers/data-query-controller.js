/**
 * 数据查询页面控制器
 * 统一管理标签页、数据加载和表格初始化
 */
const DataQueryController = {
    // 配置
    config: {
        tabs: {
            filter: {
                id: 'filter',
                apiEndpoint: 'filter_data',
                paramKey: 'date',
                emptyMessage: '请选择日期并点击筛选按钮查询数据',
                defaultOrder: [[1, 'desc']]
            },
            overdue: {
                id: 'overdue',
                apiEndpoint: 'filter_overdue_orders',
                emptyMessage: '暂无逾期订单数据',
                defaultOrder: [[1, 'desc']]
            },
            customer: {
                id: 'customer',
                apiEndpoint: 'filter_orders_by_customer_name',
                paramKey: 'customer_name',
                emptyMessage: '请输入客户姓名查询数据',
                defaultOrder: [[1, 'desc']]
            }
        },
        selectors: {
            tabContainer: '#dataTabs',
            contentContainer: '#dataTabsContent',
            loadingOverlay: '#loadingOverlay'
        }
    },
    
    // 存储当前状态
    state: {
        activeTab: null,
        loadedTabs: {},
        urlParams: null
    },
    
    // 初始化
    init: function() {
        console.log('初始化数据查询控制器');
        
        // 解析URL参数
        this.state.urlParams = new URLSearchParams(window.location.search);
        
        // 注册事件监听器
        this.registerEvents();
        
        // 处理URL参数初始化标签页
        this.initTabFromUrl();
    },
    
    // 注册事件监听器
    registerEvents: function() {
        // 监听标签页切换事件
        const tabContainer = document.querySelector(this.config.selectors.tabContainer);
        if (tabContainer) {
            tabContainer.addEventListener('shown.bs.tab', this.handleTabChange.bind(this));
        }
        
        // 监听窗口大小变化事件
        window.addEventListener('resize', this.handleWindowResize.bind(this));
    },
    
    // 从URL参数初始化标签页
    initTabFromUrl: function() {
        // 获取URL中的tab参数
        const tabParam = this.state.urlParams.get('tab');
        
        // 如果URL中有tab参数，激活对应的选项卡
        if (tabParam && this.config.tabs[tabParam]) {
            console.log('从URL参数初始化标签页: ' + tabParam);
            
            // 获取对应的标签页元素
            const tabElement = document.getElementById(tabParam + '-tab');
            if (tabElement) {
                // 使用Bootstrap的Tab API激活选项卡
                const tab = new bootstrap.Tab(tabElement);
                tab.show();
                
                // 更新当前活动的标签页
                this.state.activeTab = tabParam;
                
                // 加载标签页数据
                this.loadTabData(tabParam);
            }
        } else {
            // 默认加载第一个标签页
            this.loadDefaultTab();
        }
    },
    
    // 加载默认标签页
    loadDefaultTab: function() {
        // 获取第一个标签页
        const firstTabElement = document.querySelector(this.config.selectors.tabContainer + ' [data-bs-toggle="tab"]');
        if (firstTabElement) {
            const tabId = firstTabElement.getAttribute('data-bs-target').substring(1);
            
            // 激活第一个标签页
            const tab = new bootstrap.Tab(firstTabElement);
            tab.show();
            
            // 更新当前活动的标签页
            this.state.activeTab = tabId;
            
            // 加载标签页数据
            this.loadTabData(tabId);
        }
    },
    
    // 处理标签页切换事件
    handleTabChange: function(event) {
        // 获取目标标签页ID
        const targetId = event.target.getAttribute('data-bs-target').substring(1);
        console.log('标签页切换到: ' + targetId);
        
        // 更新当前活动的标签页
        this.state.activeTab = targetId;
        
        // 更新URL，但不刷新页面
        this.updateUrlWithoutReload({ tab: targetId });
        
        // 加载标签页数据
        this.loadTabData(targetId);
    },
    
    // 处理窗口大小变化事件
    handleWindowResize: function() {
        // 防抖处理
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            // 如果有活动的标签页，重新调整表格
            if (this.state.activeTab) {
                this.adjustActiveTable();
            }
        }, 250);
    },
    
    // 调整当前活动表格的布局
    adjustActiveTable: function() {
        if (!this.state.activeTab) return;
        
        const tabContent = document.getElementById(this.state.activeTab);
        if (!tabContent) return;
        
        const tableElement = tabContent.querySelector('.data-table');
        if (!tableElement) return;
        
        // 如果表格已经初始化为DataTable，调整列宽和响应式布局
        if ($.fn.DataTable.isDataTable(tableElement)) {
            console.log('调整表格布局: ' + this.state.activeTab);
            $(tableElement).DataTable().columns.adjust().responsive.recalc();
        }
    },
    
    // 更新URL参数但不刷新页面
    updateUrlWithoutReload: function(params) {
        const url = new URL(window.location.href);
        
        // 更新URL参数
        Object.keys(params).forEach(key => {
            url.searchParams.set(key, params[key]);
        });
        
        // 使用History API更新URL，不刷新页面
        window.history.replaceState({}, '', url.toString());
        
        // 更新内部存储的URL参数
        this.state.urlParams = url.searchParams;
    },
    
    // 加载标签页数据
    loadTabData: function(tabId) {
        // 如果不是有效的标签页ID，返回
        if (!this.config.tabs[tabId]) {
            console.error('无效的标签页ID: ' + tabId);
            return;
        }
        
        // 获取标签页配置
        const tabConfig = this.config.tabs[tabId];
        console.log(`加载标签页数据: ${tabId}, 接口: ${tabConfig.apiEndpoint}`);
        
        // 获取标签页内容元素
        const tabContent = document.getElementById(tabId);
        if (!tabContent) {
            console.error('找不到标签页内容元素: ' + tabId);
            return;
        }
        
        // 检查是否已经加载数据
        if (this.state.loadedTabs[tabId]) {
            console.log(`标签页 ${tabId} 已加载数据，无需重新加载`);
            
            // 确保表格正确渲染
            setTimeout(() => {
                this.adjustActiveTable();
            }, 100);
            
            return;
        }
        
        // 构建API参数
        const apiParams = {};
        
        // 如果标签页有参数键，从URL获取对应参数
        if (tabConfig.paramKey) {
            const paramValue = this.state.urlParams.get(tabConfig.paramKey);
            if (paramValue) {
                apiParams[tabConfig.paramKey] = paramValue;
            } else {
                // 如果需要参数但没有，显示空数据提示
                tabContent.innerHTML = `
                    <div class="alert alert-info mt-3">
                        ${tabConfig.emptyMessage}
                    </div>
                `;
                return;
            }
        }
        
        // 显示加载状态
        this.showLoading();
        
        // 检查缓存中是否有数据
        const cachedData = ApiDataManager.getData(tabConfig.apiEndpoint, apiParams);
        if (cachedData) {
            console.log(`使用缓存的${tabId}数据`);
            this.displayTabData(tabId, cachedData);
            this.hideLoading();
            return;
        }
        
        // 构建API请求URL
        let apiUrl = `/api/${tabConfig.apiEndpoint}`;
        
        // 添加参数
        if (Object.keys(apiParams).length > 0) {
            const queryParams = new URLSearchParams();
            Object.keys(apiParams).forEach(key => {
                queryParams.append(key, apiParams[key]);
            });
            apiUrl += `?${queryParams.toString()}`;
        }
        
        // 发送API请求
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                
                // 使用安全的响应JSON处理
                if (typeof DataValidator !== 'undefined') {
                    return DataValidator.safeResponseJson(response, `DataQueryController-${tabConfig.apiEndpoint}`);
                } else {
                    // 降级处理
                    return response.json().catch(error => {
                        console.error(`DataQueryController API响应JSON解析失败 [${tabConfig.apiEndpoint}]:`, error);
                        throw new Error('服务器响应格式错误');
                    });
                }
            })
            .then(data => {
                // 缓存API响应数据
                ApiDataManager.storeData(tabConfig.apiEndpoint, apiParams, data);
                
                // 显示数据
                this.displayTabData(tabId, data);
                
                // 隐藏加载状态
                this.hideLoading();
                
                // 标记标签页已加载数据
                this.state.loadedTabs[tabId] = true;
            })
            .catch(error => {
                console.error(`${tabId}数据加载错误:`, error);
                
                // 显示错误信息
                tabContent.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        获取数据失败: ${error.message}
                    </div>
                `;
                
                // 隐藏加载状态
                this.hideLoading();
            });
    },
    
    // 显示加载状态
    showLoading: function() {
        const loadingOverlay = document.querySelector(this.config.selectors.loadingOverlay);
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
    },
    
    // 隐藏加载状态
    hideLoading: function() {
        const loadingOverlay = document.querySelector(this.config.selectors.loadingOverlay);
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    },
    
    // 显示标签页数据
    displayTabData: function(tabId, data) {
        const tabConfig = this.config.tabs[tabId];
        const tabElement = document.getElementById(tabId);
        
        // 如果标签页元素不存在，返回
        if (!tabElement) {
            console.error('找不到标签页元素: ' + tabId);
            return;
        }
        
        // 保存数据到全局变量，以便导出功能使用
        window[`${tabId}Results`] = data;
        
        // 处理错误情况
        if (data.error) {
            tabElement.innerHTML = `
                <div class="alert alert-danger mt-3">
                    获取数据失败: ${data.error}
                </div>
            `;
            return;
        }
        
        // 处理无数据情况
        if (!data.results || data.results.length === 0) {
            tabElement.innerHTML = `
                <div class="alert alert-info mt-3">
                    ${tabConfig.emptyMessage}
                </div>
            `;
            return;
        }
        
        // 准备标题文本
        let titleText = `共找到 ${data.results.length} 条符合条件的记录`;
        if (tabId === 'overdue') {
            titleText = `共找到 ${data.results.length} 条逾期记录`;
        } else if (tabId === 'customer') {
            // 获取客户姓名
            let customerName = "客户";
            if (data.results && data.results.length > 0 && data.results[0]['客户姓名']) {
                customerName = data.results[0]['客户姓名'];
            }
            titleText = `共找到 ${data.results.length} 条 ${customerName} 的订单记录`;
        }
        
        // 准备附加按钮(如客户汇总按钮)
        let additionalButtons = '';
        if (tabId === 'customer') {
            // 获取客户姓名
            let customerName = "";
            if (data.results && data.results.length > 0 && data.results[0]['客户姓名']) {
                customerName = data.results[0]['客户姓名'];
            }
            
            if (customerName) {
                additionalButtons = `
                    <button class="btn btn-primary ms-3" onclick="viewCustomerSummary('${customerName}')">
                        <i class="bi bi-pie-chart"></i> 查看${customerName}汇总数据
                    </button>
                `;
            }
        }
        
        // 构建统一的HTML结构
        let contentHtml = '';
        if (additionalButtons) {
            contentHtml = `
                <div class="d-flex justify-content-between align-items-center pt-3 mb-3">
                    <div class="alert alert-success mb-0 flex-grow-1 me-3 auto-dismiss-alert">
                        ${titleText}
                    </div>
                    ${additionalButtons}
                </div>
            `;
        } else {
            contentHtml = `
                <div class="pt-3">
                    <div class="alert alert-success auto-dismiss-alert">
                        ${titleText}
                    </div>
                </div>
            `;
        }
        
        // 构建表格HTML
        const tableHtml = this.buildDataTableHtml(data, tabId);
        
        // 添加表格容器
        contentHtml += `
            <div class="table-responsive">
                ${tableHtml}
            </div>
        `;
        
        // 更新DOM
        tabElement.innerHTML = contentHtml;
        
        // 初始化表格
        this.initializeDataTable(tabId);
    },
    
    // 构建数据表格HTML
    buildDataTableHtml: function(data, tableType) {
        if (!data || !data.results || !data.columns) {
            return '<p class="text-danger">数据格式错误</p>';
        }
        
        // 构建表格头部
        let tableHtml = `<table class="table table-striped table-hover data-table" data-type="${tableType}" style="width:100%">
            <thead>
                <tr>
                    <th class="dtr-control"></th>`;
        
        // 添加列标题
        for (const column of data.columns) {
            tableHtml += `<th>${column}</th>`;
        }
        tableHtml += `</tr>
            </thead>
            <tbody>`;
        
        // 添加数据行
        for (const row of data.results) {
            // 处理行样式和数据属性
            let rowAttributes = '';
            
            // 添加状态属性（如果存在）
            if (row['备注']) {
                rowAttributes += ` data-status="${row['备注']}"`;
            }
            
            // 添加客户属性（对客户标签页）
            if (tableType === 'customer' && row['客户姓名']) {
                rowAttributes += ` data-customer="${row['客户姓名']}"`;
            }
            
            tableHtml += `<tr${rowAttributes}>
                <td class="dtr-control"></td>`;
            
            // 添加单元格
            for (const column of data.columns) {
                let cellValue = row[column];
                let cellContent = cellValue !== undefined ? cellValue : '-';
                
                // 如果值为null，显示为空
                if (cellContent === null) {
                    cellContent = '-';
                }
                
                // 添加单元格
                tableHtml += `<td>${cellContent}</td>`;
            }
            
            tableHtml += '</tr>';
        }
        
        tableHtml += '</tbody></table>';
        
        return tableHtml;
    },
    
    // 初始化DataTable
    initializeDataTable: function(tabId) {
        const tabElement = document.getElementById(tabId);
        if (!tabElement) return;
        
        const tabConfig = this.config.tabs[tabId];
        const tableElement = tabElement.querySelector('.data-table');
        
        if (!tableElement) {
            console.error(`找不到${tabId}标签页的表格元素`);
            return;
        }
        
        // 检查表格是否已初始化
        if ($.fn.DataTable.isDataTable(tableElement)) {
            console.log(`${tabId}表格已初始化，销毁旧实例`);
            $(tableElement).DataTable().destroy();
        }
        
        // 初始化DataTable
        const dataTable = $(tableElement).DataTable({
            responsive: {
                details: {
                    type: 'column',
                    target: 0 // 使用第一列作为控制列
                }
            },
            columnDefs: [
                {
                    className: 'dtr-control',
                    orderable: false,
                    targets: 0,
                    width: "40px"
                },
                {
                    targets: '_all',
                    className: 'dt-head-center dt-body-center'
                }
            ],
            order: tabConfig.defaultOrder || [[1, 'asc']],
            dom: '<"dataTable-top"lf>rt<"dataTable-bottom"ip>',
            language: {
                search: "搜索:",
                lengthMenu: "显示 _MENU_ 条数据",
                info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                infoFiltered: "(由 _MAX_ 项结果过滤)",
                paginate: {
                    first: "首页",
                    previous: "上页",
                    next: "下页",
                    last: "末页"
                },
                zeroRecords: "没有匹配结果",
                emptyTable: "暂无数据",
                loadingRecords: "加载中...",
                processing: "处理中..."
            }
        });
        
        // 使用StyleController应用样式增强
        if (window.StyleController) {
            StyleController.enhanceTable(tableElement, tabId);
        } else {
            // 兼容旧代码
            enhanceDataTables();
        }
        
        // 表格初始化完成后调整响应式布局
        setTimeout(() => {
            dataTable.columns.adjust().responsive.recalc();
        }, 100);
        
        return dataTable;
    }
};

// 当DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果是数据查询页面，初始化控制器
    if (document.getElementById('dataTabs')) {
        DataQueryController.init();
    }
}); 