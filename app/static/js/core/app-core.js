/**
 * 应用核心模块 - 基础工具函数和全局状态管理
 * 所有其他模块的基础依赖
 */

// 全局应用命名空间
window.TaixiangApp = window.TaixiangApp || {};

// 核心工具函数
TaixiangApp.Utils = {
    // 显示加载指示器
    showLoading: function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
            
            // 添加自动超时，防止加载指示器无限显示
            window.loadingTimeout = setTimeout(function() {
                TaixiangApp.Utils.hideLoading();
            }, 15000);
        }
    },
    
    // 隐藏加载指示器
    hideLoading: function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
            
            // 清除超时计时器
            if (window.loadingTimeout) {
                clearTimeout(window.loadingTimeout);
            }
        }
    },
    
    // 从HTML内容中提取纯文本
    stripHtml: function(html) {
        if (typeof html !== 'string') {
            return html;
        }
        
        const tempElement = document.createElement('div');
        tempElement.innerHTML = html;
        return tempElement.textContent || tempElement.innerText || html;
    },
    
    // 优化移动设备显示
    optimizeForMobile: function() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移动端优化逻辑
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                if ($.fn.DataTable.isDataTable(table)) {
                    $(table).DataTable().columns.adjust().responsive.recalc();
                }
            });
        }
    },
    
    // 设置自动消失提示
    setupAutoDismissAlerts: function() {
        const alerts = document.querySelectorAll('.auto-dismiss-alert:not(.alert-dismissing)');
        alerts.forEach(alert => {
            alert.classList.add('alert-dismissing');
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.style.transition = 'opacity 0.5s, height 0.5s, padding 0.5s, margin 0.5s';
                    alert.style.opacity = '0';
                    alert.style.height = '0';
                    alert.style.padding = '0';
                    alert.style.margin = '0';
                    
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 500);
                }
            }, 3000);
        });
    }
};

// 全局状态管理
TaixiangApp.State = {
    // 逾期数据状态
    overdueData: {
        currentPage: 1,
        totalPages: 1,
        totalRecords: 0,
        isLoading: false,
        hasMore: true
    },
    
    // 重置逾期数据状态
    resetOverdueState: function() {
        this.overdueData = {
            currentPage: 1,
            totalPages: 1,
            totalRecords: 0,
            isLoading: false,
            hasMore: true
        };
    }
};

// 向后兼容的全局函数
window.showLoading = TaixiangApp.Utils.showLoading;
window.hideLoading = TaixiangApp.Utils.hideLoading;
window.stripHtml = TaixiangApp.Utils.stripHtml;
window.optimizeForMobile = TaixiangApp.Utils.optimizeForMobile;
window.setupAutoDismissAlerts = TaixiangApp.Utils.setupAutoDismissAlerts;
window.overdueDataState = TaixiangApp.State.overdueData;

console.log('TaixiangApp Core 模块已加载');