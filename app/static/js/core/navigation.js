/**
 * 导航管理模块
 * 统一管理页面导航和功能跳转
 * 
 * @module NavigationManager
 * @version 2.0
 */

const NavigationManager = {
  /**
   * 配置选项
   */
  config: {
    // URL配置
    urls: {
      home: '/',
      workbench: '/home',
      overdue: '/overdue',
      summary: '/summary',
      batchReceipt: '/batch_receipt_generator',
      logout: '/logout'
    },
    
    // 默认标签页
    defaultTab: 'filter',
    
    // 是否显示加载指示器
    showLoading: true
  },

  /**
   * 初始化导航管理器
   */
  init() {
    this.bindEvents();
    this.setupUrlHandler();
    console.log('NavigationManager: 初始化完成');
  },

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听浏览器前进后退
    if (typeof window !== 'undefined') {
      EventManager.bind(window, 'popstate', (event) => {
        this.handlePopState(event);
      });
    }
  },

  /**
   * 设置URL处理器
   */
  setupUrlHandler() {
    // 处理当前页面的URL参数
    const urlParams = this.getUrlParams();
    if (urlParams.size > 0) {
      this.handleUrlParams(urlParams);
    }
  },

  /**
   * 跳转到首页
   * @param {Object} options - 跳转选项
   * @param {string} options.tab - 要激活的标签页
   * @param {Object} options.params - 额外的URL参数
   */
  goToHome(options = {}) {
    const { tab, params = {} } = options;
    
    if (tab) {
      params.tab = tab;
    }
    
    this.navigateTo(this.config.urls.home, params);
  },

  /**
   * 跳转到工作台
   */
  goToWorkbench() {
    this.navigateTo(this.config.urls.workbench);
  },

  /**
   * 日期筛选导航
   * @param {string} date - 日期字符串 (YYYY-MM-DD)
   * @param {Object} options - 额外选项
   */
  filterByDate(date, options = {}) {
    // 输入验证
    if (!this.validateDate(date)) {
      // 尝试从DOM获取日期
      const dateInput = DOMUtils.get('#date');
      if (dateInput && dateInput.value) {
        date = dateInput.value;
      } else {
        this.showError('请选择有效的日期');
        return false;
      }
    }

    const params = {
      date: date,
      tab: 'filter',
      ...options
    };

    // 检查当前页面
    if (this.isCurrentPage(this.config.urls.home)) {
      // 在首页更新URL参数
      this.updateUrlParams(params);
    } else {
      // 跳转到首页并带参数
      this.goToHome({ params });
    }

    return true;
  },

  /**
   * 客户搜索导航
   * @param {string} customerName - 客户姓名
   * @param {Object} options - 额外选项
   */
  searchCustomer(customerName, options = {}) {
    // 输入验证
    if (!this.validateCustomerName(customerName)) {
      // 尝试从DOM获取客户名
      const customerInput = DOMUtils.get('#customerName');
      if (customerInput && customerInput.value.trim()) {
        customerName = customerInput.value.trim();
      } else {
        this.showError('请输入客户姓名');
        return false;
      }
    }

    const params = {
      customerName: customerName,
      tab: 'customer',
      ...options
    };

    // 检查当前页面
    if (this.isCurrentPage(this.config.urls.home)) {
      // 在首页更新URL参数
      this.updateUrlParams(params);
    } else {
      // 跳转到首页并带参数
      this.goToHome({ params });
    }

    return true;
  },

  /**
   * 显示逾期订单
   */
  showOverdueOrders() {
    this.navigateTo(this.config.urls.overdue);
  },

  /**
   * 显示数据汇总视图
   */
  showSummaryView() {
    this.navigateTo(this.config.urls.summary);
  },

  /**
   * 显示批量回执单生成器
   */
  showBatchReceiptGenerator() {
    this.navigateTo(this.config.urls.batchReceipt);
  },

  /**
   * 退出登录
   * @param {boolean} confirm - 是否需要确认
   */
  logout(confirm = true) {
    if (confirm) {
      if (!window.confirm('确定要退出登录吗？')) {
        return false;
      }
    }

    this.navigateTo(this.config.urls.logout);
    return true;
  },

  /**
   * 通用导航方法
   * @param {string} url - 目标URL
   * @param {Object} params - URL参数
   * @param {Object} options - 导航选项
   */
  navigateTo(url, params = {}, options = {}) {
    try {
      // 显示加载指示器
      if (this.config.showLoading && typeof window.showLoading === 'function') {
        window.showLoading();
      }

      // 构建完整URL
      const fullUrl = this.buildUrl(url, params);
      
      // 记录导航历史
      this.recordNavigation(fullUrl, options);

      // 执行导航
      if (options.replace) {
        window.location.replace(fullUrl);
      } else {
        window.location.href = fullUrl;
      }

    } catch (error) {
      console.error('NavigationManager: 导航失败', error);
      this.showError('页面跳转失败，请重试');
      
      // 隐藏加载指示器
      if (typeof window.hideLoading === 'function') {
        window.hideLoading();
      }
    }
  },

  /**
   * 更新当前页面的URL参数
   * @param {Object} params - 要更新的参数
   */
  updateUrlParams(params) {
    try {
      const url = new URL(window.location);
      
      // 更新参数
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          url.searchParams.set(key, value);
        } else {
          url.searchParams.delete(key);
        }
      });

      // 使用pushState更新URL但不重新加载页面
      window.history.pushState(null, '', url.toString());
      
      // 触发自定义事件，通知其他模块URL已更新
      this.dispatchUrlChangeEvent(params);

    } catch (error) {
      console.error('NavigationManager: 更新URL参数失败', error);
    }
  },

  /**
   * 获取当前URL参数
   * @returns {Map} URL参数Map
   */
  getUrlParams() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      return new Map(urlParams.entries());
    } catch (error) {
      console.error('NavigationManager: 获取URL参数失败', error);
      return new Map();
    }
  },

  /**
   * 构建URL
   * @param {string} baseUrl - 基础URL
   * @param {Object} params - 参数对象
   * @returns {string} 完整URL
   */
  buildUrl(baseUrl, params = {}) {
    try {
      const url = new URL(baseUrl, window.location.origin);
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          url.searchParams.set(key, encodeURIComponent(value));
        }
      });

      return url.toString();
    } catch (error) {
      console.error('NavigationManager: 构建URL失败', error);
      return baseUrl;
    }
  },

  /**
   * 检查是否为当前页面
   * @param {string} path - 路径
   * @returns {boolean} 是否为当前页面
   */
  isCurrentPage(path) {
    return window.location.pathname === path;
  },

  /**
   * 处理浏览器前进后退
   * @param {PopStateEvent} event - popstate事件
   */
  handlePopState(event) {
    console.log('NavigationManager: 处理浏览器前进后退', event.state);
    
    // 获取当前URL参数并处理
    const urlParams = this.getUrlParams();
    this.handleUrlParams(urlParams);
  },

  /**
   * 处理URL参数
   * @param {Map} params - URL参数Map
   */
  handleUrlParams(params) {
    // 触发URL参数处理事件
    this.dispatchUrlChangeEvent(Object.fromEntries(params));

    // 处理特定参数
    if (params.has('tab')) {
      this.activateTab(params.get('tab'));
    }

    if (params.has('date')) {
      this.handleDateParam(params.get('date'));
    }

    if (params.has('customerName')) {
      this.handleCustomerParam(params.get('customerName'));
    }
  },

  /**
   * 激活指定标签页
   * @param {string} tabId - 标签页ID
   */
  activateTab(tabId) {
    const tabElement = DOMUtils.get(`#${tabId}-tab`);
    if (tabElement && typeof bootstrap !== 'undefined') {
      const tab = new bootstrap.Tab(tabElement);
      tab.show();
    }
  },

  /**
   * 处理日期参数
   * @param {string} date - 日期字符串
   */
  handleDateParam(date) {
    const dateInput = DOMUtils.get('#date');
    if (dateInput) {
      dateInput.value = date;
    }
  },

  /**
   * 处理客户参数
   * @param {string} customerName - 客户姓名
   */
  handleCustomerParam(customerName) {
    const customerInput = DOMUtils.get('#customerName');
    if (customerInput) {
      customerInput.value = decodeURIComponent(customerName);
    }
  },

  /**
   * 分发URL变化事件
   * @param {Object} params - 参数对象
   */
  dispatchUrlChangeEvent(params) {
    const event = new CustomEvent('urlParamsChanged', {
      detail: { params }
    });
    document.dispatchEvent(event);
  },

  /**
   * 记录导航历史
   * @param {string} url - URL
   * @param {Object} options - 选项
   */
  recordNavigation(url, options) {
    // 记录到sessionStorage以便追踪
    const navigation = {
      url,
      timestamp: Date.now(),
      from: window.location.href,
      options
    };

    try {
      const history = JSON.parse(sessionStorage.getItem('navigationHistory') || '[]');
      history.push(navigation);
      
      // 只保留最近50条记录
      if (history.length > 50) {
        history.splice(0, history.length - 50);
      }
      
      sessionStorage.setItem('navigationHistory', JSON.stringify(history));
    } catch (error) {
      console.warn('NavigationManager: 无法记录导航历史', error);
    }
  },

  /**
   * 验证日期格式
   * @param {string} date - 日期字符串
   * @returns {boolean} 是否有效
   */
  validateDate(date) {
    if (!date || typeof date !== 'string') {
      return false;
    }

    // 检查日期格式 YYYY-MM-DD
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return false;
    }

    // 检查日期是否有效
    const dateObj = new Date(date);
    return dateObj instanceof Date && !isNaN(dateObj.getTime());
  },

  /**
   * 验证客户姓名
   * @param {string} customerName - 客户姓名
   * @returns {boolean} 是否有效
   */
  validateCustomerName(customerName) {
    return customerName && 
           typeof customerName === 'string' && 
           customerName.trim().length > 0 &&
           customerName.trim().length <= 50; // 限制长度
  },

  /**
   * 显示错误消息
   * @param {string} message - 错误消息
   */
  showError(message) {
    // 优先使用自定义的错误显示函数
    if (typeof window.showErrorAlert === 'function') {
      window.showErrorAlert(message);
    } else if (typeof alert === 'function') {
      alert(message);
    } else {
      console.error('NavigationManager:', message);
    }
  },

  /**
   * 获取当前页面信息
   * @returns {Object} 页面信息
   */
  getCurrentPageInfo() {
    return {
      path: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      params: Object.fromEntries(this.getUrlParams()),
      title: document.title
    };
  },

  /**
   * 检查是否可以使用浏览器历史API
   * @returns {boolean} 是否支持
   */
  canUseHistory() {
    return typeof window !== 'undefined' && 
           window.history && 
           typeof window.history.pushState === 'function';
  }
};

// 向后兼容的全局接口
if (typeof window !== 'undefined') {
  // 保持原有的全局接口
  window.AppNavigation = {
    goToHome: () => NavigationManager.goToHome(),
    goToWorkbench: () => NavigationManager.goToWorkbench(),
    filterByDate: (date) => NavigationManager.filterByDate(date),
    searchCustomer: () => NavigationManager.searchCustomer(),
    showOverdueOrders: () => NavigationManager.showOverdueOrders(),
    showSummaryView: () => NavigationManager.showSummaryView(),
    showBatchReceiptGenerator: () => NavigationManager.showBatchReceiptGenerator(),
    logout: () => NavigationManager.logout(),
    checkApiStatus: () => {
      // 委托给API状态管理器
      if (typeof window.ApiStatusManager !== 'undefined') {
        return window.ApiStatusManager.check();
      }
    }
  };

  // 导出新的管理器
  window.NavigationManager = NavigationManager;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NavigationManager;
}

// 自动初始化
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      NavigationManager.init();
    });
  } else {
    NavigationManager.init();
  }
}