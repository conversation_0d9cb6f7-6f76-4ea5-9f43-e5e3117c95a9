/**
 * 页面自动刷新问题修复器
 * 解决用户反馈的页面静止30秒后自动刷新的问题
 */

class PageRefreshFixer {
    constructor() {
        this.isUserActive = true;
        this.lastActivityTime = Date.now();
        this.activityTimeout = null;
        this.preventRefreshTimers = new Set();
        this.originalFunctions = new Map();
        
        console.log('页面刷新修复器已初始化');
    }

    /**
     * 初始化修复器
     */
    init() {
        this.setupActivityTracking();
        this.interceptRefreshMechanisms();
        this.preventVisibilityRefresh();
        this.fixAPIStatusChecks();
        this.setupDebugMode();
        
        console.log('页面刷新修复器已启动');
    }

    /**
     * 设置用户活动跟踪
     */
    setupActivityTracking() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivityStatus();
            }, { passive: true });
        });

        // 定期检查用户活动状态
        setInterval(() => {
            const now = Date.now();
            const timeSinceLastActivity = now - this.lastActivityTime;
            
            // 如果用户5分钟内没有活动，认为用户不活跃
            this.isUserActive = timeSinceLastActivity < 5 * 60 * 1000;
            
            if (!this.isUserActive) {
                console.log('用户不活跃，暂停所有自动刷新机制');
                this.pauseAllRefreshMechanisms();
            }
        }, 30000); // 每30秒检查一次
    }

    /**
     * 更新用户活动状态
     */
    updateActivityStatus() {
        this.lastActivityTime = Date.now();
        this.isUserActive = true;
        
        // 如果用户重新活跃，恢复被暂停的机制
        this.resumeRefreshMechanisms();
    }

    /**
     * 拦截并修复页面刷新机制
     */
    interceptRefreshMechanisms() {
        // 拦截setTimeout调用
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = (callback, delay, ...args) => {
            // 检查是否是潜在的刷新调用
            if (this.isPotentialRefreshCallback(callback)) {
                console.log('拦截到潜在的自动刷新调用，延迟:', delay);
                
                // 如果用户不活跃，不执行自动刷新
                const wrappedCallback = () => {
                    if (this.isUserActive) {
                        callback.apply(this, args);
                    } else {
                        console.log('用户不活跃，跳过自动刷新');
                    }
                };
                
                const timerId = originalSetTimeout.call(window, wrappedCallback, delay);
                this.preventRefreshTimers.add(timerId);
                return timerId;
            }
            
            return originalSetTimeout.call(window, callback, delay, ...args);
        };

        // 拦截setInterval调用
        const originalSetInterval = window.setInterval;
        window.setInterval = (callback, delay, ...args) => {
            // 检查是否是状态检查相关的定时器
            if (this.isStatusCheckCallback(callback)) {
                console.log('拦截到状态检查定时器，间隔:', delay);
                
                const wrappedCallback = () => {
                    if (this.isUserActive) {
                        callback.apply(this, args);
                    } else {
                        console.log('用户不活跃，跳过状态检查');
                    }
                };
                
                return originalSetInterval.call(window, wrappedCallback, delay);
            }
            
            return originalSetInterval.call(window, callback, delay, ...args);
        };
    }

    /**
     * 检查是否是潜在的刷新回调
     */
    isPotentialRefreshCallback(callback) {
        if (typeof callback !== 'function') return false;
        
        const callbackStr = callback.toString();
        const refreshPatterns = [
            'checkApiStatus',
            'loadData',
            'refreshData',
            'reload',
            'location.reload',
            'window.location.reload',
            'showLoading',
            'loadTabData',
            'loadFilterData'
        ];
        
        return refreshPatterns.some(pattern => callbackStr.includes(pattern));
    }

    /**
     * 检查是否是状态检查回调
     */
    isStatusCheckCallback(callback) {
        if (typeof callback !== 'function') return false;
        
        const callbackStr = callback.toString();
        return callbackStr.includes('checkStatus') || 
               callbackStr.includes('checkApiStatus') ||
               callbackStr.includes('cleanCache');
    }

    /**
     * 防止页面可见性变化导致的刷新
     */
    preventVisibilityRefresh() {
        // 存储原始的visibilitychange事件处理器
        const originalAddEventListener = document.addEventListener;
        
        document.addEventListener = function(type, listener, options) {
            if (type === 'visibilitychange') {
                console.log('拦截到visibilitychange事件监听器');
                
                // 包装监听器，添加用户活动检查
                const wrappedListener = (event) => {
                    if (PageRefreshFixer.instance && PageRefreshFixer.instance.isUserActive) {
                        // 延迟执行，给用户时间进行交互
                        setTimeout(() => {
                            if (PageRefreshFixer.instance.isUserActive) {
                                listener.call(this, event);
                            }
                        }, 1000);
                    } else {
                        console.log('用户不活跃，跳过visibilitychange处理');
                    }
                };
                
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            
            return originalAddEventListener.call(this, type, listener, options);
        };
    }

    /**
     * 修复API状态检查函数
     */
    fixAPIStatusChecks() {
        // 延迟执行，确保其他脚本加载完成
        setTimeout(() => {
            this.patchGlobalFunctions();
        }, 1000);
    }

    /**
     * 修补全局函数
     */
    patchGlobalFunctions() {
        // 修补checkApiStatus函数
        if (typeof window.checkApiStatus === 'function') {
            const original = window.checkApiStatus;
            window.checkApiStatus = () => {
                if (this.isUserActive) {
                    console.log('执行API状态检查（用户活跃）');
                    original.call(window);
                } else {
                    console.log('跳过API状态检查（用户不活跃）');
                }
            };
        }

        // 修补ApiStatusManager
        if (window.apiStatusManager && window.apiStatusManager.checkStatus) {
            const original = window.apiStatusManager.checkStatus;
            window.apiStatusManager.checkStatus = function() {
                if (PageRefreshFixer.instance && PageRefreshFixer.instance.isUserActive) {
                    console.log('执行ApiStatusManager状态检查（用户活跃）');
                    return original.call(this);
                } else {
                    console.log('跳过ApiStatusManager状态检查（用户不活跃）');
                    return Promise.resolve();
                }
            };
        }

        // 修补TaixiangApp.DataLoader相关函数
        if (window.TaixiangApp && window.TaixiangApp.DataLoader) {
            const loader = window.TaixiangApp.DataLoader;
            
            ['loadFilterData', 'loadCustomerData', 'loadOverdueData'].forEach(methodName => {
                if (typeof loader[methodName] === 'function') {
                    const original = loader[methodName];
                    loader[methodName] = function(...args) {
                        if (PageRefreshFixer.instance && PageRefreshFixer.instance.isUserActive) {
                            console.log(`执行${methodName}（用户活跃）`);
                            return original.apply(this, args);
                        } else {
                            console.log(`跳过${methodName}（用户不活跃）`);
                        }
                    };
                }
            });
        }
    }

    /**
     * 暂停所有刷新机制
     */
    pauseAllRefreshMechanisms() {
        // 清除所有可能的刷新定时器
        this.preventRefreshTimers.forEach(timerId => {
            clearTimeout(timerId);
        });
        this.preventRefreshTimers.clear();

        // 暂停API状态管理器的定时检查
        if (window.apiStatusManager && window.apiStatusManager.stopPeriodicCheck) {
            window.apiStatusManager.stopPeriodicCheck();
        }
    }

    /**
     * 恢复刷新机制
     */
    resumeRefreshMechanisms() {
        // 恢复API状态管理器的定时检查（仅在未运行时启动）
        if (window.apiStatusManager && window.apiStatusManager.startPeriodicCheck) {
            // 检查是否已经在运行，避免重复启动
            if (!window.apiStatusManager.checkInterval) {
                window.apiStatusManager.startPeriodicCheck();
            }
        }
    }

    /**
     * 设置调试模式
     */
    setupDebugMode() {
        // 添加调试信息到控制台
        const debugInfo = () => {
            console.log('页面刷新修复器状态:', {
                isUserActive: this.isUserActive,
                lastActivityTime: new Date(this.lastActivityTime).toLocaleTimeString(),
                preventRefreshTimers: this.preventRefreshTimers.size,
                timeSinceLastActivity: Math.round((Date.now() - this.lastActivityTime) / 1000) + '秒'
            });
        };

        // 每分钟输出一次调试信息
        setInterval(debugInfo, 60000);

        // 暴露调试函数到全局
        window.debugPageRefreshFixer = debugInfo;
    }

    /**
     * 手动重置活动状态
     */
    resetActivity() {
        this.updateActivityStatus();
        console.log('手动重置用户活动状态');
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            isUserActive: this.isUserActive,
            lastActivityTime: this.lastActivityTime,
            timeSinceLastActivity: Date.now() - this.lastActivityTime,
            preventRefreshTimers: this.preventRefreshTimers.size
        };
    }
}

// 创建全局实例
PageRefreshFixer.instance = new PageRefreshFixer();

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本加载完成
    setTimeout(() => {
        PageRefreshFixer.instance.init();
    }, 2000);
});

// 导出到全局
window.PageRefreshFixer = PageRefreshFixer;

// 提供用户友好的控制函数
window.stopAutoRefresh = () => {
    PageRefreshFixer.instance.isUserActive = false;
    PageRefreshFixer.instance.pauseAllRefreshMechanisms();
    console.log('已停止所有自动刷新机制');
};

window.startAutoRefresh = () => {
    PageRefreshFixer.instance.resetActivity();
    PageRefreshFixer.instance.resumeRefreshMechanisms();
    console.log('已恢复自动刷新机制');
};

console.log('页面刷新修复器已加载完成'); 