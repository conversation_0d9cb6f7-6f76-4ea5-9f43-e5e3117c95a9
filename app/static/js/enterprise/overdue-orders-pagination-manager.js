/**
 * 企业级逾期订单分页管理器
 * 专门为逾期订单页面提供美观的移动端分页体验
 * 支持四级响应式断点、智能页码折叠、触摸滑动翻页等功能
 */

const OverdueOrdersPaginationManager = {
    // 配置选项
    config: {
        breakpoints: {
            extraSmall: 375,   // 超小屏 ≤375px
            small: 480,        // 小屏 ≤480px  
            mobile: 767,       // 移动端 ≤767px
            desktop: 768       // 桌面端 ≥768px
        },
        animation: {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        },
        touch: {
            threshold: 50,     // 滑动触发距离
            timeout: 300       // 滑动超时时间
        }
    },

    // 分页状态
    state: {
        currentBreakpoint: null,
        touchStart: null,
        touchEnd: null,
        isAnimating: false
    },

    /**
     * 初始化分页管理器
     */
    init() {
        console.log('🚀 初始化企业级逾期订单分页管理器');
        
        // 等待DOM完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    },

    /**
     * 设置分页管理器
     */
    setup() {
        // 检测当前断点
        this.detectBreakpoint();
        
        // 优化分页外观
        this.optimizePaginationAppearance();
        
        // 添加触摸支持
        this.setupTouchSupport();
        
        // 监听窗口变化
        this.setupResizeListener();
        
        // 添加页面切换动画
        this.setupPageTransitions();
        
        console.log('✅ 逾期订单分页管理器初始化完成');
    },

    /**
     * 检测当前断点
     */
    detectBreakpoint() {
        const width = window.innerWidth;
        const { breakpoints } = this.config;
        
        if (width <= breakpoints.extraSmall) {
            this.state.currentBreakpoint = 'extraSmall';
        } else if (width <= breakpoints.small) {
            this.state.currentBreakpoint = 'small';
        } else if (width <= breakpoints.mobile) {
            this.state.currentBreakpoint = 'mobile';
        } else {
            this.state.currentBreakpoint = 'desktop';
        }
        
        console.log(`📱 当前断点: ${this.state.currentBreakpoint} (${width}px)`);
    },

    /**
     * 优化分页外观
     */
    optimizePaginationAppearance() {
        const paginationContainer = document.querySelector('.pagination-container');
        if (!paginationContainer) return;

        // 移除旧的样式类
        paginationContainer.classList.remove(
            'overdue-pagination-extrasmall',
            'overdue-pagination-small', 
            'overdue-pagination-mobile',
            'overdue-pagination-desktop'
        );

        // 添加当前断点对应的样式类
        paginationContainer.classList.add(`overdue-pagination-${this.state.currentBreakpoint}`);

        // 根据断点应用不同的优化策略
        switch (this.state.currentBreakpoint) {
            case 'extraSmall':
                this.applyExtraSmallOptimization(paginationContainer);
                break;
            case 'small':
                this.applySmallOptimization(paginationContainer);
                break;
            case 'mobile':
                this.applyMobileOptimization(paginationContainer);
                break;
            case 'desktop':
                this.applyDesktopOptimization(paginationContainer);
                break;
        }
    },

    /**
     * 超小屏优化 (≤375px) - 极简页码指示器
     */
    applyExtraSmallOptimization(container) {
        const pagination = container.querySelector('.pagination');
        if (!pagination) return;

        // 隐藏所有页码按钮，只保留前后按钮
        const pageItems = pagination.querySelectorAll('.page-item:not(:first-child):not(:last-child)');
        pageItems.forEach(item => {
            item.style.display = 'none';
        });

        // 创建页码指示器
        this.createPageIndicator(pagination);
        
        // 添加滑动提示
        this.addSwipeHint(container);
        
        console.log('📱 应用超小屏优化');
    },

    /**
     * 小屏优化 (≤480px) - 智能页码折叠  
     */
    applySmallOptimization(container) {
        const pagination = container.querySelector('.pagination');
        if (!pagination) return;

        // 只显示当前页±1的页码
        this.applySmartHiding(pagination, 1);
        
        // 优化按钮间距
        pagination.style.gap = '4px';
        
        console.log('📱 应用小屏优化');
    },

    /**
     * 移动端优化 (≤767px) - 显示当前页±2
     */
    applyMobileOptimization(container) {
        const pagination = container.querySelector('.pagination');
        if (!pagination) return;

        // 显示当前页±2的页码
        this.applySmartHiding(pagination, 2);
        
        // 添加触摸反馈
        this.addTouchFeedback(pagination);
        
        console.log('📱 应用移动端优化');
    },

    /**
     * 桌面端优化 (≥768px) - 完整显示
     */
    applyDesktopOptimization(container) {
        const pagination = container.querySelector('.pagination');
        if (!pagination) return;

        // 显示所有页码
        const pageItems = pagination.querySelectorAll('.page-item');
        pageItems.forEach(item => {
            item.style.display = '';
        });

        // 移除移动端特有元素
        const indicator = pagination.querySelector('.page-indicator');
        if (indicator) indicator.remove();
        
        const swipeHint = container.querySelector('.swipe-hint');
        if (swipeHint) swipeHint.remove();

        console.log('🖥️ 应用桌面端优化');
    },

    /**
     * 智能隐藏页码按钮
     */
    applySmartHiding(pagination, range) {
        const pageItems = Array.from(pagination.querySelectorAll('.page-item'));
        const currentPageItem = pagination.querySelector('.page-item.active');
        
        if (!currentPageItem) return;
        
        const currentPageIndex = pageItems.indexOf(currentPageItem);
        
        pageItems.forEach((item, index) => {
            const isNavButton = item.querySelector('.page-link i'); // 有图标的是导航按钮
            const isInRange = Math.abs(index - currentPageIndex) <= range;
            const isFirstOrLast = index === 1 || index === pageItems.length - 2; // 跳过首末的导航按钮
            
            if (isNavButton || isInRange || isFirstOrLast) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    },

    /**
     * 创建页码指示器
     */
    createPageIndicator(pagination) {
        // 移除现有指示器
        const existingIndicator = pagination.querySelector('.page-indicator');
        if (existingIndicator) existingIndicator.remove();

        // 获取当前页和总页数
        const activeLink = pagination.querySelector('.page-item.active .page-link');
        const allPageLinks = pagination.querySelectorAll('.page-item:not(.disabled) .page-link');
        
        if (!activeLink) return;
        
        const currentPage = activeLink.textContent.trim();
        let totalPages = 1;
        
        // 从所有页码链接中找到最大值作为总页数
        allPageLinks.forEach(link => {
            const pageNum = parseInt(link.textContent.trim());
            if (!isNaN(pageNum) && pageNum > totalPages) {
                totalPages = pageNum;
            }
        });

        // 创建页码指示器
        const indicator = document.createElement('li');
        indicator.className = 'page-item page-indicator';
        indicator.innerHTML = `
            <span class="page-link page-indicator-content">
                <i class="bi bi-layers"></i>
                <span class="page-text">${currentPage} / ${totalPages}</span>
            </span>
        `;

        // 插入到前后按钮之间
        const nextButton = pagination.querySelector('.page-item:last-child');
        if (nextButton) {
            pagination.insertBefore(indicator, nextButton);
        } else {
            pagination.appendChild(indicator);
        }
    },

    /**
     * 添加滑动提示
     */
    addSwipeHint(container) {
        // 移除现有提示
        const existingHint = container.querySelector('.swipe-hint');
        if (existingHint) existingHint.remove();

        // 创建滑动提示
        const hint = document.createElement('div');
        hint.className = 'swipe-hint';
        hint.innerHTML = `
            <i class="bi bi-arrow-left-right"></i>
            <span>左右滑动切换页面</span>
        `;
        
        container.appendChild(hint);

        // 3秒后自动隐藏
        setTimeout(() => {
            if (hint.parentNode) {
                hint.style.opacity = '0';
                setTimeout(() => hint.remove(), 300);
            }
        }, 3000);
    },

    /**
     * 添加触摸反馈
     */
    addTouchFeedback(pagination) {
        const pageLinks = pagination.querySelectorAll('.page-link');
        
        pageLinks.forEach(link => {
            // 移除现有事件监听器
            link.removeEventListener('touchstart', this.handleTouchStart);
            link.removeEventListener('touchend', this.handleTouchEnd);
            
            // 添加触摸反馈
            link.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
            link.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        });
    },

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        const target = e.currentTarget;
        target.style.transform = 'scale(0.95)';
        target.style.transition = 'transform 0.1s ease';
    },

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        const target = e.currentTarget;
        target.style.transform = '';
        setTimeout(() => {
            target.style.transition = '';
        }, 100);
    },

    /**
     * 设置触摸滑动支持
     */
    setupTouchSupport() {
        const paginationContainer = document.querySelector('.pagination-container');
        if (!paginationContainer) return;

        let startX = null;
        let startTime = null;

        // 触摸开始
        paginationContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startTime = Date.now();
        }, { passive: true });

        // 触摸结束
        paginationContainer.addEventListener('touchend', (e) => {
            if (!startX || !startTime) return;

            const endX = e.changedTouches[0].clientX;
            const endTime = Date.now();
            const deltaX = endX - startX;
            const deltaTime = endTime - startTime;

            // 检查是否为有效滑动
            if (Math.abs(deltaX) > this.config.touch.threshold && 
                deltaTime < this.config.touch.timeout) {
                
                if (deltaX > 0) {
                    // 向右滑动 - 上一页
                    this.navigateToPrevPage();
                } else {
                    // 向左滑动 - 下一页  
                    this.navigateToNextPage();
                }
            }

            startX = null;
            startTime = null;
        }, { passive: true });
    },

    /**
     * 导航到上一页
     */
    navigateToPrevPage() {
        const prevButton = document.querySelector('.pagination .page-item:first-child .page-link');
        if (prevButton && !prevButton.closest('.page-item').classList.contains('disabled')) {
            this.animatePageTransition(() => {
                prevButton.click();
            });
        }
    },

    /**
     * 导航到下一页
     */
    navigateToNextPage() {
        const nextButton = document.querySelector('.pagination .page-item:last-child .page-link');
        if (nextButton && !nextButton.closest('.page-item').classList.contains('disabled')) {
            this.animatePageTransition(() => {
                nextButton.click();
            });
        }
    },

    /**
     * 页面切换动画
     */
    animatePageTransition(callback) {
        if (this.state.isAnimating) return;
        
        this.state.isAnimating = true;
        const table = document.querySelector('#overdueTable');
        
        if (table) {
            table.style.opacity = '0.5';
            table.style.transform = 'translateX(-10px)';
            table.style.transition = `all ${this.config.animation.duration}ms ${this.config.animation.easing}`;
        }

        setTimeout(() => {
            callback();
            
            if (table) {
                table.style.opacity = '';
                table.style.transform = '';
                setTimeout(() => {
                    table.style.transition = '';
                    this.state.isAnimating = false;
                }, this.config.animation.duration);
            } else {
                this.state.isAnimating = false;
            }
        }, this.config.animation.duration / 2);
    },

    /**
     * 设置页面切换动画
     */
    setupPageTransitions() {
        const pageLinks = document.querySelectorAll('.pagination .page-link');
        
        pageLinks.forEach(link => {
            if (!link.getAttribute('href') || link.getAttribute('href') === '#') return;
            
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                
                this.animatePageTransition(() => {
                    window.location.href = href;
                });
            });
        });
    },

    /**
     * 设置窗口大小变化监听器
     */
    setupResizeListener() {
        let resizeTimer;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                const oldBreakpoint = this.state.currentBreakpoint;
                this.detectBreakpoint();
                
                // 只有当断点发生变化时才重新优化
                if (oldBreakpoint !== this.state.currentBreakpoint) {
                    console.log(`📱 断点变化: ${oldBreakpoint} → ${this.state.currentBreakpoint}`);
                    this.optimizePaginationAppearance();
                }
            }, 150);
        });
    },

    /**
     * 刷新分页管理器
     */
    refresh() {
        console.log('🔄 刷新逾期订单分页管理器');
        this.setup();
    }
};

// 自动初始化
OverdueOrdersPaginationManager.init();

// 导出到全局作用域
window.OverdueOrdersPaginationManager = OverdueOrdersPaginationManager; 