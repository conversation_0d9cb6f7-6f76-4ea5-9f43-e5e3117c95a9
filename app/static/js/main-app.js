/**
 * 主应用初始化模块
 * 协调所有模块的加载和初始化，替代原始的main.js
 */

// 确保基础对象存在，延迟检查具体模块
window.TaixiangApp = window.TaixiangApp || {};

// 延迟依赖检查函数
function checkAppDependencies() {
    const missing = [];
    if (!window.TaixiangApp.Utils) missing.push('Utils');
    if (!window.TaixiangApp.Navigation) missing.push('Navigation');
    if (!window.TaixiangApp.TableManager) missing.push('TableManager');
    
    if (missing.length > 0) {
        console.warn('以下模块未加载，部分功能可能受限:', missing.join(', '));
        return false;
    }
    return true;
}

// 主应用对象
TaixiangApp.App = {
    initialized: false,
    
    // 应用初始化
    init: function() {
        if (this.initialized) {
            console.log('应用已初始化，跳过重复初始化');
            return;
        }
        
        console.log('开始初始化太享查询应用');
        
        // 检查依赖但不阻止初始化
        const hasAllDependencies = checkAppDependencies();
        
        // 初始化各个模块（即使某些依赖缺失也尝试初始化）
        this.initializeCore();
        
        if (hasAllDependencies) {
            this.initializeExportManager();
            this.initializeTabs();
            this.initializeForms();
            this.initializeSidebar();
            this.handleUrlParams();
        } else {
            console.log('部分模块未加载，将延迟初始化');
            this.scheduleRetryInitialization();
        }
        this.setupEventListeners();
        this.finalizeInitialization();
        
        this.initialized = true;
        console.log('太享查询应用初始化完成');
    },
    
    // 初始化核心功能
    initializeCore: function() {
        console.log('初始化核心功能');
        
        // 优化移动设备显示
        TaixiangApp.Utils.optimizeForMobile();
        
        // 设置自动消失提示
        TaixiangApp.Utils.setupAutoDismissAlerts();
        
        // 初始化表格增强功能
        if (typeof TaixiangApp.TableManager.enhanceDataTables === 'function') {
            setTimeout(() => {
                TaixiangApp.TableManager.enhanceDataTables();
            }, 300);
        }
    },
    
    // 初始化导出管理器
    initializeExportManager: function() {
        if (typeof ExportManager !== 'undefined' && ExportManager.init) {
            ExportManager.init({
                fileNamePrefix: '太享数据',
                showSuccessMessage: true,
                includeTimestamp: true,
                confirmBeforeExport: true
            });
            console.log('导出管理器初始化完成');
        }
    },
    
    // 初始化数据选项卡（合并版本）
    initializeTabs: function() {
        console.log('初始化标签页系统');
        
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');
        const customerName = urlParams.get('customerName');
        const date = urlParams.get('date');
        
        // 如果URL中指定了标签页，激活对应的标签页
        if (tabParam) {
            console.log(`URL参数指定标签页: ${tabParam}`);
            const tabLink = document.querySelector(`[data-bs-target="#${tabParam}"]`);
            if (tabLink) {
                const tab = new bootstrap.Tab(tabLink);
                tab.show();
                console.log(`已激活标签页: ${tabParam}`);
            }
        }
        
        // 为选项卡切换事件添加处理器
        const tabLinks = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabLinks.forEach(tabLink => {
            tabLink.addEventListener('shown.bs.tab', (event) => {
                const targetId = event.target.getAttribute('data-bs-target').replace('#', '');
                console.log(`切换到标签页: ${targetId}`);
                
                // 重新设置自动消失提示
                TaixiangApp.Utils.setupAutoDismissAlerts();
                
                // 更新URL参数
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('tab', targetId);
                window.history.replaceState({}, '', currentUrl);
                
                // 如果标签页没有数据，则加载数据
                const targetTab = document.getElementById(targetId);
                if (targetTab && !targetTab.querySelector('.data-table')) {
                    TaixiangApp.DataLoader.loadTabData(targetId);
                }
                
                // 如果是逾期订单标签页，进行特殊处理
                if (targetId === 'overdue') {
                    this.handleOverdueTabActivation();
                }
            });
        });
        
                 // 处理初始数据加载 - 使用时间切片优化性能
         TaixiangApp.PerformanceMonitor.timeSlice(() => {
             if (tabParam) {
                 console.log(`根据URL参数加载${tabParam}标签页数据`);
                 return TaixiangApp.DataLoader.loadTabData(tabParam);
             } else if (customerName) {
                 console.log('根据客户姓名参数加载客户数据');
                 const tabLink = document.querySelector('[data-bs-target="#customer"]');
                 if (tabLink) {
                     const tab = new bootstrap.Tab(tabLink);
                     tab.show();
                 }
                 return TaixiangApp.DataLoader.loadCustomerData();
             } else if (date) {
                 console.log('根据日期参数加载筛选数据');
                 const tabLink = document.querySelector('[data-bs-target="#filter"]');
                 if (tabLink) {
                     const tab = new bootstrap.Tab(tabLink);
                     tab.show();
                 }
                 return TaixiangApp.DataLoader.loadFilterData();
             }
             return Promise.resolve();
         });
     },
    
    // 处理逾期订单标签页激活 - 性能优化版本
    handleOverdueTabActivation: function() {
        console.log('逾期订单标签页激活，应用特殊处理');
        // 使用分片处理避免长时间阻塞
        TaixiangApp.PerformanceMonitor.batchDOMUpdates([
            () => TaixiangApp.TableManager.enhanceDataTables(),
            () => {
                const tableElement = document.querySelector('#overdue .data-table');
                if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                    const dataTable = $(tableElement).DataTable();
                    dataTable.columns.adjust().responsive.recalc();
                }
            }
        ]);
    },
    
    // 初始化表单
    initializeForms: function() {
        console.log('初始化表单处理');
        
        // 表单提交时显示加载指示器
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                // 某些表单需要阻止默认提交
                const formId = this.id;
                if (['dateFilterForm', 'customerSearchForm', 'quickSearchForm'].includes(formId)) {
                    e.preventDefault();
                    
                    // 处理快速搜索表单
                    if (formId === 'quickSearchForm') {
                        TaixiangApp.QuickSearch.handleQuickSearch();
                    }
                    return;
                }
                
                TaixiangApp.Utils.showLoading();
                
                // 记录导航状态
                sessionStorage.setItem('navigatedFromSidebar', 'true');
                
                const linkText = this.textContent || '';
                if (linkText.includes('逾期订单查询')) {
                    sessionStorage.setItem('navigatingToOverdue', 'true');
                }
            });
        });
        
        // 绑定AJAX请求事件
        if (typeof $ !== 'undefined') {
            $(document).ajaxStart(function() {
                TaixiangApp.Utils.showLoading();
            }).ajaxStop(function() {
                TaixiangApp.Utils.hideLoading();
            });
        }
    },


    
    // 初始化侧边栏
    initializeSidebar: function() {
        console.log('初始化侧边栏');
        
        // 初始化侧边栏功能管理器
        TaixiangApp.SidebarFunctionManager.init();
        
        // 使用新的侧边栏管理器V2
        if (typeof TaixiangApp.SidebarManagerV2 !== 'undefined') {
            const sidebarManager = new TaixiangApp.SidebarManagerV2();
            sidebarManager.init();
            console.log('使用侧边栏管理器V2');
        } else if (!window.universalSidebarFix || !window.universalSidebarFix.initialized) {
            // 降级到旧版本管理器
            const sidebarManager = new TaixiangApp.SidebarManager();
            sidebarManager.init();
            console.log('降级使用旧版侧边栏管理器');
        } else {
            console.log('检测到通用侧边栏修复器已初始化，跳过主应用中的SidebarManager初始化');
        }
    },
    
    // 处理URL参数
    handleUrlParams: function() {
        console.log('处理URL参数');
        
        const urlParams = TaixiangApp.Navigation.getUrlParams();
        const tabParam = urlParams.get('tab');
        
        if (tabParam) {
            console.log('检测到URL参数tab=' + tabParam);
            
            if (TaixiangApp.Navigation.activateTab(tabParam)) {
                // 处理特定标签页的初始化
                this.handleSpecificTabInitialization(tabParam, urlParams);
            }
        }
    },
    
    // 处理特定标签页的初始化
    handleSpecificTabInitialization: function(tabParam, urlParams) {
        if (tabParam === 'overdue') {
            const pageParam = parseInt(urlParams.get('page')) || 1;
            
            // 重置分页状态
            TaixiangApp.State.resetOverdueState();
            TaixiangApp.State.overdueData.currentPage = pageParam;
            
            TaixiangApp.PerformanceMonitor.smartDelay(() => {
                if (typeof loadOverdueData === 'function') {
                    loadOverdueData(pageParam, false);
                }
            }, 'normal');
        } else if (tabParam === 'customer') {
            TaixiangApp.PerformanceMonitor.smartDelay(() => {
                const tableElement = document.querySelector('#customer .data-table');
                if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                    TaixiangApp.PerformanceMonitor.batchDOMUpdates([
                        () => $(tableElement).DataTable().columns.adjust(),
                        () => $(tableElement).DataTable().responsive.recalc()
                    ]);
                }
            }, 'low');
        }
    },
    
    // 设置事件监听器
    setupEventListeners: function() {
        console.log('设置全局事件监听器');
        
        // 窗口大小变化事件
        window.addEventListener('resize', this.handleWindowResize.bind(this));
        
        // 页面完全加载事件
        window.addEventListener('load', this.handlePageLoad.bind(this));
        
        // 为侧边栏链接添加加载指示器
        const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function() {
                TaixiangApp.Utils.showLoading();
                
                sessionStorage.setItem('navigatedFromSidebar', 'true');
                
                const linkText = this.textContent.trim();
                if (linkText.includes('逾期订单查询')) {
                    sessionStorage.setItem('navigatingToOverdue', 'true');
                }
            });
        });
    },
    
    // 处理窗口大小变化 - 高级性能优化版本
    handleWindowResize: function() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            console.log('处理窗口大小变化');

            // 使用分片处理，避免一次性处理太多表格
            const tables = document.querySelectorAll('.data-table');
            const chunks = Array.from(tables).reduce((acc, table, index) => {
                const chunkIndex = Math.floor(index / 2); // 每批处理2个表格
                if (!acc[chunkIndex]) acc[chunkIndex] = [];
                acc[chunkIndex].push(table);
                return acc;
            }, []);

            // 分批异步处理表格
            chunks.forEach((chunk, index) => {
                setTimeout(() => {
                    requestAnimationFrame(() => {
                        chunk.forEach(table => {
                            if ($.fn.DataTable.isDataTable(table)) {
                                $(table).DataTable().columns.adjust().responsive.recalc();
                            }
                        });
                    });
                }, index * 10); // 10ms间隔
            });

            // 优化移动端显示（轻量操作）
            requestAnimationFrame(() => {
                TaixiangApp.Utils.optimizeForMobile();
            });
        }, 150); // 减少延迟
    },
    
    // 处理页面加载完成
    handlePageLoad: function() {
        console.log('页面完全加载完成');
        
        TaixiangApp.PerformanceMonitor.smartDelay(() => {
            // 重新应用表格增强功能
            if (typeof TaixiangApp.TableManager.enhanceDataTables === 'function') {
                TaixiangApp.TableManager.enhanceDataTables();
            }
            
            // 处理导航状态
            this.handleNavigationState();
            
            // 隐藏加载指示器
            TaixiangApp.Utils.hideLoading();
        }, 'normal');
    },
    
    // 处理导航状态
    handleNavigationState: function() {
        const navigatedFromSidebar = sessionStorage.getItem('navigatedFromSidebar') === 'true';
        const navigatingToOverdue = sessionStorage.getItem('navigatingToOverdue') === 'true';
        
        if (navigatedFromSidebar && navigatingToOverdue) {
            console.log('检测到从侧边栏导航到逾期订单页面');
            
            TaixiangApp.PerformanceMonitor.smartDelay(() => {
                const overdueTab = document.getElementById('overdue-tab');
                if (overdueTab) {
                    overdueTab.click();
                    
                    const overdueContainer = document.getElementById('overdue');
                    const hasLoadingIndicator = overdueContainer && 
                        overdueContainer.querySelector('.spinner-border');
                    const hasTable = overdueContainer && 
                        overdueContainer.querySelector('table.data-table');
                    
                    if (hasLoadingIndicator && !hasTable) {
                        console.log('检测到逾期订单加载中但未完成，重新加载');
                        if (typeof loadOverdueData === 'function') {
                            loadOverdueData();
                        }
                    }
                    
                    if (typeof TaixiangApp.TableManager.enhanceDataTables === 'function') {
                        TaixiangApp.TableManager.enhanceDataTables();
                    }
                }
                
                // 清除导航标记
                sessionStorage.removeItem('navigatedFromSidebar');
                sessionStorage.removeItem('navigatingToOverdue');
            }, 'normal');
        }
    },
    
    // 安排重试初始化
    scheduleRetryInitialization: function() {
        console.log('安排重试初始化...');
        const retryDelay = 1000; // 1秒后重试
        
        setTimeout(() => {
            const hasAllDependencies = checkAppDependencies();
            if (hasAllDependencies && !this.initialized) {
                console.log('依赖模块已加载，重试初始化');
                this.initializeExportManager();
                this.initializeTabs();
                this.initializeForms();
                this.initializeSidebar();
                this.handleUrlParams();
            } else if (!hasAllDependencies) {
                console.log('依赖模块仍未完全加载，继续等待');
            }
        }, retryDelay);
    },
    
    // 最终化初始化
    finalizeInitialization: function() {
        console.log('完成初始化设置');
        
        // 如果有特定页面的初始化函数，调用它们
        if (typeof calculatePeriodDevicesCount === 'function' && 
            document.querySelector('#customerTabsContent')) {
            TaixiangApp.PerformanceMonitor.smartDelay(() => {
                calculatePeriodDevicesCount();
            }, 'low');
        }
        
        // 页面加载完成后，确保加载指示器已隐藏
        TaixiangApp.PerformanceMonitor.smartDelay(() => {
            TaixiangApp.Utils.hideLoading();
        }, 'low');
    }
};

// 向后兼容的全局函数
window.initializeTabs = TaixiangApp.App.initializeTabs.bind(TaixiangApp.App);
window.registerFormSubmitHandlers = TaixiangApp.App.initializeForms.bind(TaixiangApp.App);

// 当DOM加载完成时自动初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化应用');
    TaixiangApp.App.init();
});

console.log('主应用模块已加载');

// 性能监控工具 - 企业级性能优化方案
TaixiangApp.PerformanceMonitor = {
    // 监控长时间运行的任务
    warnLongTasks: function() {
        if (typeof PerformanceObserver !== 'undefined') {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > 50) {
                        console.warn(`⚠️ 长时间任务检测: ${entry.name || 'unknown'} 耗时 ${entry.duration.toFixed(2)}ms`);
                    }
                }
            });

            try {
                observer.observe({entryTypes: ['longtask']});
                console.log('✅ 性能监控已启动：长时间任务检测');
            } catch (e) {
                console.log('⚠️ 浏览器不支持长时间任务监控');
            }
        }
    },

    // 批量DOM操作工具 - 智能分片
    batchDOMUpdates: function(updates) {
        return new Promise((resolve) => {
            const CHUNK_SIZE = 2; // 每批处理2个操作
            const DELAY_BETWEEN_CHUNKS = 5; // 5ms间隔

            const processChunk = (chunk, index) => {
                setTimeout(() => {
                    requestAnimationFrame(() => {
                        chunk.forEach(update => {
                            try {
                                const startTime = performance.now();
                                update();
                                const endTime = performance.now();
                                if (endTime - startTime > 50) {
                                    console.warn(`⚠️ DOM操作耗时: ${(endTime - startTime).toFixed(2)}ms`);
                                }
                            } catch (e) {
                                console.error('批量DOM更新失败:', e);
                            }
                        });

                        // 检查是否为最后一批
                        if (index === chunks.length - 1) {
                            resolve();
                        }
                    });
                }, index * DELAY_BETWEEN_CHUNKS);
            };

            // 将更新操作分片
            const chunks = [];
            for (let i = 0; i < updates.length; i += CHUNK_SIZE) {
                chunks.push(updates.slice(i, i + CHUNK_SIZE));
            }

            chunks.forEach(processChunk);
        });
    },

    // 防抖动优化
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 时间切片优化 - 确保不阻塞主线程
    timeSlice: function(task, callback) {
        const SLICE_TIME = 16; // 16ms切片，保持60fps
        let startTime = performance.now();

        function executeSlice() {
            const currentTime = performance.now();

            // 如果在时间片内，继续执行
            if (currentTime - startTime < SLICE_TIME) {
                try {
                    const result = task();
                    if (result && typeof result.then === 'function') {
                        // 如果返回Promise，等待完成
                        result.then(() => {
                            if (callback) callback();
                        }).catch(e => {
                            console.error('时间切片任务失败:', e);
                            if (callback) callback(e);
                        });
                    } else {
                        if (callback) callback();
                    }
                } catch (e) {
                    console.error('时间切片任务失败:', e);
                    if (callback) callback(e);
                }
            } else {
                // 时间片用完，延迟到下一帧
                requestAnimationFrame(() => {
                    startTime = performance.now();
                    executeSlice();
                });
            }
        }

        requestAnimationFrame(executeSlice);
    },

    // 智能延迟执行
    smartDelay: function(func, priority = 'normal') {
        const delays = {
            high: 0,
            normal: 5,
            low: 16
        };

        const delay = delays[priority] || delays.normal;

        if (delay === 0) {
            return requestAnimationFrame(func);
        } else {
            return setTimeout(() => {
                requestAnimationFrame(func);
            }, delay);
        }
    }
};