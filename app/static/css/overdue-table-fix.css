/**
 * 逾期订单表格修复样式
 * 这个CSS文件专门修复逾期订单表格在导航切换后的列宽错位问题
 * 并确保与日期筛选表格样式一致
 */

/* 确保逾期表格与筛选表格样式一致 */
#overdue .data-table,
#filter .data-table {
    width: 100% !important;
    margin: 0 !important;
    border-collapse: separate;
    border-spacing: 0;
}

/* 确保表头样式一致 */
#overdue .data-table thead th,
#filter .data-table thead th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    text-align: center !important;
    padding: 12px 10px;
}

/* 确保内容单元格样式一致 */
#overdue .data-table tbody td,
#filter .data-table tbody td {
    padding: 10px;
    text-align: center !important;
    vertical-align: middle;
}

/* 控制列样式统一 */
#overdue .data-table td.dtr-control,
#filter .data-table td.dtr-control,
#customer .data-table td.dtr-control {
    position: relative;
    text-align: center;
    cursor: pointer;
    padding-left: 0;
    padding-right: 0;
    width: 40px;
    min-width: 40px;
    vertical-align: middle;
}

/* 展开/折叠按钮样式统一 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    content: '+';
    background-color: #007bff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 0.25rem rgba(0, 123, 255, 0.5);
    position: absolute;
    line-height: 1;
    font-size: 16px;
}

/* 确保展开状态按钮样式一致 */
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
    content: '-';
    background-color: #dc3545;
}

/* 允许单元格内容在展开时正常显示 */
#overdue .data-table td .cell-content,
#filter .data-table td .cell-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 确保展开状态下的单元格内容可以完全显示 */
#overdue .data-table td .cell-content.expanded,
#filter .data-table td .cell-content.expanded {
    white-space: normal;
    overflow: visible;
    max-width: none;
    z-index: 100;
    position: relative;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 剩余特殊列宽设置保持不变 */
#overdue .data-table th:first-child,
#overdue .data-table td:first-child {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
}

/* 针对常见列的宽度设置 */
#overdue .data-table th:nth-child(2),  /* 订单编号 */
#overdue .data-table td:nth-child(2) {
    width: 130px !important;
}

#overdue .data-table th:nth-child(3),  /* 客户姓名 */
#overdue .data-table td:nth-child(3) {
    width: 100px !important;
}

#overdue .data-table th:nth-child(4),  /* 贷后状态 */
#overdue .data-table td:nth-child(4) {
    width: 100px !important;
}

#overdue .data-table th:nth-child(5),  /* 客户手机 */
#overdue .data-table td:nth-child(5) {
    width: 120px !important;
}

#overdue .data-table th:nth-child(6),  /* 客服归属 */
#overdue .data-table td:nth-child(6) {
    width: 100px !important;
}

#overdue .data-table th:nth-child(7),  /* 业务归属 */
#overdue .data-table td:nth-child(7) {
    width: 100px !important;
}

#overdue .data-table th:nth-child(8),  /* 产品 */
#overdue .data-table td:nth-child(8) {
    width: 90px !important;
}

#overdue .data-table th:nth-child(9),  /* 期数 */
#overdue .data-table td:nth-child(9) {
    width: 60px !important;
}

#overdue .data-table th:nth-child(10),  /* 总待收 */
#overdue .data-table td:nth-child(10) {
    width: 120px !important;
}

#overdue .data-table th:nth-child(11),  /* 当前待收 */
#overdue .data-table td:nth-child(11) {
    width: 120px !important;
}

#overdue .data-table th:nth-child(12),  /* 首次逾期期数 */
#overdue .data-table td:nth-child(12) {
    width: 120px !important;
}

#overdue .data-table th:nth-child(13),  /* 账单日期 */
#overdue .data-table td:nth-child(13) {
    width: 100px !important;
}

#overdue .data-table th:nth-child(14),  /* 备注信息 */
#overdue .data-table td:nth-child(14) {
    width: 150px !important;
    min-width: 150px !important;
}

/* 确保状态样式一致性 - 应用到两个表格 */
#overdue .data-table tr[data-status],
#filter .data-table tr[data-status] {
    transition: background-color 0.2s ease;
}

/* 移动设备优化保持一致 */
@media (max-width: 768px) {
    #overdue .data-table th,
    #overdue .data-table td,
    #filter .data-table th,
    #filter .data-table td,
    #customer .data-table th,
    #customer .data-table td {
        min-width: 60px !important;
        padding: 8px 6px;
    }
    
    #overdue .data-table td.dtr-control,
    #filter .data-table td.dtr-control,
    #customer .data-table td.dtr-control {
        min-width: 40px !important;
        width: 40px !important;
    }
    
    /* 移动设备下的折叠按钮样式优化 */
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        width: 18px;
        height: 18px;
        font-size: 14px;
    }
}

/* 确保即使DataTables重新计算宽度也不会覆盖这些规则 */
#overdue .data-table th {
    box-sizing: border-box !important;
    position: relative !important;
}

/* 防止宽表格造成页面水平滚动条 */
#overdue .table-responsive {
    overflow-x: auto !important;
    max-width: 100% !important;
}

/* 确保按钮在各种状态下都正确居中 */
.data-table td.dtr-control,
.data-table th.dtr-control {
    position: relative !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* 确保所有表格的按钮位置保持一致，不仅仅是特定的表格 */
.table-responsive .data-table td.dtr-control::before {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
}

/* 统一表格行和单元格样式 */
#overdue .data-table tr,
#filter .data-table tr {
    transition: background-color 0.2s ease-in-out;
}

/* 贷后状态单元格样式 */
#overdue .data-table td[class="逾期还款"],
#overdue .data-table td[class="逾期未还"],
#overdue .data-table td[class="催收"],
#overdue .data-table td[class="诉讼"] {
    font-weight: 600;
    background-color: rgba(255, 199, 206, 0.4);  /* 浅红色 */
    color: #d63031;
}

#overdue .data-table td[class="提前还款"] {
    font-weight: 500;
    background-color: rgba(209, 216, 224, 0.3);  /* 浅灰色 */
    color: #0984e3;
}

#overdue .data-table td[class="按时还款"] {
    font-weight: 500;
    background-color: rgba(200, 247, 197, 0.3);  /* 浅绿色 */
    color: #00b894;
}

#overdue .data-table td[class="账单日"] {
    font-weight: 500;
    background-color: rgba(255, 234, 167, 0.3);  /* 浅黄色 */
    color: #f39c12;
}

/* 确保行的hover效果一致 */
#overdue .data-table tbody tr:hover,
#filter .data-table tbody tr:hover,
#customer .data-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* 统一表格内部的字体和对齐方式 */
#overdue .data-table tbody td,
#filter .data-table tbody td,
#customer .data-table tbody td {
    font-size: 14px;
    font-family: "Microsoft YaHei", sans-serif;
    text-align: center;
}

/* 确保状态色彩在所有表格中一致显示 */
tr[data-status="逾期还款"],
tr[data-status="逾期未还"],
tr[data-status="催收"],
tr[data-status="诉讼"] {
    background-color: rgba(255, 199, 206, 0.2) !important;
}

tr[data-status="提前还款"] {
    background-color: rgba(217, 225, 242, 0.2) !important;
}

tr[data-status="按时还款"] {
    background-color: rgba(198, 239, 206, 0.2) !important;
}

tr[data-status="账单日"] {
    background-color: rgba(244, 176, 132, 0.2) !important;
}

/* 统一表格中的文本处理 */
.data-table td {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 统一单元格内容样式 */
.data-table .cell-content {
    text-align: left;
    display: block;
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: all 0.2s ease;
}

.data-table .cell-content.expanded {
    white-space: normal;
    max-width: none;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 10;
    position: relative;
}

/* 统一金额类型数字的格式 */
#overdue .data-table td:nth-child(10),
#overdue .data-table td:nth-child(11),
#filter .data-table td:nth-child(10),
#filter .data-table td:nth-child(11),
#customer .data-table td:nth-child(10),
#customer .data-table td:nth-child(11) {
    text-align: right !important;
    font-family: Consolas, monospace;
    font-weight: 500;
}
