/**
 * 企业级紧凑型响应式表格样式
 * 解决DataTables响应式展开内容占用过多垂直空间的问题
 * 
 * 核心改进：
 * 1. 使用CSS网格布局，每行显示多个字段
 * 2. 优化视觉层次和间距
 * 3. 提升用户体验和空间利用率
 * 4. 支持多种设备尺寸的响应式布局
 */

/* ==================== 核心响应式详情容器 ==================== */

.dtr-details {
    width: 100% !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 8px !important;
    margin: 8px 0 !important;
    padding: 12px 16px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e3e6ea !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 详情标题栏 */
.dtr-details::before {
    content: "详细信息";
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 2px solid #007bff;
    font-size: 0.9em;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* ==================== 网格布局系统 ==================== */

/* 主网格容器 */
.dtr-details ul,
.dtr-details .dtr-details-list {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 8px 16px !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    align-items: start !important;
}

/* 每个详情项 */
.dtr-details li {
    display: flex !important;
    align-items: flex-start !important;
    background: rgba(255, 255, 255, 0.7) !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    border: 1px solid rgba(0, 123, 255, 0.1) !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    margin: 0 !important;
    min-height: 40px !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 详情项悬停效果 */
.dtr-details li:hover {
    background: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(0, 123, 255, 0.2) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
}

/* ==================== 字段样式系统 ==================== */

/* 字段标签 */
.dtr-title {
    font-weight: 600 !important;
    color: #495057 !important;
    min-width: 80px !important;
    max-width: 100px !important;
    font-size: 0.85em !important;
    margin-right: 8px !important;
    flex-shrink: 0 !important;
    line-height: 1.4 !important;
    padding-right: 6px !important;
    border-right: 2px solid #e9ecef !important;
    text-align: right !important;
}

/* 字段数据 */
.dtr-data {
    flex: 1 !important;
    word-break: break-word !important;
    font-size: 0.9em !important;
    color: #212529 !important;
    line-height: 1.4 !important;
    padding-left: 8px !important;
    min-width: 0 !important;
    overflow-wrap: break-word !important;
}

/* ==================== 数据类型特殊样式 ==================== */

/* 金额字段 */
.dtr-details li[data-field-type="amount"] {
    border-left: 3px solid #007bff !important;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05), rgba(0, 123, 255, 0.02)) !important;
}

.dtr-details li[data-field-type="amount"] .dtr-data {
    font-weight: 600 !important;
    color: #007bff !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* 状态字段 */
.dtr-details li[data-field-type="status"] {
    border-left: 3px solid #28a745 !important;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(40, 167, 69, 0.02)) !important;
}

/* 日期字段 */
.dtr-details li[data-field-type="date"] {
    border-left: 3px solid #ffc107 !important;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.02)) !important;
}

/* 联系信息字段 */
.dtr-details li[data-field-type="contact"] {
    border-left: 3px solid #17a2b8 !important;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.05), rgba(23, 162, 184, 0.02)) !important;
}

/* ==================== 数据增强样式 ==================== */

/* 徽章样式优化 */
.dtr-data .badge {
    font-size: 0.8em !important;
    padding: 3px 6px !important;
    margin: 0 !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
}

/* 重要数据高亮 */
.dtr-data .amount-field {
    font-weight: 600 !important;
    color: #007bff !important;
    font-family: 'Segoe UI', monospace !important;
}

/* 链接样式 */
.dtr-data a {
    color: #007bff !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

.dtr-data a:hover {
    text-decoration: underline !important;
    color: #0056b3 !important;
}

/* ==================== 响应式断点优化 ==================== */

/* 大屏幕 (1200px+) - 最多3列 */
@media (min-width: 1200px) {
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
        gap: 10px 20px !important;
    }
}

/* 中等屏幕 (768px-1199px) - 最多2列 */
@media (min-width: 768px) and (max-width: 1199px) {
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
        gap: 8px 16px !important;
    }
}

/* 平板设备 (576px-767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .dtr-details {
        padding: 10px 14px !important;
    }
    
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)) !important;
        gap: 6px 12px !important;
    }
    
    .dtr-details li {
        padding: 7px 10px !important;
        min-height: 36px !important;
    }
    
    .dtr-title {
        min-width: 75px !important;
        max-width: 90px !important;
        font-size: 0.8em !important;
    }
    
    .dtr-data {
        font-size: 0.85em !important;
    }
}

/* 手机设备 (最大575px) - 单列布局 */
@media (max-width: 575px) {
    .dtr-details {
        margin: 6px 0 !important;
        padding: 10px 12px !important;
    }
    
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        grid-template-columns: 1fr !important;
        gap: 4px !important;
    }
    
    .dtr-details li {
        padding: 6px 10px !important;
        min-height: 32px !important;
    }
    
    .dtr-title {
        min-width: 70px !important;
        max-width: 85px !important;
        font-size: 0.8em !important;
    }
    
    .dtr-data {
        font-size: 0.85em !important;
    }
}

/* 超小屏幕 (最大375px) */
@media (max-width: 375px) {
    .dtr-details {
        margin: 4px 0 !important;
        padding: 8px 10px !important;
    }
    
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        gap: 3px !important;
    }
    
    .dtr-details li {
        padding: 5px 8px !important;
        min-height: 30px !important;
    }
    
    .dtr-title {
        min-width: 60px !important;
        max-width: 75px !important;
        font-size: 0.75em !important;
    }
    
    .dtr-data {
        font-size: 0.8em !important;
    }
}

/* ==================== 动画和交互效果 ==================== */

/* 展开动画 */
.dtr-details {
    animation: fadeInUp 0.3s ease-out !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 内容项渐入动画 */
.dtr-details li {
    animation: fadeInItem 0.2s ease-out !important;
    animation-fill-mode: both !important;
}

@keyframes fadeInItem {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 延迟动画效果 */
.dtr-details li:nth-child(1) { animation-delay: 0.05s !important; }
.dtr-details li:nth-child(2) { animation-delay: 0.1s !important; }
.dtr-details li:nth-child(3) { animation-delay: 0.15s !important; }
.dtr-details li:nth-child(4) { animation-delay: 0.2s !important; }
.dtr-details li:nth-child(5) { animation-delay: 0.25s !important; }
.dtr-details li:nth-child(6) { animation-delay: 0.3s !important; }

/* ==================== 兼容性和回退 ==================== */

/* 为不支持网格布局的旧浏览器提供回退 */
@supports not (display: grid) {
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        display: flex !important;
        flex-wrap: wrap !important;
    }
    
    .dtr-details li {
        width: calc(50% - 8px) !important;
        margin: 4px !important;
    }
    
    @media (max-width: 768px) {
        .dtr-details li {
            width: 100% !important;
        }
    }
}

/* ==================== 打印样式 ==================== */

@media print {
    .dtr-details {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        page-break-inside: avoid !important;
    }
    
    .dtr-details ul,
    .dtr-details .dtr-details-list {
        gap: 2px 8px !important;
    }
    
    .dtr-details li {
        background: white !important;
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
} 