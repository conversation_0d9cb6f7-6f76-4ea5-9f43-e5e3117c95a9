/**
 * 企业级紧凑型响应式表格样式系统
 * 版本: 2.0
 * 目标: 解决逾期订单页面空间占用过多的问题，提高企业级项目的信息密度
 */

/* ==================== 紧凑型响应式详情样式 ==================== */

/* 详情容器 - 紧凑企业级设计 */
.dtr-details {
    width: 100% !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 6px !important;
    margin: 6px 0 !important;
    padding: 8px 12px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e3e6ea !important;
    position: relative !important;
    max-height: 240px !important; /* 限制最大高度 */
    overflow-y: auto !important;  /* 允许垂直滚动 */
}

/* 详情标题栏 - 紧凑设计 */
.dtr-details::before {
    content: "详细信息";
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 6px;
    padding-bottom: 4px;
    border-bottom: 1px solid #007bff;
    font-size: 0.8em;
    letter-spacing: 0.3px;
}

/* 使用紧凑网格布局显示详情内容 */
.dtr-details ul {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)) !important;
    gap: 4px 12px !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

/* 每个详情项样式 - 紧凑设计 */
.dtr-details li {
    display: flex !important;
    align-items: center !important;
    background: rgba(255, 255, 255, 0.6) !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    border: 1px solid rgba(0, 123, 255, 0.08) !important;
    transition: all 0.15s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
    margin: 0 !important;
    min-height: 28px !important;
    font-size: 0.85em !important;
}

/* 详情项悬停效果 */
.dtr-details li:hover {
    background: rgba(255, 255, 255, 0.85) !important;
    border-color: rgba(0, 123, 255, 0.15) !important;
    transform: translateY(-0.5px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

/* 字段标签样式 - 紧凑设计 */
.dtr-title {
    font-weight: 600 !important;
    color: #495057 !important;
    min-width: 65px !important;
    max-width: 85px !important;
    font-size: 0.8em !important;
    margin-right: 6px !important;
    flex-shrink: 0 !important;
    line-height: 1.3 !important;
    padding-right: 4px !important;
    border-right: 1px solid #e9ecef !important;
}

/* 字段数据样式 - 紧凑设计 */
.dtr-data {
    flex: 1 !important;
    word-break: break-word !important;
    font-size: 0.85em !important;
    color: #212529 !important;
    line-height: 1.3 !important;
    padding-left: 6px !important;
    min-width: 0 !important;
}

/* 重要数据高亮 */
.dtr-data .badge {
    font-size: 0.75em !important;
    padding: 2px 4px !important;
    margin: 0 !important;
}

.dtr-data .amount-field {
    font-weight: 600 !important;
    color: #007bff !important;
    font-family: 'Segoe UI', monospace !important;
}

/* 特殊字段的样式增强 - 简化版本 */
.dtr-details li[data-field-type="amount"] {
    border-left: 2px solid #007bff !important;
}

.dtr-details li[data-field-type="status"] {
    border-left: 2px solid #28a745 !important;
}

.dtr-details li[data-field-type="date"] {
    border-left: 2px solid #ffc107 !important;
}

.dtr-details li[data-field-type="contact"] {
    border-left: 2px solid #17a2b8 !important;
}

/* ==================== 移动设备优化 ==================== */
@media (max-width: 768px) {
    .dtr-details {
        margin: 4px 0 !important;
        padding: 6px 8px !important;
        max-height: 180px !important;
    }
    
    .dtr-details ul {
        grid-template-columns: 1fr !important;
        gap: 3px !important;
    }
    
    .dtr-details li {
        padding: 3px 6px !important;
        min-height: 24px !important;
        font-size: 0.8em !important;
    }
    
    .dtr-title {
        min-width: 55px !important;
        max-width: 70px !important;
        font-size: 0.75em !important;
    }
    
    .dtr-data {
        font-size: 0.8em !important;
    }
    
    .dtr-details::before {
        font-size: 0.75em;
        margin-bottom: 4px;
        padding-bottom: 3px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 576px) {
    .dtr-details {
        margin: 3px 0 !important;
        padding: 5px 6px !important;
        max-height: 150px !important;
    }
    
    .dtr-details ul {
        gap: 2px !important;
    }
    
    .dtr-details li {
        padding: 2px 4px !important;
        min-height: 20px !important;
        font-size: 0.75em !important;
    }
    
    .dtr-title {
        min-width: 45px !important;
        max-width: 60px !important;
        font-size: 0.7em !important;
    }
    
    .dtr-data {
        font-size: 0.75em !important;
    }
    
    .dtr-details::before {
        font-size: 0.7em;
        margin-bottom: 3px;
        padding-bottom: 2px;
    }
}

/* ==================== 企业级表格增强 ==================== */

/* 表格行间距优化 */
.data-table tbody tr {
    height: 42px; /* 固定行高，提高信息密度 */
}

.data-table tbody td {
    padding: 6px 8px !important; /* 紧凑的内边距 */
    vertical-align: middle !important;
}

/* 表头紧凑化 */
.data-table thead th {
    padding: 8px 6px !important;
    font-size: 0.9em;
    line-height: 1.2;
}

/* 响应式控制列紧凑化 */
.data-table td.dtr-control,
.data-table th.dtr-control {
    width: 30px !important;
    min-width: 30px !important;
    max-width: 30px !important;
    padding: 4px !important;
}

/* 展开/折叠按钮紧凑化 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    width: 16px;
    height: 16px;
    font-size: 12px;
    border-radius: 50%;
    line-height: 1;
}

/* 表格容器紧凑化 */
.table-responsive {
    margin-bottom: 1rem; /* 减少底部间距 */
}

/* 分页控件紧凑化 */
.pagination-container {
    margin-top: 12px;
    margin-bottom: 20px;
}

/* ==================== 工具提示和状态指示器 ==================== */

/* 信息密度指示器 */
.info-density-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7em;
    z-index: 1000;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

/* 紧凑模式激活指示 */
.compact-mode-active .data-table {
    font-size: 0.85rem !important;
}

.compact-mode-active .dtr-details {
    max-height: 200px !important;
}

/* ==================== 性能优化 ==================== */

/* 减少动画和过渡效果，提高性能 */
.performance-mode .dtr-details li {
    transition: none !important;
}

.performance-mode .dtr-details li:hover {
    transform: none !important;
}

/* 表格虚拟化准备 */
.virtualized-table {
    contain: layout style paint;
}

.virtualized-table .data-table {
    table-layout: fixed;
}

/* ==================== 企业级主题变量 ==================== */
:root {
    --enterprise-compact-spacing: 4px;
    --enterprise-detail-max-height: 240px;
    --enterprise-row-height: 42px;
    --enterprise-border-radius: 4px;
    --enterprise-shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.04);
    --enterprise-shadow-hover: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 使用CSS变量提高可维护性 */
.dtr-details {
    border-radius: var(--enterprise-border-radius) !important;
    max-height: var(--enterprise-detail-max-height) !important;
    box-shadow: var(--enterprise-shadow-subtle) !important;
}

.dtr-details li:hover {
    box-shadow: var(--enterprise-shadow-hover) !important;
}

.data-table tbody tr {
    height: var(--enterprise-row-height);
}