<!-- 404错误页面 -->
<template>
  <div class="not-found">
    <div class="error-content">
      <h1>404</h1>
      <h2>页面未找到</h2>
      <p>抱歉，您访问的页面不存在。</p>
      <a-button type="primary" @click="goHome">
        返回首页
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};
</script>

<style lang="scss" scoped>
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  
  .error-content {
    h1 {
      font-size: 6rem;
      font-weight: bold;
      color: $primary-500;
      margin-bottom: $space-4;
    }
    
    h2 {
      font-size: 2rem;
      margin-bottom: $space-4;
    }
    
    p {
      font-size: 1.2rem;
      color: $text-secondary;
      margin-bottom: $space-6;
    }
  }
}
</style>
