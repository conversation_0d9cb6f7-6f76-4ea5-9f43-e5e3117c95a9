// API类型定义示例 - 基于现有Flask API设计
// frontend/src/api/types/index.ts

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页信息
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// 缓存信息
export interface CacheInfo {
  last_update: string;
  next_update: string;
}

// 数据查询相关类型
export interface FilterDataParams {
  date: string; // YYYY-MM-DD格式
}

export interface FilterDataResponse {
  results: OrderRecord[];
  columns: string[];
  pagination?: Pagination;
  cache_info?: CacheInfo;
}

export interface OrderRecord {
  订单编号: string;
  客户姓名: string;
  客户手机: string;
  订单日期: string;
  账单日期: string;
  当前待收: number;
  总待收: number;
  成本: number;
  金额: number;
  业务: string;
  客服: string;
  产品: string;
  产品类型: string;
  逾期天数?: number;
  逾期期数?: number;
  状态?: string;
  备注?: string;
  [key: string]: any; // 支持动态期数字段
}

// 客户查询类型
export interface CustomerQueryParams {
  customer_name: string;
}

export interface CustomerOrderResponse {
  results: OrderRecord[];
  columns: string[];
}

// 逾期订单类型
export interface OverdueOrderParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface OverdueOrderResponse {
  results: OrderRecord[];
  columns: string[];
  pagination: Pagination;
  cache_info: CacheInfo;
}

// 客户汇总类型
export interface CustomerSummaryParams {
  customer_name?: string;
  phone?: string;
}

export interface CustomerSummaryData {
  customer_name: string;
  phone: string;
  total_orders: number;
  total_financing: number;
  current_outstanding: number;
  total_repaid: number;
  overdue_amount: number;
  overdue_orders: number;
  order_details: OrderRecord[];
}

// 数据汇总类型
export interface DataSummaryParams {
  start_date: string;
  end_date: string;
}

export interface DataSummaryResponse {
  summary_data: {
    total_orders: number;
    total_amount: number;
    overdue_orders: number;
    overdue_amount: number;
    [key: string]: any;
  };
  chart_data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string;
      borderColor?: string;
    }>;
  };
}

// 导出相关类型
export interface ExportParams {
  format: 'excel' | 'csv';
  data_type: 'filter' | 'customer' | 'overdue';
  search_params?: Record<string, any>;
}

// 用户权限类型
export interface UserInfo {
  username: string;
  permission_level: 'limited' | 'standard' | 'full';
  is_authenticated: boolean;
}

// API服务类设计
// frontend/src/api/services/dataService.ts

import { http } from '../client';
import type {
  FilterDataParams,
  FilterDataResponse,
  CustomerQueryParams,
  CustomerOrderResponse,
  OverdueOrderParams,
  OverdueOrderResponse,
  CustomerSummaryParams,
  CustomerSummaryData,
  DataSummaryParams,
  DataSummaryResponse,
  ExportParams
} from '../types';

export class DataService {
  // 日期筛选数据
  static async getFilterData(params: FilterDataParams): Promise<FilterDataResponse> {
    return http.get('/api/filter_data', { params });
  }

  // 客户订单查询
  static async getCustomerOrders(params: CustomerQueryParams): Promise<CustomerOrderResponse> {
    return http.get('/api/filter_orders_by_customer_name', { params });
  }

  // 逾期订单查询
  static async getOverdueOrders(params: OverdueOrderParams): Promise<OverdueOrderResponse> {
    return http.get('/api/filter_overdue_orders', { params });
  }

  // 客户汇总数据
  static async getCustomerSummary(params: CustomerSummaryParams): Promise<CustomerSummaryData> {
    return http.get('/api/customer_summary', { params });
  }

  // 数据汇总
  static async getDataSummary(params: DataSummaryParams): Promise<DataSummaryResponse> {
    return http.get('/api/summary_data', { params });
  }

  // 数据导出
  static async exportData(params: ExportParams): Promise<Blob> {
    return http.post('/api/export', params, {
      responseType: 'blob'
    });
  }
}

// HTTP客户端配置
// frontend/src/api/client.ts

import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios';
import { ElMessage } from 'element-plus'; // 或使用Ant Design的message
import { useUserStore } from '@/stores/user';

class HttpClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token等
        const userStore = useUserStore();
        if (userStore.token) {
          config.headers.Authorization = `Bearer ${userStore.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response.data;
      },
      (error) => {
        // 统一错误处理
        const message = error.response?.data?.message || error.message || '请求失败';
        ElMessage.error(message);
        
        // 处理认证错误
        if (error.response?.status === 401) {
          const userStore = useUserStore();
          userStore.logout();
        }
        
        return Promise.reject(error);
      }
    );
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config);
  }
}

export const http = new HttpClient();
