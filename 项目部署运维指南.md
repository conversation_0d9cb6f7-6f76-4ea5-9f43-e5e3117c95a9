# 太享查询系统 - 项目部署运维指南

## 项目概述

太享查询系统是一个基于Flask框架的金融数据查询展示系统，用于展示和分析从API获取的金融数据。

### 功能特点
- 用户登录与权限控制（三级权限：有限、标准、完全）
- 根据日期筛选金融数据
- 逾期订单查询与展示
- 客户订单查询与汇总数据
- 数据汇总分析与图表展示
- 数据导出功能（Excel、CSV）
- 常用工具：计算器、二维码生成、日历、合同生成、回执单生成等
- **新增功能**：
  - 🆕 **二维码生成工具**：支持多种尺寸，完全本地化生成
  - 🆕 **前端代码优化**：JavaScript模块化，统一表格样式
  - 🆕 **用户体验提升**：响应式设计，移动端优化

### 技术栈
- **后端**: Flask, Pandas, Requests, QRCode, Pillow
- **前端**: Bootstrap 5, Chart.js, DataTables, 模块化JavaScript
- **数据处理**: Pandas, Matplotlib
- **部署**: Gunicorn, Nginx, Systemd

### 环境要求
- Python 3.8+
- Ubuntu Server (推荐)
- 依赖包详见 `requirements.txt`
- **新增系统依赖**：图像处理库、字体支持

## 快速部署

### 一键部署方法

1. **上传项目文件**
   ```bash
   # 确保项目文件在指定目录
   /var/www/hdsc_query_app
   ```

2. **执行部署脚本**
   ```bash
   chmod +x deploy.sh
   sudo ./deploy.sh
   ```

3. **访问系统**
   ```
   http://**************:5000
   ```

### 手动部署步骤

#### 1. 安装系统依赖
```bash
sudo apt update

# 基础开发工具
sudo apt install -y python3 python3-pip python3-venv python3-dev build-essential libssl-dev

# 二维码生成功能所需的图像处理库依赖
sudo apt install -y libjpeg-dev zlib1g-dev libfreetype6-dev liblcms2-dev libwebp-dev
sudo apt install -y libtiff5-dev libopenjp2-7-dev libffi-dev libjpeg8-dev libpng-dev
sudo apt install -y tcl8.6-dev tk8.6-dev python3-tk

# 其他必要的系统依赖
sudo apt install -y libxml2-dev libxslt1-dev python3-pil python3-matplotlib python3-flask

# 字体支持（用于二维码生成时的文本渲染）
sudo apt install -y fonts-dejavu-core fonts-liberation ttf-wqy-zenhei

# 数据处理库依赖
sudo apt install -y libpython3-dev cython3 python3-numpy python3-wheel python3-pandas
```

#### 2. 创建虚拟环境
```bash
cd /var/www/hdsc_query_app
python3 -m venv .venv
source .venv/bin/activate
```

#### 3. 安装Python依赖
```bash
pip install --upgrade pip
pip install wheel numpy setuptools

# 多策略安装pandas
pip install "pandas~=2.1.0" || \
pip install pandas || \
pip install pandas==1.5.3

# 安装二维码生成功能依赖
pip install qrcode[pil]==7.4.2
pip install Pillow==9.5.0

# 安装其他依赖
pip install -r requirements.txt
pip install gunicorn
```

#### 4. 配置环境变量
```bash
cat > .env << EOF
FLASK_APP=run.py
FLASK_ENV=production
API_KEY=lxw8025031
SECRET_KEY=$(python3 -c "import os; print(os.urandom(24).hex())")
EOF
```

#### 5. 创建Systemd服务
```bash
sudo nano /etc/systemd/system/hdsc-query.service
```

服务配置文件内容：
```ini
[Unit]
Description=太享查询系统Web服务
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/hdsc_query_app
Environment="PATH=/var/www/hdsc_query_app/.venv/bin"
Environment="FLASK_APP=run.py"
Environment="FLASK_ENV=production"
ExecStart=/var/www/hdsc_query_app/.venv/bin/gunicorn --workers 3 --bind **************:5000 --access-logfile /var/www/hdsc_query_app/access.log --error-logfile /var/www/hdsc_query_app/error.log run:app

[Install]
WantedBy=multi-user.target
```

#### 6. 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable hdsc-query
sudo systemctl start hdsc-query
```

## 配置说明

### 用户权限配置
- **有限权限**: `TT2024`
- **标准权限**: `881017`
- **完全权限**: `Doolin`

### API服务器配置
- **新API地址**: `*************:5000`
- **旧API地址**: `***********:5000`（兼容）
- **API密钥**: `lxw8025031`

### 项目结构
```
hdsc_query_app/
├── app/
│   ├── static/            # 静态资源
│   │   ├── css/          # 样式文件
│   │   │   ├── unified-table-styles.css  # 统一表格样式
│   │   │   └── data-table.css            # 数据表格样式
│   │   └── js/           # JavaScript文件
│   │       ├── modules/  # 模块化JS文件
│   │       │   ├── unified-table-manager.js
│   │       │   ├── table-manager.js
│   │       │   └── data-display.js
│   │       └── controllers/  # 控制器文件
│   │           ├── data-query-controller.js
│   │           └── style-controller.js
│   ├── templates/         # HTML模板
│   ├── models/            # 数据模型
│   ├── routes/            # 路由
│   │   └── main.py       # 包含二维码生成路由
│   ├── services/          # 业务服务
│   └── utils/             # 工具函数
├── config.py              # 配置文件
├── requirements.txt       # 依赖项（包含qrcode[pil]==7.4.2）
├── run.py                 # 应用入口
└── deploy.sh              # 部署脚本（已更新）
```

## 新功能详解

### 二维码生成功能
- **位置**: 首页"常用工具"区域
- **功能**: 支持网址、文本、联系方式等各种内容
- **尺寸**: 128x128、256x256、512x512三种尺寸
- **特性**: 完全本地化生成，无需外部API
- **操作**: 一键生成、下载PNG、复制到剪贴板

### 前端优化特性
- **JavaScript模块化**: 将大型JS文件拆分为独立模块
- **统一表格样式**: 确保所有页面表格样式一致
- **响应式设计**: 优化移动端显示效果
- **用户体验提升**: 改进交互反馈和视觉效果

## 服务管理

### 基本命令
```bash
# 启动服务
sudo systemctl start hdsc-query

# 停止服务
sudo systemctl stop hdsc-query

# 重启服务
sudo systemctl restart hdsc-query

# 查看状态
sudo systemctl status hdsc-query

# 查看日志
sudo journalctl -u hdsc-query -f
```

### 日志文件
- **访问日志**: `/var/www/hdsc_query_app/access.log`
- **错误日志**: `/var/www/hdsc_query_app/error.log`
- **调试日志**: `/var/www/hdsc_query_app/debug_output.log`
- **二维码测试日志**: `/var/www/hdsc_query_app/qrcode_test.log`

## 故障排除

### 常见问题

#### 1. pandas安装失败
**症状**: 安装失败、ModuleNotFoundError
**解决方案**:
```bash
# 尝试兼容性安装
pip install "pandas~=2.1.0"

# 尝试最新版本
pip install pandas

# 尝试稳定旧版本
pip install pandas==1.5.3

# 使用系统pandas
sudo apt install -y python3-pandas
pip install --no-deps pandas
```

#### 2. 二维码生成功能失败
**症状**: 二维码生成报错、图像处理失败
**解决方案**:
```bash
# 检查qrcode库安装
python3 -c "import qrcode; print(qrcode.__version__)"

# 检查Pillow库安装
python3 -c "import PIL; print(PIL.__version__)"

# 重新安装二维码相关依赖
pip install --force-reinstall qrcode[pil]==7.4.2
pip install --force-reinstall Pillow==9.5.0

# 安装系统图像处理库
sudo apt install -y python3-pil libjpeg-dev libpng-dev
```

#### 3. 前端样式问题
**症状**: 表格样式不一致、响应式布局异常
**解决方案**:
```bash
# 检查前端文件是否存在
ls -la /var/www/hdsc_query_app/app/static/css/unified-table-styles.css
ls -la /var/www/hdsc_query_app/app/static/js/modules/

# 设置正确的文件权限
sudo chmod 644 /var/www/hdsc_query_app/app/static/css/*.css
sudo chmod 644 /var/www/hdsc_query_app/app/static/js/**/*.js

# 清除浏览器缓存或强制刷新页面
```

#### 4. Flask-Login兼容性问题
**症状**: `TypeError: urlencode() got an unexpected keyword argument 'sort'`
**解决方案**:
```bash
pip install Flask-Login==0.6.2 --upgrade
pip install Werkzeug==2.3.7
```

#### 5. 服务无法启动
**检查步骤**:
```bash
# 检查日志
cat /var/www/hdsc_query_app/error.log
sudo journalctl -u hdsc-query

# 手动测试
cd /var/www/hdsc_query_app
source .venv/bin/activate
python run.py

# 测试二维码功能
python3 -c "
import qrcode
qr = qrcode.QRCode()
qr.add_data('测试')
qr.make()
print('二维码功能正常')
"
```

#### 6. 端口冲突
```bash
# 检查端口占用
sudo lsof -i:5000

# 杀死占用进程
sudo kill -9 <PID>
```

#### 7. 权限问题
```bash
# 修复权限
sudo chown -R www-data:www-data /var/www/hdsc_query_app
sudo chmod -R 755 /var/www/hdsc_query_app

# 特别设置静态资源权限
sudo chmod -R 755 /var/www/hdsc_query_app/app/static
sudo find /var/www/hdsc_query_app/app/static -name "*.css" -exec chmod 644 {} \;
sudo find /var/www/hdsc_query_app/app/static -name "*.js" -exec chmod 644 {} \;
```

## 开发环境设置

### 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd hdsc_query_app

# 创建虚拟环境
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装二维码生成依赖
pip install qrcode[pil]==7.4.2

# 配置环境变量
# Windows
set FLASK_ENV=development
set API_KEY=lxw8025031
set SECRET_KEY=your_secret_key

# Linux/macOS
export FLASK_ENV=development
export API_KEY=lxw8025031
export SECRET_KEY=your_secret_key

# 启动应用
python run.py
```

### 功能测试
```bash
# 测试二维码生成功能
python3 -c "
from app import create_app
app = create_app('development')
with app.test_client() as client:
    response = client.post('/generate_qrcode', 
        json={'content': '测试二维码', 'size': 256})
    print('二维码生成测试:', response.status_code)
"

# 测试前端模块加载
curl -I http://localhost:5000/static/js/modules/unified-table-manager.js
curl -I http://localhost:5000/static/css/unified-table-styles.css
```

## 安全建议

1. **防火墙配置**
   ```bash
   sudo ufw allow 5000/tcp
   sudo ufw enable
   ```

2. **定期更新**
   - 定期更新系统和依赖包
   - 监控安全漏洞
   - 特别关注图像处理库的安全更新

3. **备份策略**
   - 定期备份数据和配置文件
   - 备份静态资源文件
   - 测试恢复流程

4. **日志监控**
   - 监控错误日志
   - 监控二维码生成请求
   - 设置告警机制

## 性能优化

### Gunicorn配置
```python
# gunicorn_config.py
bind = "**************:5000"
workers = 4
timeout = 120
errorlog = "logs/gunicorn-error.log"
accesslog = "logs/gunicorn-access.log"
loglevel = "info"
proc_name = "hdsc_query_app"

# 针对二维码生成的优化
worker_class = "sync"  # 同步工作模式适合CPU密集型任务
max_requests = 1000    # 定期重启工作进程
max_requests_jitter = 100
```

### 前端性能优化
- **静态资源缓存**: 设置CSS/JS文件的缓存头
- **图片优化**: 二维码图片使用适当的压缩
- **模块化加载**: 按需加载JavaScript模块

### Nginx反向代理（可选）
```nginx
server {
    listen 80;
    server_name **************;

    location / {
        proxy_pass http://**************:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /var/www/hdsc_query_app/app/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 二维码生成请求的特殊处理
    location /generate_qrcode {
        proxy_pass http://**************:5000;
        proxy_read_timeout 60s;
        proxy_connect_timeout 10s;
    }
}
```

## 更新和迁移

### 版本更新
```bash
# 停止服务
sudo systemctl stop hdsc-query

# 备份当前版本
cp -r /var/www/hdsc_query_app /var/www/hdsc_query_app.backup

# 更新代码
git pull origin main

# 更新依赖
source .venv/bin/activate
pip install -r requirements.txt

# 检查新功能
python3 -c "import qrcode; print('二维码功能可用')"

# 重启服务
sudo systemctl start hdsc-query
```

### 数据迁移
- 备份SQLite数据库文件
- 导出配置文件
- 备份静态资源文件
- 测试新版本兼容性

## 监控和维护

### 系统监控
```bash
# 检查服务状态
sudo systemctl status hdsc-query

# 监控资源使用
htop
df -h

# 检查二维码生成功能
curl -X POST http://localhost:5000/generate_qrcode \
  -H "Content-Type: application/json" \
  -d '{"content":"测试","size":256}'
```

### 定期维护任务
- 清理临时文件和日志
- 检查磁盘空间使用
- 更新系统和依赖包
- 测试关键功能（包括二维码生成）
- 备份重要数据

## 联系方式

如有部署问题，请联系系统管理员或开发团队获取支持。

**特别说明**: 本版本新增了二维码生成功能和前端优化，如遇到相关问题，请优先检查图像处理库依赖和静态资源文件权限。

---
**最后更新**: 2024年12月  
**维护团队**: 太享查询系统开发组  
**版本**: v2.0 - 包含二维码生成功能和前端优化 