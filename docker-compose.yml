services:
  hdsc-query-app:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    image: hdsc-query-app:latest
    container_name: hdsc-query-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载缓存目录
      - ./cache:/app/cache
      # 挂载配置文件（如果需要动态修改）
      - ./config.py:/app/config.py:ro
      - ./gunicorn_config.py:/app/gunicorn_config.py:ro
      # 挂载文档目录
      - ./AHT:/app/AHT:ro
      - ./BHT:/app/BHT:ro
      - ./HZ:/app/HZ:ro
    environment:
      - FLASK_ENV=production
      - FLASK_APP=run.py
      - PYTHONPATH=/app
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - hdsc-network

networks:
  hdsc-network:
    driver: bridge
    name: hdsc-network

# 可选：如果需要数据库服务，可以取消注释以下配置
# volumes:
#   postgres_data:

# services:
#   postgres:
#     image: postgres:15-alpine
#     container_name: hdsc-postgres
#     restart: unless-stopped
#     environment:
#       POSTGRES_DB: hdsc_query
#       POSTGRES_USER: hdsc_user
#       POSTGRES_PASSWORD: hdsc_password
#     volumes:
#       - postgres_data:/var/lib/postgresql/data
#     ports:
#       - "5432:5432"
#     networks:
#       - hdsc-network
