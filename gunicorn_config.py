# gunicorn配置文件
import multiprocessing

# 工作进程数，根据CPU核心数自动设置
workers = multiprocessing.cpu_count() * 2 + 1
bind = "0.0.0.0:5000"

# 工作模式
worker_class = "sync"

# 超时设置 - 适应长时间API调用（企业信用查询可能需要10分钟）
timeout = 700  # 设置为700秒（约11.7分钟），略大于API超时时间
keepalive = 5

# 日志设置
accesslog = "./access.log"
errorlog = "./error.log"
loglevel = "info"

# 进程名称
proc_name = "hdsc_query"

# 启动时预加载应用
preload_app = True

# 定时任务初始化
def post_worker_init(worker):
    from app import create_app
    from app.services.scheduled_tasks import init_scheduler
    app = create_app('production')
    with app.app_context():
        try:
            init_scheduler(app)
            print("定时任务初始化成功")
        except Exception as e:
            print(f"定时任务初始化失败: {e}")
