# 开发环境配置
VITE_APP_ENV=development
VITE_APP_DEBUG=true

# API配置 - 请根据您的后端服务器配置修改
VITE_API_BASE_URL=http://localhost:5000
VITE_API_TIMEOUT=30000

# 常见后端框架的默认端口配置（取消注释使用）：
# VITE_API_BASE_URL=http://localhost:3000  # Node.js/Express
# VITE_API_BASE_URL=http://localhost:8080  # Spring Boot
# VITE_API_BASE_URL=http://localhost:8000  # Django/Laravel
# VITE_API_BASE_URL=http://localhost:5000  # Flask/FastAPI

# 开发工具
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK=false
VITE_ENABLE_CONSOLE_LOG=true

# 代理配置 - 解决跨域问题
VITE_PROXY_ENABLED=true

# API响应格式配置
VITE_API_RESPONSE_FORMAT=standard
# 可选值：
# - standard: { code, message, data }
# - laravel: { success, data, message }
# - simple: 直接返回数据

# 认证配置
VITE_AUTH_TOKEN_KEY=auth_token
VITE_AUTH_STORAGE_TYPE=localStorage

# 性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true
