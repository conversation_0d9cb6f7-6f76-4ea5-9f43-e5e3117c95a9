@echo off
chcp 65001 >nul
title 太享查询系统

echo 🚀 太享查询系统启动脚本
echo ==========================

REM 检查Node.js
echo 📋 检查环境...
node -v >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+ 版本
    pause
    exit /b 1
)

echo ✅ Node.js 版本: 
node -v
echo ✅ npm 版本: 
npm -v

REM 检查依赖
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖已安装
)

REM 检查环境配置
if not exist ".env.development" (
    echo ⚙️ 创建默认环境配置...
    (
        echo # 开发环境配置
        echo VITE_APP_ENV=development
        echo VITE_APP_DEBUG=true
        echo.
        echo # API配置
        echo VITE_API_BASE_URL=http://localhost:5000
        echo VITE_API_TIMEOUT=30000
        echo VITE_API_RESPONSE_FORMAT=standard
        echo.
        echo # 开发工具
        echo VITE_ENABLE_DEVTOOLS=true
        echo VITE_ENABLE_MOCK=false
        echo VITE_ENABLE_CONSOLE_LOG=true
        echo.
        echo # 代理配置
        echo VITE_PROXY_ENABLED=true
        echo.
        echo # 认证配置
        echo VITE_AUTH_TOKEN_KEY=auth_token
        echo VITE_AUTH_STORAGE_TYPE=localStorage
        echo.
        echo # 性能监控
        echo VITE_ENABLE_PERFORMANCE_MONITOR=true
    ) > .env.development
    echo ✅ 已创建默认环境配置
) else (
    echo ✅ 环境配置已存在
)

REM 显示配置信息
echo.
echo 📋 当前配置:
findstr "VITE_API_BASE_URL" .env.development
findstr "VITE_ENABLE_MOCK" .env.development
findstr "VITE_ENABLE_CONSOLE_LOG" .env.development

REM 询问是否配置API
echo.
set /p configure_api="🔧 是否需要配置API连接? (y/n): "
if /i "%configure_api%"=="y" (
    echo 🎛️ 启动API配置向导...
    npm run setup-api
)

REM 启动开发服务器
echo.
echo 🚀 启动开发服务器...
echo    本地地址: http://localhost:3000
echo    网络地址: http://0.0.0.0:3000
echo.
echo 💡 提示:
echo    - 按 Ctrl+C 停止服务器
echo    - 访问 /api-config 页面测试API连接
echo    - 查看浏览器控制台获取调试信息
echo.

REM 启动服务器
npm run dev

pause
