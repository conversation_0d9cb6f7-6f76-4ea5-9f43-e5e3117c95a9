#!/bin/bash

# 太享查询系统启动脚本
# 自动检查环境并启动开发服务器

echo "🚀 太享查询系统启动脚本"
echo "=========================="

# 检查Node.js版本
echo "📋 检查环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+ 版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "⚠️  Node.js 版本过低 (当前: $(node -v))，建议使用 18+ 版本"
fi

echo "✅ Node.js 版本: $(node -v)"
echo "✅ npm 版本: $(npm -v)"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
else
    echo "✅ 依赖已安装"
fi

# 检查环境配置
if [ ! -f ".env.development" ]; then
    echo "⚙️  创建默认环境配置..."
    cat > .env.development << EOF
# 开发环境配置
VITE_APP_ENV=development
VITE_APP_DEBUG=true

# API配置
VITE_API_BASE_URL=http://localhost:5000
VITE_API_TIMEOUT=30000
VITE_API_RESPONSE_FORMAT=standard

# 开发工具
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK=false
VITE_ENABLE_CONSOLE_LOG=true

# 代理配置
VITE_PROXY_ENABLED=true

# 认证配置
VITE_AUTH_TOKEN_KEY=auth_token
VITE_AUTH_STORAGE_TYPE=localStorage

# 性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true
EOF
    echo "✅ 已创建默认环境配置"
else
    echo "✅ 环境配置已存在"
fi

# 显示配置信息
echo ""
echo "📋 当前配置:"
echo "   API地址: $(grep VITE_API_BASE_URL .env.development | cut -d'=' -f2)"
echo "   Mock数据: $(grep VITE_ENABLE_MOCK .env.development | cut -d'=' -f2)"
echo "   调试日志: $(grep VITE_ENABLE_CONSOLE_LOG .env.development | cut -d'=' -f2)"

# 询问是否配置API
echo ""
read -p "🔧 是否需要配置API连接? (y/n): " configure_api
if [[ $configure_api =~ ^[Yy]$ ]]; then
    echo "🎛️  启动API配置向导..."
    npm run setup-api
fi

# 启动开发服务器
echo ""
echo "🚀 启动开发服务器..."
echo "   本地地址: http://localhost:3000"
echo "   网络地址: http://0.0.0.0:3000"
echo ""
echo "💡 提示:"
echo "   - 按 Ctrl+C 停止服务器"
echo "   - 访问 /api-config 页面测试API连接"
echo "   - 查看浏览器控制台获取调试信息"
echo ""

# 启动服务器
npm run dev
