<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="太享查询系统 - 现代化金融数据查询展示系统" />
    <meta name="keywords" content="金融数据,查询系统,数据分析,Vue3,TypeScript" />
    <meta name="author" content="太享科技" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="太享查询系统" />
    <meta property="og:description" content="现代化金融数据查询展示系统" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="太享查询系统" />
    <meta property="twitter:description" content="现代化金融数据查询展示系统" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- 字体加载 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    
    <title>太享查询系统</title>
    
    <!-- 内联关键CSS以提升首屏性能 -->
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
      }
      
      #loading.fade-out {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 16px;
        color: #6b7280;
        font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        font-size: 14px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 基础样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      html {
        font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      body {
        background-color: #f9fafb;
        color: #111827;
        line-height: 1.5;
      }
      
      /* 暗色主题支持 */
      @media (prefers-color-scheme: dark) {
        body {
          background-color: #1f2937;
          color: #f3f4f6;
        }
        
        #loading {
          background: #1f2937;
        }
        
        .loading-spinner {
          border-color: #374151;
          border-top-color: #3b82f6;
        }
        
        .loading-text {
          color: #9ca3af;
        }
      }
    </style>
  </head>
  <body>
    <!-- 应用容器 -->
    <div id="app">
      <!-- 初始加载动画 -->
      <div id="loading">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">系统加载中...</div>
        </div>
      </div>
    </div>
    
    <!-- 应用脚本 -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 加载完成后隐藏加载动画 -->
    <script>
      window.addEventListener('load', function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.classList.add('fade-out');
          setTimeout(() => {
            loading.style.display = 'none';
          }, 300);
        }
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
        
        // 可以在这里添加错误上报逻辑
      });
      
      // Promise错误处理
      window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled promise rejection:', e.reason);
        
        // 可以在这里添加错误上报逻辑
      });
    </script>
  </body>
</html>
