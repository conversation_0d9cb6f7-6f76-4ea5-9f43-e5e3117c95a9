# 🔌 API连接配置指南

本指南将帮助您将前端连接到真实的后端API，替换Mock数据。

## 🚀 快速开始

### 方法一：使用配置向导（推荐）

```bash
# 运行API配置向导
npm run setup-api
```

向导将引导您完成以下配置：
1. 选择后端框架类型
2. 配置API基础地址
3. 设置响应格式
4. 配置调试选项

### 方法二：手动配置

编辑 `.env.development` 文件：

```bash
# API配置
VITE_API_BASE_URL=http://localhost:5000  # 您的后端API地址
VITE_API_TIMEOUT=30000
VITE_API_RESPONSE_FORMAT=standard
VITE_ENABLE_MOCK=false                   # 禁用Mock数据
VITE_ENABLE_CONSOLE_LOG=true            # 启用调试日志
VITE_PROXY_ENABLED=true                 # 启用代理解决跨域
```

## 📋 后端API要求

### 必需的API端点

您的后端需要提供以下API端点：

#### 1. 数据查询
```
GET /api/filter_data
参数: start_date, end_date, page, page_size
返回: 订单数据列表
```

#### 2. 客户查询
```
GET /api/filter_orders_by_customer_name
参数: customer_name, start_date, end_date
返回: 客户订单数据
```

#### 3. 逾期订单
```
GET /api/filter_overdue_orders
参数: end_date
返回: 逾期订单列表
```

#### 4. 数据汇总
```
GET /api/customer_summary
GET /api/summary_data
GET /api/order_summary
GET /api/overdue_summary
参数: start_date, end_date
返回: 汇总统计数据
```

#### 5. 数据导出
```
POST /api/export
参数: start_date, end_date, format
返回: 文件流 (Excel/CSV)
```

### 响应格式

#### 标准格式 (推荐)
```json
{
  "code": 200,
  "message": "success",
  "data": [...],
  "total": 100,
  "page": 1,
  "pageSize": 20
}
```

#### Laravel格式
```json
{
  "success": true,
  "data": [...],
  "message": "success",
  "meta": {
    "total": 100,
    "current_page": 1,
    "per_page": 20
  }
}
```

#### 简单格式
```json
[...] // 直接返回数据数组
```

## 🔧 常见后端框架配置

### Flask/FastAPI (Python)

```python
from flask import Flask, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # 解决跨域问题

@app.route('/api/filter_data')
def filter_data():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 您的业务逻辑
    data = get_orders_by_date(start_date, end_date)
    
    return jsonify({
        'code': 200,
        'message': 'success',
        'data': data,
        'total': len(data)
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
```

### Express.js (Node.js)

```javascript
const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors()); // 解决跨域问题
app.use(express.json());

app.get('/api/filter_data', (req, res) => {
  const { start_date, end_date } = req.query;
  
  // 您的业务逻辑
  const data = getOrdersByDate(start_date, end_date);
  
  res.json({
    code: 200,
    message: 'success',
    data: data,
    total: data.length
  });
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

### Spring Boot (Java)

```java
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*") // 解决跨域问题
public class DataController {
    
    @GetMapping("/filter_data")
    public ResponseEntity<?> filterData(
        @RequestParam String start_date,
        @RequestParam String end_date
    ) {
        // 您的业务逻辑
        List<Order> data = orderService.getOrdersByDate(start_date, end_date);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "success");
        response.put("data", data);
        response.put("total", data.size());
        
        return ResponseEntity.ok(response);
    }
}
```

### Laravel (PHP)

```php
<?php

Route::get('/api/filter_data', function (Request $request) {
    $startDate = $request->get('start_date');
    $endDate = $request->get('end_date');
    
    // 您的业务逻辑
    $data = Order::whereBetween('order_date', [$startDate, $endDate])->get();
    
    return response()->json([
        'success' => true,
        'data' => $data,
        'message' => 'success',
        'meta' => [
            'total' => $data->count()
        ]
    ]);
});
```

## 🧪 测试API连接

### 1. 使用内置测试工具

启动前端后，访问：
```
http://localhost:3000/api-config
```

这个页面提供：
- API配置界面
- 连接测试工具
- 完整的API测试套件
- 实时连接状态监控

### 2. 使用浏览器控制台

```javascript
// 在浏览器控制台运行
fetch('http://localhost:5000/api/filter_data?start_date=2024-01-01&end_date=2024-01-31')
  .then(response => response.json())
  .then(data => console.log('API响应:', data))
  .catch(error => console.error('API错误:', error));
```

### 3. 使用命令行测试

```bash
# 测试API连接
curl "http://localhost:5000/api/filter_data?start_date=2024-01-01&end_date=2024-01-31"
```

## 🔍 故障排除

### 常见问题

#### 1. 跨域错误 (CORS)
**错误**: `Access to fetch at 'http://localhost:5000' from origin 'http://localhost:3000' has been blocked by CORS policy`

**解决方案**:
- 在后端添加CORS支持
- 或使用前端代理（已在vite.config.ts中配置）

#### 2. 连接被拒绝
**错误**: `Failed to fetch` 或 `Connection refused`

**解决方案**:
- 确保后端服务器正在运行
- 检查端口号是否正确
- 检查防火墙设置

#### 3. 404错误
**错误**: `404 Not Found`

**解决方案**:
- 检查API端点路径是否正确
- 确保后端路由配置正确

#### 4. 数据格式错误
**错误**: 前端显示数据异常

**解决方案**:
- 检查后端响应格式是否符合要求
- 在浏览器开发者工具中查看API响应
- 调整 `VITE_API_RESPONSE_FORMAT` 配置

### 调试技巧

1. **启用调试日志**
   ```bash
   VITE_ENABLE_CONSOLE_LOG=true
   ```

2. **查看网络请求**
   - 打开浏览器开发者工具
   - 切换到 Network 标签
   - 查看API请求和响应

3. **使用API测试工具**
   - 访问 `/api-config` 页面
   - 运行完整的API测试套件

## 📚 数据字段映射

前端期望的数据字段格式：

```javascript
{
  "订单编号": "ORD20240115001",
  "客户姓名": "张三",
  "客户手机": "138****8888",
  "订单日期": "2024-01-15",
  "订单金额": 50000,
  "当前待收": 35000,
  "业务类型": "电商",
  "产品类型": "分期产品",
  "客服": "客服A",
  "状态": "正常",
  "逾期天数": 0
}
```

如果您的后端使用不同的字段名，数据服务会自动进行映射转换。

## 🎯 下一步

1. **配置API连接** - 使用 `npm run setup-api` 或手动配置
2. **启动后端服务** - 确保您的后端API正在运行
3. **测试连接** - 访问 `/api-config` 页面测试
4. **验证功能** - 逐个测试各个页面功能
5. **优化性能** - 根据实际数据量调整缓存和分页设置

如果您在配置过程中遇到问题，请查看浏览器控制台的错误信息，或联系技术支持。
