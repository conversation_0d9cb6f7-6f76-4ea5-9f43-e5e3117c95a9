#!/usr/bin/env node

/**
 * API连接设置脚本
 * 帮助快速配置和测试API连接
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(colors[color] + message + colors.reset);
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 常见后端框架配置
const frameworkConfigs = {
  'flask': {
    name: 'Flask/FastAPI',
    defaultPort: 5000,
    defaultPath: '/api',
    responseFormat: 'standard'
  },
  'express': {
    name: 'Node.js/Express',
    defaultPort: 3000,
    defaultPath: '/api',
    responseFormat: 'standard'
  },
  'spring': {
    name: 'Spring Boot',
    defaultPort: 8080,
    defaultPath: '/api',
    responseFormat: 'standard'
  },
  'django': {
    name: 'Django',
    defaultPort: 8000,
    defaultPath: '/api',
    responseFormat: 'standard'
  },
  'laravel': {
    name: 'Laravel',
    defaultPort: 8000,
    defaultPath: '/api',
    responseFormat: 'laravel'
  },
  'custom': {
    name: '自定义配置',
    defaultPort: null,
    defaultPath: null,
    responseFormat: 'standard'
  }
};

async function main() {
  colorLog('cyan', '🚀 API连接配置向导');
  colorLog('cyan', '='.repeat(50));
  
  console.log('这个向导将帮助您配置前端与后端API的连接。\n');

  // 1. 选择后端框架
  colorLog('yellow', '1. 请选择您的后端框架:');
  Object.entries(frameworkConfigs).forEach(([key, config], index) => {
    console.log(`   ${index + 1}. ${config.name}`);
  });
  
  const frameworkChoice = await question('\n请输入选项编号 (1-6): ');
  const frameworkKeys = Object.keys(frameworkConfigs);
  const selectedFramework = frameworkKeys[parseInt(frameworkChoice) - 1];
  
  if (!selectedFramework) {
    colorLog('red', '❌ 无效选择，退出配置');
    rl.close();
    return;
  }

  const config = frameworkConfigs[selectedFramework];
  colorLog('green', `✅ 已选择: ${config.name}`);

  // 2. 配置API地址
  let apiBaseURL;
  if (selectedFramework === 'custom') {
    apiBaseURL = await question('\n请输入完整的API地址 (例: http://localhost:3000/api): ');
  } else {
    const defaultURL = `http://localhost:${config.defaultPort}${config.defaultPath}`;
    const customURL = await question(`\n默认API地址: ${defaultURL}\n是否使用默认地址? (y/n): `);
    
    if (customURL.toLowerCase() === 'y' || customURL === '') {
      apiBaseURL = defaultURL;
    } else {
      apiBaseURL = await question('请输入自定义API地址: ');
    }
  }

  // 3. 配置响应格式
  colorLog('yellow', '\n3. 请选择API响应格式:');
  console.log('   1. 标准格式 { code, message, data }');
  console.log('   2. Laravel格式 { success, data, message }');
  console.log('   3. 简单格式 (直接返回数据)');
  
  const formatChoice = await question('\n请输入选项编号 (1-3): ');
  const responseFormats = ['standard', 'laravel', 'simple'];
  const responseFormat = responseFormats[parseInt(formatChoice) - 1] || config.responseFormat;

  // 4. 其他配置
  const enableMock = await question('\nAPI失败时是否启用Mock数据? (y/n): ');
  const enableConsoleLog = await question('是否启用控制台调试日志? (y/n): ');

  // 5. 生成配置
  const envConfig = {
    VITE_API_BASE_URL: apiBaseURL,
    VITE_API_TIMEOUT: '30000',
    VITE_API_RESPONSE_FORMAT: responseFormat,
    VITE_ENABLE_MOCK: enableMock.toLowerCase() === 'y' ? 'true' : 'false',
    VITE_ENABLE_CONSOLE_LOG: enableConsoleLog.toLowerCase() === 'y' ? 'true' : 'false',
    VITE_PROXY_ENABLED: 'true'
  };

  // 6. 写入配置文件
  const envPath = path.join(__dirname, '../.env.development');
  let envContent = '';
  
  // 读取现有配置
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }

  // 更新配置
  Object.entries(envConfig).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const newLine = `${key}=${value}`;
    
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, newLine);
    } else {
      envContent += `\n${newLine}`;
    }
  });

  // 写入文件
  fs.writeFileSync(envPath, envContent);

  // 7. 显示配置结果
  colorLog('green', '\n✅ 配置完成！');
  colorLog('cyan', '\n📋 配置摘要:');
  console.log(`   API地址: ${apiBaseURL}`);
  console.log(`   响应格式: ${responseFormat}`);
  console.log(`   Mock数据: ${envConfig.VITE_ENABLE_MOCK}`);
  console.log(`   调试日志: ${envConfig.VITE_ENABLE_CONSOLE_LOG}`);

  // 8. 提供下一步指导
  colorLog('yellow', '\n🎯 下一步操作:');
  console.log('   1. 启动您的后端服务器');
  console.log('   2. 运行 npm run dev 启动前端开发服务器');
  console.log('   3. 访问 http://localhost:3000/api-config 测试API连接');
  console.log('   4. 如果连接失败，请检查后端服务器是否正常运行');

  // 9. 生成测试脚本
  const testScript = `
// API连接测试脚本
// 在浏览器控制台中运行此脚本来测试API连接

async function testApiConnection() {
  const apiUrl = '${apiBaseURL}';
  console.log('🔗 测试API连接:', apiUrl);
  
  try {
    const response = await fetch(apiUrl + '/filter_data?start_date=2024-01-01&end_date=2024-01-31');
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API连接成功:', data);
      return true;
    } else {
      console.error('❌ API响应错误:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ API连接失败:', error.message);
    return false;
  }
}

// 运行测试
testApiConnection();
`;

  fs.writeFileSync(path.join(__dirname, '../test-api.js'), testScript);
  
  colorLog('cyan', '\n💡 提示:');
  console.log('   - 已生成 test-api.js 文件，可在浏览器控制台运行测试');
  console.log('   - 配置已保存到 .env.development 文件');
  console.log('   - 如需修改配置，可重新运行此脚本或手动编辑环境文件');

  rl.close();
}

// 错误处理
process.on('SIGINT', () => {
  colorLog('yellow', '\n\n👋 配置已取消');
  rl.close();
  process.exit(0);
});

// 运行主程序
main().catch((error) => {
  colorLog('red', '❌ 配置过程中发生错误:');
  console.error(error);
  rl.close();
  process.exit(1);
});
