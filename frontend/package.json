{"name": "hdsc-frontend", "version": "1.0.0", "description": "太享查询系统 - 现代化前端重构版本", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit", "prepare": "husky install", "setup-api": "node scripts/setup-api.js", "test-api": "node test-api.js"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "ant-design-vue": "^4.1.0", "@ant-design/icons-vue": "^7.0.1", "echarts": "^5.4.3", "axios": "^1.6.0", "@vueuse/core": "^10.7.0", "dayjs": "^1.11.10", "qrcode": "^1.5.3"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "sass": "^1.69.0", "typescript": "~5.3.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vitest": "^1.0.0", "vue-tsc": "^1.8.0", "@playwright/test": "^1.40.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{css,scss,less,html,json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}