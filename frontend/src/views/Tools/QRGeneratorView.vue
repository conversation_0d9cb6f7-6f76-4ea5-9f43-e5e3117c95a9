<!-- 二维码生成工具页面 -->
<template>
  <div class="qr-generator-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">二维码生成器</h1>
        <p class="page-description">快速生成各种类型的二维码，支持文本、链接、联系人等</p>
      </div>
    </div>

    <a-row :gutter="24">
      <!-- 左侧配置区域 -->
      <a-col :span="12">
        <div class="config-panel">
          <a-card title="二维码配置" :bordered="false">
            <!-- 二维码类型选择 -->
            <div class="config-section">
              <h4>二维码类型</h4>
              <a-radio-group v-model:value="qrType" @change="handleTypeChange">
                <a-radio value="text">文本</a-radio>
                <a-radio value="url">网址链接</a-radio>
                <a-radio value="wifi">WiFi</a-radio>
                <a-radio value="contact">联系人</a-radio>
                <a-radio value="sms">短信</a-radio>
                <a-radio value="email">邮箱</a-radio>
              </a-radio-group>
            </div>

            <!-- 内容输入区域 -->
            <div class="config-section">
              <h4>内容设置</h4>
              
              <!-- 文本类型 -->
              <template v-if="qrType === 'text'">
                <a-textarea
                  v-model:value="qrContent.text"
                  placeholder="请输入要生成二维码的文本内容"
                  :rows="4"
                  @input="generateQR"
                />
              </template>

              <!-- 网址类型 -->
              <template v-else-if="qrType === 'url'">
                <a-input
                  v-model:value="qrContent.url"
                  placeholder="请输入网址，如：https://www.example.com"
                  @input="generateQR"
                >
                  <template #prefix>
                    <LinkOutlined />
                  </template>
                </a-input>
              </template>

              <!-- WiFi类型 -->
              <template v-else-if="qrType === 'wifi'">
                <a-space direction="vertical" style="width: 100%">
                  <a-input
                    v-model:value="qrContent.wifi.ssid"
                    placeholder="WiFi名称(SSID)"
                    @input="generateQR"
                  />
                  <a-input-password
                    v-model:value="qrContent.wifi.password"
                    placeholder="WiFi密码"
                    @input="generateQR"
                  />
                  <a-select
                    v-model:value="qrContent.wifi.security"
                    placeholder="加密类型"
                    style="width: 100%"
                    @change="generateQR"
                  >
                    <a-select-option value="WPA">WPA/WPA2</a-select-option>
                    <a-select-option value="WEP">WEP</a-select-option>
                    <a-select-option value="nopass">无密码</a-select-option>
                  </a-select>
                  <a-checkbox v-model:checked="qrContent.wifi.hidden" @change="generateQR">
                    隐藏网络
                  </a-checkbox>
                </a-space>
              </template>

              <!-- 联系人类型 -->
              <template v-else-if="qrType === 'contact'">
                <a-space direction="vertical" style="width: 100%">
                  <a-input
                    v-model:value="qrContent.contact.name"
                    placeholder="姓名"
                    @input="generateQR"
                  />
                  <a-input
                    v-model:value="qrContent.contact.phone"
                    placeholder="电话号码"
                    @input="generateQR"
                  />
                  <a-input
                    v-model:value="qrContent.contact.email"
                    placeholder="邮箱地址"
                    @input="generateQR"
                  />
                  <a-input
                    v-model:value="qrContent.contact.organization"
                    placeholder="公司/组织"
                    @input="generateQR"
                  />
                  <a-textarea
                    v-model:value="qrContent.contact.address"
                    placeholder="地址"
                    :rows="2"
                    @input="generateQR"
                  />
                </a-space>
              </template>

              <!-- 短信类型 -->
              <template v-else-if="qrType === 'sms'">
                <a-space direction="vertical" style="width: 100%">
                  <a-input
                    v-model:value="qrContent.sms.phone"
                    placeholder="手机号码"
                    @input="generateQR"
                  />
                  <a-textarea
                    v-model:value="qrContent.sms.message"
                    placeholder="短信内容"
                    :rows="3"
                    @input="generateQR"
                  />
                </a-space>
              </template>

              <!-- 邮箱类型 -->
              <template v-else-if="qrType === 'email'">
                <a-space direction="vertical" style="width: 100%">
                  <a-input
                    v-model:value="qrContent.email.to"
                    placeholder="收件人邮箱"
                    @input="generateQR"
                  />
                  <a-input
                    v-model:value="qrContent.email.subject"
                    placeholder="邮件主题"
                    @input="generateQR"
                  />
                  <a-textarea
                    v-model:value="qrContent.email.body"
                    placeholder="邮件内容"
                    :rows="3"
                    @input="generateQR"
                  />
                </a-space>
              </template>
            </div>

            <!-- 样式设置 -->
            <div class="config-section">
              <h4>样式设置</h4>
              <a-row :gutter="16">
                <a-col :span="12">
                  <div class="style-item">
                    <label>尺寸大小</label>
                    <a-slider
                      v-model:value="qrStyle.size"
                      :min="100"
                      :max="500"
                      :step="10"
                      @change="generateQR"
                    />
                    <span class="size-value">{{ qrStyle.size }}px</span>
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="style-item">
                    <label>容错级别</label>
                    <a-select
                      v-model:value="qrStyle.errorCorrectionLevel"
                      style="width: 100%"
                      @change="generateQR"
                    >
                      <a-select-option value="L">低 (7%)</a-select-option>
                      <a-select-option value="M">中 (15%)</a-select-option>
                      <a-select-option value="Q">较高 (25%)</a-select-option>
                      <a-select-option value="H">高 (30%)</a-select-option>
                    </a-select>
                  </div>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <div class="style-item">
                    <label>前景色</label>
                    <div class="color-picker">
                      <input
                        type="color"
                        v-model="qrStyle.foregroundColor"
                        @change="generateQR"
                      />
                      <span>{{ qrStyle.foregroundColor }}</span>
                    </div>
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="style-item">
                    <label>背景色</label>
                    <div class="color-picker">
                      <input
                        type="color"
                        v-model="qrStyle.backgroundColor"
                        @change="generateQR"
                      />
                      <span>{{ qrStyle.backgroundColor }}</span>
                    </div>
                  </div>
                </a-col>
              </a-row>

              <div class="style-item">
                <a-checkbox v-model:checked="qrStyle.includeMargin" @change="generateQR">
                  包含边距
                </a-checkbox>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="config-section">
              <a-space>
                <a-button type="primary" @click="generateQR" :loading="generating">
                  <QrcodeOutlined />
                  生成二维码
                </a-button>
                <a-button @click="resetConfig">
                  <ReloadOutlined />
                  重置配置
                </a-button>
              </a-space>
            </div>
          </a-card>
        </div>
      </a-col>

      <!-- 右侧预览区域 -->
      <a-col :span="12">
        <div class="preview-panel">
          <a-card title="二维码预览" :bordered="false">
            <div class="qr-preview">
              <div v-if="!qrDataURL" class="qr-placeholder">
                <QrcodeOutlined />
                <p>请配置二维码内容</p>
              </div>
              
              <div v-else class="qr-display">
                <img :src="qrDataURL" :alt="qrType" class="qr-image" />
                
                <!-- 二维码信息 -->
                <div class="qr-info">
                  <a-descriptions :column="1" size="small">
                    <a-descriptions-item label="类型">
                      {{ getTypeLabel(qrType) }}
                    </a-descriptions-item>
                    <a-descriptions-item label="尺寸">
                      {{ qrStyle.size }}x{{ qrStyle.size }}px
                    </a-descriptions-item>
                    <a-descriptions-item label="容错级别">
                      {{ getErrorLevelLabel(qrStyle.errorCorrectionLevel) }}
                    </a-descriptions-item>
                    <a-descriptions-item label="数据长度">
                      {{ qrDataLength }} 字符
                    </a-descriptions-item>
                  </a-descriptions>
                </div>

                <!-- 下载按钮 -->
                <div class="qr-actions">
                  <a-space>
                    <a-button type="primary" @click="downloadQR('png')">
                      <DownloadOutlined />
                      下载PNG
                    </a-button>
                    <a-button @click="downloadQR('svg')">
                      <FileImageOutlined />
                      下载SVG
                    </a-button>
                    <a-button @click="copyQRToClipboard">
                      <CopyOutlined />
                      复制图片
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
          </a-card>

          <!-- 使用说明 -->
          <a-card title="使用说明" :bordered="false" style="margin-top: 16px">
            <div class="usage-tips">
              <h4>{{ getTypeLabel(qrType) }}二维码说明：</h4>
              <ul>
                <template v-if="qrType === 'text'">
                  <li>支持任意文本内容，包括中文、英文、数字、符号等</li>
                  <li>建议文本长度不超过1000字符，以确保二维码清晰度</li>
                </template>
                <template v-else-if="qrType === 'url'">
                  <li>支持HTTP和HTTPS协议的网址链接</li>
                  <li>扫描后可直接在浏览器中打开链接</li>
                  <li>建议使用完整的URL地址，包含协议头</li>
                </template>
                <template v-else-if="qrType === 'wifi'">
                  <li>扫描后可自动连接到指定的WiFi网络</li>
                  <li>支持WPA/WPA2、WEP加密和开放网络</li>
                  <li>可设置隐藏网络选项</li>
                </template>
                <template v-else-if="qrType === 'contact'">
                  <li>扫描后可直接添加联系人到通讯录</li>
                  <li>支持姓名、电话、邮箱、公司、地址等信息</li>
                  <li>兼容vCard格式标准</li>
                </template>
                <template v-else-if="qrType === 'sms'">
                  <li>扫描后可直接发送短信到指定号码</li>
                  <li>可预设短信内容</li>
                  <li>支持国际号码格式</li>
                </template>
                <template v-else-if="qrType === 'email'">
                  <li>扫描后可直接发送邮件</li>
                  <li>可预设收件人、主题和邮件内容</li>
                  <li>支持多个收件人（用逗号分隔）</li>
                </template>
              </ul>
            </div>
          </a-card>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import {
  QrcodeOutlined,
  LinkOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FileImageOutlined,
  CopyOutlined
} from '@ant-design/icons-vue';
import QRCode from 'qrcode';
import { message } from 'ant-design-vue';

// 响应式数据
const qrType = ref('text');
const generating = ref(false);
const qrDataURL = ref('');
const qrDataLength = ref(0);

// 二维码内容配置
const qrContent = reactive({
  text: '',
  url: '',
  wifi: {
    ssid: '',
    password: '',
    security: 'WPA',
    hidden: false
  },
  contact: {
    name: '',
    phone: '',
    email: '',
    organization: '',
    address: ''
  },
  sms: {
    phone: '',
    message: ''
  },
  email: {
    to: '',
    subject: '',
    body: ''
  }
});

// 二维码样式配置
const qrStyle = reactive({
  size: 256,
  errorCorrectionLevel: 'M',
  foregroundColor: '#000000',
  backgroundColor: '#ffffff',
  includeMargin: true
});

// 方法
const handleTypeChange = () => {
  generateQR();
};

const generateQRContent = () => {
  switch (qrType.value) {
    case 'text':
      return qrContent.text;
    
    case 'url':
      return qrContent.url;
    
    case 'wifi':
      const { ssid, password, security, hidden } = qrContent.wifi;
      return `WIFI:T:${security};S:${ssid};P:${password};H:${hidden ? 'true' : 'false'};;`;
    
    case 'contact':
      const { name, phone, email, organization, address } = qrContent.contact;
      return `BEGIN:VCARD
VERSION:3.0
FN:${name}
TEL:${phone}
EMAIL:${email}
ORG:${organization}
ADR:${address}
END:VCARD`;
    
    case 'sms':
      return `sms:${qrContent.sms.phone}?body=${encodeURIComponent(qrContent.sms.message)}`;
    
    case 'email':
      const { to, subject, body } = qrContent.email;
      return `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    
    default:
      return '';
  }
};

const generateQR = async () => {
  const content = generateQRContent();
  
  if (!content.trim()) {
    qrDataURL.value = '';
    qrDataLength.value = 0;
    return;
  }

  generating.value = true;
  
  try {
    const options = {
      width: qrStyle.size,
      errorCorrectionLevel: qrStyle.errorCorrectionLevel,
      color: {
        dark: qrStyle.foregroundColor,
        light: qrStyle.backgroundColor
      },
      margin: qrStyle.includeMargin ? 4 : 0
    };

    qrDataURL.value = await QRCode.toDataURL(content, options);
    qrDataLength.value = content.length;
  } catch (error) {
    console.error('生成二维码失败:', error);
    message.error('生成二维码失败');
  } finally {
    generating.value = false;
  }
};

const resetConfig = () => {
  qrType.value = 'text';
  
  // 重置内容
  Object.assign(qrContent, {
    text: '',
    url: '',
    wifi: {
      ssid: '',
      password: '',
      security: 'WPA',
      hidden: false
    },
    contact: {
      name: '',
      phone: '',
      email: '',
      organization: '',
      address: ''
    },
    sms: {
      phone: '',
      message: ''
    },
    email: {
      to: '',
      subject: '',
      body: ''
    }
  });

  // 重置样式
  Object.assign(qrStyle, {
    size: 256,
    errorCorrectionLevel: 'M',
    foregroundColor: '#000000',
    backgroundColor: '#ffffff',
    includeMargin: true
  });

  qrDataURL.value = '';
  qrDataLength.value = 0;
};

const downloadQR = async (format: 'png' | 'svg') => {
  if (!qrDataURL.value) {
    message.warning('请先生成二维码');
    return;
  }

  try {
    const content = generateQRContent();
    
    if (format === 'png') {
      // 下载PNG格式
      const link = document.createElement('a');
      link.download = `qrcode_${qrType.value}_${Date.now()}.png`;
      link.href = qrDataURL.value;
      link.click();
    } else {
      // 生成SVG格式
      const svgString = await QRCode.toString(content, {
        type: 'svg',
        width: qrStyle.size,
        errorCorrectionLevel: qrStyle.errorCorrectionLevel,
        color: {
          dark: qrStyle.foregroundColor,
          light: qrStyle.backgroundColor
        },
        margin: qrStyle.includeMargin ? 4 : 0
      });

      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.download = `qrcode_${qrType.value}_${Date.now()}.svg`;
      link.href = url;
      link.click();
      URL.revokeObjectURL(url);
    }

    message.success('下载成功');
  } catch (error) {
    console.error('下载失败:', error);
    message.error('下载失败');
  }
};

const copyQRToClipboard = async () => {
  if (!qrDataURL.value) {
    message.warning('请先生成二维码');
    return;
  }

  try {
    // 将base64转换为blob
    const response = await fetch(qrDataURL.value);
    const blob = await response.blob();
    
    // 复制到剪贴板
    await navigator.clipboard.write([
      new ClipboardItem({ 'image/png': blob })
    ]);
    
    message.success('二维码已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败，请手动保存图片');
  }
};

const getTypeLabel = (type: string) => {
  const labels = {
    text: '文本',
    url: '网址链接',
    wifi: 'WiFi',
    contact: '联系人',
    sms: '短信',
    email: '邮箱'
  };
  return labels[type] || type;
};

const getErrorLevelLabel = (level: string) => {
  const labels = {
    L: '低 (7%)',
    M: '中 (15%)',
    Q: '较高 (25%)',
    H: '高 (30%)'
  };
  return labels[level] || level;
};

// 生命周期
onMounted(() => {
  // 初始化时生成一个示例二维码
  qrContent.text = '欢迎使用二维码生成器！';
  generateQR();
});
</script>

<style lang="scss" scoped>
.qr-generator-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  margin-bottom: $space-6;
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.config-panel,
.preview-panel {
  .config-section {
    margin-bottom: $space-6;
    
    h4 {
      font-size: $text-base;
      font-weight: $font-semibold;
      color: $text-primary;
      margin-bottom: $space-3;
    }
    
    .style-item {
      margin-bottom: $space-4;
      
      label {
        display: block;
        font-size: $text-sm;
        color: $text-secondary;
        margin-bottom: $space-2;
      }
      
      .size-value {
        font-size: $text-sm;
        color: $text-tertiary;
        margin-left: $space-2;
      }
      
      .color-picker {
        @include flex-center-vertical;
        gap: $space-2;
        
        input[type="color"] {
          width: 40px;
          height: 32px;
          border: 1px solid $border-medium;
          @include border-radius();
          cursor: pointer;
        }
        
        span {
          font-size: $text-sm;
          color: $text-secondary;
          font-family: $font-family-mono;
        }
      }
    }
  }
}

.qr-preview {
  text-align: center;
  
  .qr-placeholder {
    padding: $space-16 $space-8;
    color: $text-tertiary;
    
    .anticon {
      font-size: 48px;
      margin-bottom: $space-4;
    }
    
    p {
      font-size: $text-base;
      margin: 0;
    }
  }
  
  .qr-display {
    .qr-image {
      max-width: 100%;
      height: auto;
      border: 1px solid $border-light;
      @include border-radius();
      margin-bottom: $space-4;
    }
    
    .qr-info {
      margin-bottom: $space-4;
      text-align: left;
    }
    
    .qr-actions {
      text-align: center;
    }
  }
}

.usage-tips {
  h4 {
    font-size: $text-base;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $space-3;
  }
  
  ul {
    margin: 0;
    padding-left: $space-5;
    
    li {
      font-size: $text-sm;
      color: $text-secondary;
      line-height: $leading-relaxed;
      margin-bottom: $space-2;
    }
  }
}

// 响应式适配
@include responsive('md') {
  .qr-generator-container {
    .ant-col {
      margin-bottom: $space-4;
    }
  }
}
</style>
