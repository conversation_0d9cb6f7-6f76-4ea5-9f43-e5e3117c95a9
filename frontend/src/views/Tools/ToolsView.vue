<!-- 工具页面主入口 -->
<template>
  <div class="tools-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">实用工具</h1>
        <p class="page-description">提供各种实用的业务工具，提升工作效率</p>
      </div>
    </div>

    <!-- 工具分类 -->
    <div class="tools-categories">
      <a-tabs v-model:activeKey="activeCategory" @change="handleCategoryChange">
        <a-tab-pane key="generator" tab="生成工具">
          <div class="tools-grid">
            <ToolCard
              title="二维码生成器"
              description="快速生成各种类型的二维码，支持文本、链接、WiFi等"
              icon="QrcodeOutlined"
              color="blue"
              @click="openTool('qr-generator')"
            />
            
            <ToolCard
              title="条形码生成器"
              description="生成标准条形码，支持多种格式"
              icon="BarcodeOutlined"
              color="green"
              @click="openTool('barcode-generator')"
            />
            
            <ToolCard
              title="密码生成器"
              description="生成安全的随机密码，可自定义规则"
              icon="KeyOutlined"
              color="purple"
              @click="openTool('password-generator')"
            />
            
            <ToolCard
              title="UUID生成器"
              description="生成唯一标识符，支持多种版本"
              icon="NumberOutlined"
              color="orange"
              @click="openTool('uuid-generator')"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="converter" tab="转换工具">
          <div class="tools-grid">
            <ToolCard
              title="时间戳转换"
              description="时间戳与日期时间相互转换"
              icon="ClockCircleOutlined"
              color="cyan"
              @click="openTool('timestamp-converter')"
            />
            
            <ToolCard
              title="Base64编解码"
              description="Base64编码和解码工具"
              icon="CodeOutlined"
              color="red"
              @click="openTool('base64-converter')"
            />
            
            <ToolCard
              title="URL编解码"
              description="URL编码和解码工具"
              icon="LinkOutlined"
              color="blue"
              @click="openTool('url-converter')"
            />
            
            <ToolCard
              title="JSON格式化"
              description="JSON数据格式化和验证"
              icon="FileTextOutlined"
              color="green"
              @click="openTool('json-formatter')"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="calculator" tab="计算工具">
          <div class="tools-grid">
            <ToolCard
              title="利率计算器"
              description="计算贷款利率、还款金额等"
              icon="CalculatorOutlined"
              color="gold"
              @click="openTool('interest-calculator')"
            />
            
            <ToolCard
              title="日期计算器"
              description="计算日期差值、工作日等"
              icon="CalendarOutlined"
              color="purple"
              @click="openTool('date-calculator')"
            />
            
            <ToolCard
              title="单位转换器"
              description="长度、重量、面积等单位转换"
              icon="SwapOutlined"
              color="cyan"
              @click="openTool('unit-converter')"
            />
            
            <ToolCard
              title="汇率转换器"
              description="实时汇率查询和货币转换"
              icon="DollarOutlined"
              color="green"
              @click="openTool('currency-converter')"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="data" tab="数据工具">
          <div class="tools-grid">
            <ToolCard
              title="Excel导入助手"
              description="Excel文件解析和数据导入"
              icon="FileExcelOutlined"
              color="green"
              @click="openTool('excel-importer')"
            />
            
            <ToolCard
              title="数据清洗工具"
              description="数据去重、格式化、验证"
              icon="FilterOutlined"
              color="blue"
              @click="openTool('data-cleaner')"
            />
            
            <ToolCard
              title="SQL查询助手"
              description="SQL语句生成和优化建议"
              icon="DatabaseOutlined"
              color="purple"
              @click="openTool('sql-helper')"
            />
            
            <ToolCard
              title="数据对比工具"
              description="比较两个数据集的差异"
              icon="DiffOutlined"
              color="orange"
              @click="openTool('data-diff')"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="system" tab="系统工具">
          <div class="tools-grid">
            <ToolCard
              title="系统信息查看"
              description="查看浏览器和系统信息"
              icon="InfoCircleOutlined"
              color="blue"
              @click="openTool('system-info')"
            />
            
            <ToolCard
              title="网络测试工具"
              description="网络连接和速度测试"
              icon="WifiOutlined"
              color="green"
              @click="openTool('network-test')"
            />
            
            <ToolCard
              title="颜色选择器"
              description="颜色选择和格式转换"
              icon="BgColorsOutlined"
              color="red"
              @click="openTool('color-picker')"
            />
            
            <ToolCard
              title="正则表达式测试"
              description="正则表达式测试和验证"
              icon="SearchOutlined"
              color="purple"
              @click="openTool('regex-tester')"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 最近使用的工具 -->
    <div v-if="recentTools.length > 0" class="recent-tools">
      <h3>最近使用</h3>
      <div class="recent-tools-list">
        <div
          v-for="tool in recentTools"
          :key="tool.key"
          class="recent-tool-item"
          @click="openTool(tool.key)"
        >
          <div class="tool-icon">
            <component :is="getToolIcon(tool.icon)" />
          </div>
          <div class="tool-info">
            <div class="tool-name">{{ tool.title }}</div>
            <div class="tool-time">{{ formatTime(tool.lastUsed) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具使用统计 -->
    <div class="tools-stats">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic
            title="总工具数"
            :value="totalTools"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="今日使用次数"
            :value="todayUsage"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="最常用工具"
            :value="mostUsedTool"
            :value-style="{ color: '#722ed1' }"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 工具模态框 -->
    <a-modal
      v-model:visible="toolModalVisible"
      :title="currentTool?.title"
      :width="1200"
      :footer="null"
      :destroy-on-close="true"
      @cancel="closeToolModal"
    >
      <component
        v-if="currentTool"
        :is="currentTool.component"
        @close="closeToolModal"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {
  QrcodeOutlined,
  BarcodeOutlined,
  KeyOutlined,
  NumberOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  LinkOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  CalendarOutlined,
  SwapOutlined,
  DollarOutlined,
  FileExcelOutlined,
  FilterOutlined,
  DatabaseOutlined,
  DiffOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  BgColorsOutlined,
  SearchOutlined
} from '@ant-design/icons-vue';
import ToolCard from './components/ToolCard.vue';
import QRGeneratorView from './QRGeneratorView.vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

// 响应式数据
const activeCategory = ref('generator');
const toolModalVisible = ref(false);
const currentTool = ref<any>(null);
const recentTools = ref<any[]>([]);
const totalTools = ref(16);
const todayUsage = ref(0);
const mostUsedTool = ref('二维码生成器');

// 工具配置
const toolsConfig = {
  'qr-generator': {
    title: '二维码生成器',
    component: QRGeneratorView,
    icon: 'QrcodeOutlined',
    category: 'generator'
  },
  'barcode-generator': {
    title: '条形码生成器',
    component: null, // 待开发
    icon: 'BarcodeOutlined',
    category: 'generator'
  },
  'password-generator': {
    title: '密码生成器',
    component: null, // 待开发
    icon: 'KeyOutlined',
    category: 'generator'
  },
  'uuid-generator': {
    title: 'UUID生成器',
    component: null, // 待开发
    icon: 'NumberOutlined',
    category: 'generator'
  },
  'timestamp-converter': {
    title: '时间戳转换',
    component: null, // 待开发
    icon: 'ClockCircleOutlined',
    category: 'converter'
  },
  'base64-converter': {
    title: 'Base64编解码',
    component: null, // 待开发
    icon: 'CodeOutlined',
    category: 'converter'
  },
  'url-converter': {
    title: 'URL编解码',
    component: null, // 待开发
    icon: 'LinkOutlined',
    category: 'converter'
  },
  'json-formatter': {
    title: 'JSON格式化',
    component: null, // 待开发
    icon: 'FileTextOutlined',
    category: 'converter'
  },
  'interest-calculator': {
    title: '利率计算器',
    component: null, // 待开发
    icon: 'CalculatorOutlined',
    category: 'calculator'
  },
  'date-calculator': {
    title: '日期计算器',
    component: null, // 待开发
    icon: 'CalendarOutlined',
    category: 'calculator'
  },
  'unit-converter': {
    title: '单位转换器',
    component: null, // 待开发
    icon: 'SwapOutlined',
    category: 'calculator'
  },
  'currency-converter': {
    title: '汇率转换器',
    component: null, // 待开发
    icon: 'DollarOutlined',
    category: 'calculator'
  },
  'excel-importer': {
    title: 'Excel导入助手',
    component: null, // 待开发
    icon: 'FileExcelOutlined',
    category: 'data'
  },
  'data-cleaner': {
    title: '数据清洗工具',
    component: null, // 待开发
    icon: 'FilterOutlined',
    category: 'data'
  },
  'sql-helper': {
    title: 'SQL查询助手',
    component: null, // 待开发
    icon: 'DatabaseOutlined',
    category: 'data'
  },
  'data-diff': {
    title: '数据对比工具',
    component: null, // 待开发
    icon: 'DiffOutlined',
    category: 'data'
  },
  'system-info': {
    title: '系统信息查看',
    component: null, // 待开发
    icon: 'InfoCircleOutlined',
    category: 'system'
  },
  'network-test': {
    title: '网络测试工具',
    component: null, // 待开发
    icon: 'WifiOutlined',
    category: 'system'
  },
  'color-picker': {
    title: '颜色选择器',
    component: null, // 待开发
    icon: 'BgColorsOutlined',
    category: 'system'
  },
  'regex-tester': {
    title: '正则表达式测试',
    component: null, // 待开发
    icon: 'SearchOutlined',
    category: 'system'
  }
};

// 图标映射
const iconMap = {
  QrcodeOutlined,
  BarcodeOutlined,
  KeyOutlined,
  NumberOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  LinkOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  CalendarOutlined,
  SwapOutlined,
  DollarOutlined,
  FileExcelOutlined,
  FilterOutlined,
  DatabaseOutlined,
  DiffOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  BgColorsOutlined,
  SearchOutlined
};

// 方法
const handleCategoryChange = (key: string) => {
  activeCategory.value = key;
};

const openTool = (toolKey: string) => {
  const tool = toolsConfig[toolKey];
  
  if (!tool) {
    message.error('工具不存在');
    return;
  }
  
  if (!tool.component) {
    message.info('该工具正在开发中，敬请期待');
    return;
  }
  
  currentTool.value = tool;
  toolModalVisible.value = true;
  
  // 更新使用记录
  updateToolUsage(toolKey, tool);
};

const closeToolModal = () => {
  toolModalVisible.value = false;
  currentTool.value = null;
};

const updateToolUsage = (toolKey: string, tool: any) => {
  // 更新最近使用列表
  const existingIndex = recentTools.value.findIndex(item => item.key === toolKey);
  
  if (existingIndex >= 0) {
    // 更新现有记录
    recentTools.value[existingIndex].lastUsed = Date.now();
    // 移到最前面
    const item = recentTools.value.splice(existingIndex, 1)[0];
    recentTools.value.unshift(item);
  } else {
    // 添加新记录
    recentTools.value.unshift({
      key: toolKey,
      title: tool.title,
      icon: tool.icon,
      lastUsed: Date.now()
    });
  }
  
  // 只保留最近10个
  if (recentTools.value.length > 10) {
    recentTools.value = recentTools.value.slice(0, 10);
  }
  
  // 更新今日使用次数
  todayUsage.value++;
  
  // 保存到本地存储
  localStorage.setItem('recentTools', JSON.stringify(recentTools.value));
  localStorage.setItem('todayUsage', todayUsage.value.toString());
};

const getToolIcon = (iconName: string) => {
  return iconMap[iconName as keyof typeof iconMap] || QrcodeOutlined;
};

const formatTime = (timestamp: number) => {
  return dayjs(timestamp).fromNow();
};

const loadUsageData = () => {
  // 从本地存储加载数据
  try {
    const savedRecentTools = localStorage.getItem('recentTools');
    if (savedRecentTools) {
      recentTools.value = JSON.parse(savedRecentTools);
    }
    
    const savedTodayUsage = localStorage.getItem('todayUsage');
    if (savedTodayUsage) {
      todayUsage.value = parseInt(savedTodayUsage, 10);
    }
  } catch (error) {
    console.error('加载使用数据失败:', error);
  }
};

// 生命周期
onMounted(() => {
  loadUsageData();
});
</script>

<style lang="scss" scoped>
.tools-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  margin-bottom: $space-6;
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.tools-categories {
  margin-bottom: $space-8;
  
  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: $space-4;
    margin-top: $space-4;
    
    @include responsive('md') {
      grid-template-columns: 1fr;
    }
  }
}

.recent-tools {
  margin-bottom: $space-8;
  
  h3 {
    font-size: $text-xl;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $space-4;
  }
  
  .recent-tools-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: $space-3;
    
    @include responsive('md') {
      grid-template-columns: 1fr;
    }
    
    .recent-tool-item {
      @include flex-center-vertical;
      gap: $space-3;
      padding: $space-3;
      background: $bg-primary;
      @include border-radius();
      @include shadow('sm');
      @include transition(all);
      cursor: pointer;
      
      &:hover {
        @include shadow('md');
        transform: translateY(-1px);
      }
      
      .tool-icon {
        width: 32px;
        height: 32px;
        @include border-radius();
        @include flex-center;
        background: $bg-secondary;
        color: $primary-500;
        font-size: 16px;
        flex-shrink: 0;
      }
      
      .tool-info {
        flex: 1;
        
        .tool-name {
          font-weight: $font-medium;
          color: $text-primary;
          margin-bottom: $space-1;
        }
        
        .tool-time {
          font-size: $text-xs;
          color: $text-tertiary;
        }
      }
    }
  }
}

.tools-stats {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
}

// 模态框样式覆盖
:deep(.ant-modal) {
  .ant-modal-content {
    @include border-radius('lg');
  }
  
  .ant-modal-header {
    border-bottom: 1px solid $border-light;
    @include border-radius('lg lg 0 0');
  }
  
  .ant-modal-body {
    padding: $space-6;
  }
}
</style>
