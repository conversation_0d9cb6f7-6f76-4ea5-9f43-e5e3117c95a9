<!-- 工具卡片组件 -->
<template>
  <div class="tool-card" :class="[`tool-card--${color}`]" @click="handleClick">
    <div class="tool-card__icon">
      <component :is="iconComponent" />
    </div>
    
    <div class="tool-card__content">
      <h4 class="tool-card__title">{{ title }}</h4>
      <p class="tool-card__description">{{ description }}</p>
    </div>
    
    <div class="tool-card__arrow">
      <RightOutlined />
    </div>
    
    <!-- 状态标签 -->
    <div v-if="status" class="tool-card__status">
      <a-tag :color="getStatusColor(status)">{{ getStatusText(status) }}</a-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  QrcodeOutlined,
  BarcodeOutlined,
  KeyOutlined,
  NumberOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  LinkOutlined,
  FileTextOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  CalendarOutlined,
  SwapOutlined,
  DollarOutlined,
  FileExcelOutlined,
  FilterOutlined,
  DatabaseOutlined,
  DiffOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  BgColorsOutlined,
  SearchOutlined,
  RightOutlined
} from '@ant-design/icons-vue';

interface Props {
  title: string;
  description: string;
  icon: string;
  color: 'blue' | 'green' | 'red' | 'purple' | 'orange' | 'cyan' | 'gold';
  status?: 'available' | 'developing' | 'beta' | 'new';
}

const props = withDefaults(defineProps<Props>(), {
  status: 'available'
});

const emit = defineEmits<{
  click: [];
}>();

// 图标映射
const iconMap = {
  QrcodeOutlined,
  BarcodeOutlined,
  KeyOutlined,
  NumberOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  LinkOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  CalendarOutlined,
  SwapOutlined,
  DollarOutlined,
  FileExcelOutlined,
  FilterOutlined,
  DatabaseOutlined,
  DiffOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  BgColorsOutlined,
  SearchOutlined
};

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || QrcodeOutlined;
});

// 方法
const handleClick = () => {
  emit('click');
};

const getStatusColor = (status: string) => {
  const colors = {
    available: 'green',
    developing: 'orange',
    beta: 'blue',
    new: 'red'
  };
  return colors[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts = {
    available: '可用',
    developing: '开发中',
    beta: '测试版',
    new: '新功能'
  };
  return texts[status] || status;
};
</script>

<style lang="scss" scoped>
.tool-card {
  position: relative;
  display: flex;
  align-items: center;
  gap: $space-4;
  padding: $space-5;
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  @include transition(all);
  cursor: pointer;
  border: 2px solid transparent;
  
  &:hover {
    @include shadow('md');
    transform: translateY(-2px);
    border-color: currentColor;
    
    .tool-card__icon {
      background: currentColor;
      color: white;
      transform: scale(1.1);
    }
    
    .tool-card__arrow {
      color: currentColor;
      transform: translateX(4px);
    }
  }
  
  // 颜色主题
  &--blue {
    color: $primary-500;
    
    .tool-card__icon {
      background: rgba($primary-500, 0.1);
      color: $primary-500;
    }
  }
  
  &--green {
    color: $success-500;
    
    .tool-card__icon {
      background: rgba($success-500, 0.1);
      color: $success-500;
    }
  }
  
  &--red {
    color: $error-500;
    
    .tool-card__icon {
      background: rgba($error-500, 0.1);
      color: $error-500;
    }
  }
  
  &--purple {
    color: #722ed1;
    
    .tool-card__icon {
      background: rgba(#722ed1, 0.1);
      color: #722ed1;
    }
  }
  
  &--orange {
    color: $warning-500;
    
    .tool-card__icon {
      background: rgba($warning-500, 0.1);
      color: $warning-500;
    }
  }
  
  &--cyan {
    color: #13c2c2;
    
    .tool-card__icon {
      background: rgba(#13c2c2, 0.1);
      color: #13c2c2;
    }
  }
  
  &--gold {
    color: #faad14;
    
    .tool-card__icon {
      background: rgba(#faad14, 0.1);
      color: #faad14;
    }
  }
}

.tool-card__icon {
  width: 48px;
  height: 48px;
  @include border-radius('lg');
  @include flex-center;
  font-size: 24px;
  @include transition(all);
  flex-shrink: 0;
}

.tool-card__content {
  flex: 1;
  
  .tool-card__title {
    font-size: $text-lg;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0 0 $space-2 0;
    line-height: $leading-tight;
  }
  
  .tool-card__description {
    font-size: $text-sm;
    color: $text-secondary;
    margin: 0;
    line-height: $leading-normal;
  }
}

.tool-card__arrow {
  color: $text-tertiary;
  font-size: $text-sm;
  @include transition(all);
  flex-shrink: 0;
}

.tool-card__status {
  position: absolute;
  top: $space-3;
  right: $space-3;
}

// 响应式适配
@include responsive('md') {
  .tool-card {
    padding: $space-4;
    gap: $space-3;
    
    .tool-card__icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }
    
    .tool-card__title {
      font-size: $text-base;
    }
    
    .tool-card__description {
      font-size: $text-xs;
    }
  }
}

// 禁用状态
.tool-card--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  &:hover {
    transform: none;
    box-shadow: $shadow-sm;
    border-color: transparent;
    
    .tool-card__icon {
      transform: none;
      background: rgba($text-tertiary, 0.1);
      color: $text-tertiary;
    }
    
    .tool-card__arrow {
      transform: none;
      color: $text-tertiary;
    }
  }
}
</style>
