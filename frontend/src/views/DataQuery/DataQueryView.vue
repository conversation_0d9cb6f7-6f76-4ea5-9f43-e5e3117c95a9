<!-- 数据查询页面 -->
<template>
  <div class="data-query-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">数据查询</h1>
        <p class="page-description">按日期查询订单数据，支持多种筛选条件</p>
      </div>
    </div>

    <!-- 查询表单 -->
    <div class="query-form-card">
      <a-form
        ref="formRef"
        :model="queryForm"
        :rules="formRules"
        layout="inline"
        class="query-form"
        @finish="handleQuery"
      >
        <a-form-item name="date" label="查询日期">
          <a-date-picker
            v-model:value="queryForm.date"
            format="YYYY-MM-DD"
            placeholder="请选择日期"
            style="width: 200px"
            :disabled-date="disabledDate"
          />
        </a-form-item>

        <a-form-item name="business" label="业务类型">
          <a-select
            v-model:value="queryForm.business"
            placeholder="请选择业务类型"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="电商">电商</a-select-option>
            <a-select-option value="租赁">租赁</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item name="product" label="产品类型">
          <a-select
            v-model:value="queryForm.product"
            placeholder="请选择产品类型"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="分期">分期</a-select-option>
            <a-select-option value="租赁">租赁</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item name="status" label="订单状态">
          <a-select
            v-model:value="queryForm.status"
            placeholder="请选择状态"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="逾期">逾期</a-select-option>
            <a-select-option value="结清">结清</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button
              type="primary"
              html-type="submit"
              :loading="dataStore.loading.filter"
              @click="handleQuery"
            >
              <SearchOutlined />
              查询
            </a-button>
            
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
            
            <a-button
              v-if="dataStore.hasFilterData && userStore.canExportData"
              @click="handleExport"
              :loading="exportLoading"
            >
              <DownloadOutlined />
              导出
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 查询结果统计 -->
    <div v-if="dataStore.hasFilterData" class="result-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="总订单数"
            :value="resultStats.totalOrders"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="总金额"
            :value="resultStats.totalAmount"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="逾期订单"
            :value="resultStats.overdueOrders"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="逾期金额"
            :value="resultStats.overdueAmount"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-card">
      <DataTable
        :columns="tableColumns"
        :data-source="tableData"
        :loading="dataStore.loading.filter"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['20', '50', '100', '200'],
          defaultPageSize: 50
        }"
        @refresh="handleRefresh"
        @export="handleTableExport"
      >
        <!-- 自定义单元格渲染 -->
        <template #cell="{ column, record, value }">
          <!-- 金额列 -->
          <template v-if="column.dataIndex === '金额' || column.dataIndex === '当前待收'">
            <AmountCell :amount="value" />
          </template>
          
          <!-- 日期列 -->
          <template v-else-if="column.dataIndex === '订单日期' || column.dataIndex === '账单日期'">
            <DateCell :date="value" />
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.dataIndex === '状态'">
            <StatusTag :status="value" />
          </template>
          
          <!-- 逾期天数列 -->
          <template v-else-if="column.dataIndex === '逾期天数'">
            <a-tag v-if="value > 0" color="red">{{ value }}天</a-tag>
            <span v-else>-</span>
          </template>
          
          <!-- 客户信息列 -->
          <template v-else-if="column.dataIndex === '客户姓名'">
            <a-button
              type="link"
              size="small"
              @click="viewCustomerDetail(record)"
            >
              {{ value }}
            </a-button>
          </template>
          
          <!-- 默认渲染 -->
          <template v-else>
            {{ value }}
          </template>
        </template>

        <!-- 展开行内容 -->
        <template #expanded-row="{ record }">
          <div class="expanded-detail">
            <a-descriptions :column="3" size="small" bordered>
              <a-descriptions-item label="订单编号">
                {{ record.订单编号 }}
              </a-descriptions-item>
              <a-descriptions-item label="客户手机">
                {{ record.客户手机 }}
              </a-descriptions-item>
              <a-descriptions-item label="成本">
                <AmountCell :amount="record.成本" />
              </a-descriptions-item>
              <a-descriptions-item label="总待收">
                <AmountCell :amount="record.总待收" />
              </a-descriptions-item>
              <a-descriptions-item label="业务类型">
                {{ record.业务 }}
              </a-descriptions-item>
              <a-descriptions-item label="客服">
                {{ record.客服 }}
              </a-descriptions-item>
              
              <!-- 动态期数信息 -->
              <template v-for="(value, key) in getInstallmentInfo(record)" :key="key">
                <a-descriptions-item :label="key">
                  <StatusTag :status="value" />
                </a-descriptions-item>
              </template>
            </a-descriptions>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- 客户详情模态框 -->
    <CustomerDetailModal
      v-model:visible="customerModalVisible"
      :customer-name="selectedCustomer"
      @close="customerModalVisible = false"
    />

    <!-- 导出确认模态框 -->
    <ExportModal
      v-model:visible="exportModalVisible"
      :data="tableData"
      :filename="`数据查询_${queryForm.date}_${Date.now()}`"
      @export="handleConfirmExport"
    />
  </div>
</template>

<script setup lang="ts">
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { useDataStore } from '@/stores/data';
import { useUserStore } from '@/stores/user';
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import StatusTag from '@/components/DataTable/StatusTag.vue';
import CustomerDetailModal from './components/CustomerDetailModal.vue';
import ExportModal from '@/components/common/ExportModal.vue';
import { message } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';

const dataStore = useDataStore();
const userStore = useUserStore();

// 表单引用
const formRef = ref();

// 响应式数据
const queryForm = reactive({
  date: dayjs().format('YYYY-MM-DD'),
  business: '',
  product: '',
  status: ''
});

const exportLoading = ref(false);
const customerModalVisible = ref(false);
const exportModalVisible = ref(false);
const selectedCustomer = ref('');

// 表单验证规则
const formRules = {
  date: [
    { required: true, message: '请选择查询日期', trigger: 'change' }
  ]
};

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '订单编号',
    dataIndex: '订单编号',
    key: '订单编号',
    width: 150,
    fixed: 'left'
  },
  {
    title: '客户姓名',
    dataIndex: '客户姓名',
    key: '客户姓名',
    width: 120
  },
  {
    title: '客户手机',
    dataIndex: '客户手机',
    key: '客户手机',
    width: 130
  },
  {
    title: '订单日期',
    dataIndex: '订单日期',
    key: '订单日期',
    width: 120,
    sorter: true
  },
  {
    title: '金额',
    dataIndex: '金额',
    key: '金额',
    width: 120,
    sorter: true
  },
  {
    title: '当前待收',
    dataIndex: '当前待收',
    key: '当前待收',
    width: 120,
    sorter: true
  },
  {
    title: '业务',
    dataIndex: '业务',
    key: '业务',
    width: 100,
    filters: [
      { text: '电商', value: '电商' },
      { text: '租赁', value: '租赁' },
      { text: '其他', value: '其他' }
    ]
  },
  {
    title: '产品',
    dataIndex: '产品',
    key: '产品',
    width: 120
  },
  {
    title: '客服',
    dataIndex: '客服',
    key: '客服',
    width: 100
  },
  {
    title: '逾期天数',
    dataIndex: '逾期天数',
    key: '逾期天数',
    width: 100,
    sorter: true
  },
  {
    title: '状态',
    dataIndex: '状态',
    key: '状态',
    width: 100,
    filters: [
      { text: '正常', value: '正常' },
      { text: '逾期', value: '逾期' },
      { text: '结清', value: '结清' }
    ]
  }
]);

// 计算属性
const tableData = computed(() => {
  if (!dataStore.filterData?.results) return [];
  
  return dataStore.filterData.results.map(record => ({
    ...record,
    key: record.订单编号
  }));
});

const resultStats = computed(() => {
  const data = tableData.value;
  
  return {
    totalOrders: data.length,
    totalAmount: data.reduce((sum, item) => sum + (item.金额 || 0), 0),
    overdueOrders: data.filter(item => (item.逾期天数 || 0) > 0).length,
    overdueAmount: data
      .filter(item => (item.逾期天数 || 0) > 0)
      .reduce((sum, item) => sum + (item.当前待收 || 0), 0)
  };
});

// 方法
const disabledDate = (current: Dayjs) => {
  // 禁用未来日期
  return current && current > dayjs().endOf('day');
};

const handleQuery = async () => {
  try {
    await formRef.value?.validate();
    
    const params = {
      date: queryForm.date
    };
    
    await dataStore.fetchFilterData(params);
    
    if (dataStore.hasFilterData) {
      message.success(`查询成功，共找到 ${tableData.value.length} 条记录`);
    } else {
      message.info('未找到符合条件的数据');
    }
  } catch (error) {
    console.error('查询失败:', error);
  }
};

const handleReset = () => {
  queryForm.date = dayjs().format('YYYY-MM-DD');
  queryForm.business = '';
  queryForm.product = '';
  queryForm.status = '';
  
  // 清除查询结果
  dataStore.clearCache('filter');
};

const handleRefresh = () => {
  if (queryForm.date) {
    handleQuery();
  }
};

const handleExport = () => {
  exportModalVisible.value = true;
};

const handleTableExport = (format: 'excel' | 'csv') => {
  handleConfirmExport(format);
};

const handleConfirmExport = async (format: 'excel' | 'csv') => {
  if (!userStore.canExportData) {
    message.warning('您没有权限导出数据');
    return;
  }
  
  exportLoading.value = true;
  try {
    // 这里调用导出API
    const params = {
      format,
      data_type: 'filter',
      search_params: queryForm,
      filename: `数据查询_${queryForm.date}_${Date.now()}.${format === 'excel' ? 'xlsx' : 'csv'}`
    };
    
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    message.success('导出成功');
    exportModalVisible.value = false;
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

const viewCustomerDetail = (record: any) => {
  selectedCustomer.value = record.客户姓名;
  customerModalVisible.value = true;
};

const getInstallmentInfo = (record: any) => {
  const installmentInfo: Record<string, string> = {};
  
  Object.keys(record).forEach(key => {
    if (key.startsWith('期数') && key !== '期数') {
      installmentInfo[key] = record[key];
    }
  });
  
  return installmentInfo;
};

// 生命周期
onMounted(() => {
  // 如果有默认日期，自动查询
  if (queryForm.date) {
    handleQuery();
  }
});
</script>

<style lang="scss" scoped>
.data-query-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  margin-bottom: $space-6;
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.query-form-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  margin-bottom: $space-6;
  
  .query-form {
    :deep(.ant-form-item) {
      margin-bottom: $space-4;
      
      @include responsive('md') {
        margin-right: 0;
        margin-bottom: $space-3;
      }
    }
    
    @include responsive('md') {
      .ant-form-item {
        display: block;
        width: 100%;
      }
    }
  }
}

.result-stats {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  margin-bottom: $space-6;
}

.data-table-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
}

.expanded-detail {
  padding: $space-4;
  background: $bg-secondary;
  @include border-radius();
  margin: $space-2;
}
</style>
