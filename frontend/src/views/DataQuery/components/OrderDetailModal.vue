<!-- 订单详情模态框 -->
<template>
  <a-modal
    v-model:visible="modalVisible"
    title="订单详情"
    :width="900"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="order-detail-modal">
      <a-spin :spinning="loading">
        <div v-if="orderData" class="order-content">
          <!-- 订单基本信息 -->
          <div class="order-header">
            <div class="order-info">
              <h3>{{ orderData.orderNumber }}</h3>
              <p>{{ orderData.customerName }} · {{ orderData.phone }}</p>
            </div>
            <div class="order-status">
              <StatusTag :status="orderData.status" />
            </div>
          </div>

          <!-- 订单统计 -->
          <div class="order-stats">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="订单金额"
                  :value="orderData.orderAmount"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="当前待收"
                  :value="orderData.currentAmount"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#faad14' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="已还金额"
                  :value="orderData.paidAmount"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="逾期天数"
                  :value="orderData.overdueDays"
                  suffix="天"
                  :value-style="{ color: orderData.overdueDays > 0 ? '#ff4d4f' : '#52c41a' }"
                />
              </a-col>
            </a-row>
          </div>

          <!-- 详细信息 -->
          <a-tabs>
            <a-tab-pane key="basic" tab="基本信息">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="订单编号">
                  {{ orderData.orderNumber }}
                </a-descriptions-item>
                <a-descriptions-item label="客户姓名">
                  {{ orderData.customerName }}
                </a-descriptions-item>
                <a-descriptions-item label="手机号码">
                  {{ orderData.phone }}
                </a-descriptions-item>
                <a-descriptions-item label="订单日期">
                  <DateCell :date="orderData.orderDate" />
                </a-descriptions-item>
                <a-descriptions-item label="业务类型">
                  {{ orderData.businessType }}
                </a-descriptions-item>
                <a-descriptions-item label="产品类型">
                  {{ orderData.productType }}
                </a-descriptions-item>
                <a-descriptions-item label="客服">
                  {{ orderData.service }}
                </a-descriptions-item>
                <a-descriptions-item label="订单状态">
                  <StatusTag :status="orderData.status" />
                </a-descriptions-item>
                <a-descriptions-item label="订单金额">
                  <AmountCell :amount="orderData.orderAmount" />
                </a-descriptions-item>
                <a-descriptions-item label="成本">
                  <AmountCell :amount="orderData.cost" />
                </a-descriptions-item>
                <a-descriptions-item label="总待收">
                  <AmountCell :amount="orderData.totalAmount" />
                </a-descriptions-item>
                <a-descriptions-item label="当前待收">
                  <AmountCell :amount="orderData.currentAmount" />
                </a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>

            <a-tab-pane key="installments" tab="期数信息">
              <DataTable
                :columns="installmentColumns"
                :data-source="orderData.installments || []"
                :pagination="false"
                size="small"
              >
                <template #cell="{ column, record, value }">
                  <template v-if="column.dataIndex === 'amount'">
                    <AmountCell :amount="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'dueDate'">
                    <DateCell :date="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'paidDate'">
                    <DateCell :date="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'status'">
                    <StatusTag :status="value" />
                  </template>
                  <template v-else>
                    {{ value }}
                  </template>
                </template>
              </DataTable>
            </a-tab-pane>

            <a-tab-pane key="payments" tab="还款记录">
              <DataTable
                :columns="paymentColumns"
                :data-source="orderData.payments || []"
                :pagination="false"
                size="small"
              >
                <template #cell="{ column, record, value }">
                  <template v-if="column.dataIndex === 'amount'">
                    <AmountCell :amount="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'paymentDate'">
                    <DateCell :date="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'status'">
                    <StatusTag :status="value" />
                  </template>
                  <template v-else>
                    {{ value }}
                  </template>
                </template>
              </DataTable>
            </a-tab-pane>

            <a-tab-pane key="logs" tab="操作日志">
              <div class="operation-logs">
                <a-timeline>
                  <a-timeline-item
                    v-for="log in orderData.operationLogs || []"
                    :key="log.id"
                    :color="getLogColor(log.type)"
                  >
                    <div class="log-header">
                      <span class="log-action">{{ log.action }}</span>
                      <span class="log-time">
                        <DateCell :date="log.time" />
                      </span>
                    </div>
                    <div class="log-content">{{ log.content }}</div>
                    <div class="log-operator">操作人：{{ log.operator }}</div>
                  </a-timeline-item>
                </a-timeline>
                
                <a-empty v-if="!orderData.operationLogs?.length" description="暂无操作记录" />
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>

        <a-empty v-else description="订单信息不存在" />
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import StatusTag from '@/components/DataTable/StatusTag.vue';
import { message } from 'ant-design-vue';

interface Props {
  visible: boolean;
  orderId: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  close: [];
}>();

// 响应式数据
const loading = ref(false);
const orderData = ref<any>(null);

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 期数列配置
const installmentColumns = [
  {
    title: '期数',
    dataIndex: 'period',
    key: 'period',
    width: 80
  },
  {
    title: '应还金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120
  },
  {
    title: '到期日期',
    dataIndex: 'dueDate',
    key: 'dueDate',
    width: 120
  },
  {
    title: '实还日期',
    dataIndex: 'paidDate',
    key: 'paidDate',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  }
];

// 还款记录列配置
const paymentColumns = [
  {
    title: '还款日期',
    dataIndex: 'paymentDate',
    key: 'paymentDate',
    width: 120
  },
  {
    title: '还款金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120
  },
  {
    title: '还款方式',
    dataIndex: 'method',
    key: 'method',
    width: 100
  },
  {
    title: '交易流水号',
    dataIndex: 'transactionId',
    key: 'transactionId',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  }
];

// 方法
const loadOrderData = async (orderId: string) => {
  if (!orderId) return;

  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    orderData.value = {
      orderNumber: orderId,
      customerName: '张三',
      phone: '138****8888',
      orderDate: '2024-01-15',
      businessType: '电商',
      productType: '分期产品',
      service: '客服A',
      status: '正常',
      orderAmount: 50000,
      cost: 45000,
      totalAmount: 55000,
      currentAmount: 35000,
      paidAmount: 20000,
      overdueDays: 0,
      installments: [
        {
          period: 1,
          amount: 18333.33,
          dueDate: '2024-02-15',
          paidDate: '2024-02-14',
          status: '已还'
        },
        {
          period: 2,
          amount: 18333.33,
          dueDate: '2024-03-15',
          paidDate: null,
          status: '未还'
        },
        {
          period: 3,
          amount: 18333.34,
          dueDate: '2024-04-15',
          paidDate: null,
          status: '未还'
        }
      ],
      payments: [
        {
          paymentDate: '2024-02-14',
          amount: 18333.33,
          method: '银行转账',
          transactionId: 'TXN20240214001',
          status: '成功'
        }
      ],
      operationLogs: [
        {
          id: 1,
          type: 'create',
          action: '创建订单',
          content: '订单创建成功，等待审核',
          time: '2024-01-15 10:00:00',
          operator: '系统'
        },
        {
          id: 2,
          type: 'approve',
          action: '订单审核',
          content: '订单审核通过，开始放款',
          time: '2024-01-15 14:30:00',
          operator: '审核员A'
        },
        {
          id: 3,
          type: 'payment',
          action: '客户还款',
          content: '客户还款18333.33元，第1期已结清',
          time: '2024-02-14 09:15:00',
          operator: '系统'
        }
      ]
    };
  } catch (error) {
    console.error('加载订单数据失败:', error);
    message.error('加载订单数据失败');
  } finally {
    loading.value = false;
  }
};

const getLogColor = (type: string) => {
  const colors = {
    create: 'blue',
    approve: 'green',
    payment: 'cyan',
    overdue: 'red',
    contact: 'orange'
  };
  return colors[type] || 'gray';
};

const handleClose = () => {
  emit('close');
};

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.orderId) {
    loadOrderData(props.orderId);
  }
});

watch(() => props.orderId, (orderId) => {
  if (props.visible && orderId) {
    loadOrderData(orderId);
  }
});
</script>

<style lang="scss" scoped>
.order-detail-modal {
  .order-content {
    .order-header {
      @include flex-between;
      margin-bottom: $space-6;
      padding-bottom: $space-4;
      border-bottom: 1px solid $border-light;
      
      .order-info {
        h3 {
          font-size: $text-xl;
          font-weight: $font-semibold;
          color: $text-primary;
          margin: 0 0 $space-1 0;
        }
        
        p {
          color: $text-secondary;
          margin: 0;
        }
      }
      
      .order-status {
        flex-shrink: 0;
      }
    }
    
    .order-stats {
      margin-bottom: $space-6;
    }
  }
  
  .operation-logs {
    .log-header {
      @include flex-between;
      margin-bottom: $space-1;
      
      .log-action {
        font-weight: $font-medium;
        color: $text-primary;
      }
      
      .log-time {
        font-size: $text-sm;
        color: $text-tertiary;
      }
    }
    
    .log-content {
      color: $text-secondary;
      line-height: $leading-relaxed;
      margin-bottom: $space-1;
    }
    
    .log-operator {
      font-size: $text-sm;
      color: $text-tertiary;
    }
  }
}
</style>
