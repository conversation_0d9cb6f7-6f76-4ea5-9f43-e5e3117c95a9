<!-- API配置和测试页面 -->
<template>
  <div class="api-config-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">API配置与测试</h1>
        <p class="page-description">配置后端API连接并测试接口可用性</p>
      </div>
    </div>

    <a-row :gutter="24">
      <!-- 左侧配置区域 -->
      <a-col :span="12">
        <a-card title="API配置" :bordered="false">
          <a-form
            :model="apiSettings"
            layout="vertical"
            @finish="handleSaveConfig"
          >
            <!-- API基础地址 -->
            <a-form-item label="API基础地址" required>
              <a-input
                v-model:value="apiSettings.baseURL"
                placeholder="http://localhost:5000"
                addon-before="🌐"
              />
              <div class="form-help">
                <p>常见后端框架默认地址：</p>
                <ul>
                  <li>Flask/FastAPI: http://localhost:5000</li>
                  <li>Node.js/Express: http://localhost:3000</li>
                  <li>Spring Boot: http://localhost:8080</li>
                  <li>Django/Laravel: http://localhost:8000</li>
                </ul>
              </div>
            </a-form-item>

            <!-- 超时设置 -->
            <a-form-item label="请求超时(毫秒)">
              <a-input-number
                v-model:value="apiSettings.timeout"
                :min="1000"
                :max="60000"
                :step="1000"
                style="width: 100%"
              />
            </a-form-item>

            <!-- 响应格式 -->
            <a-form-item label="响应格式">
              <a-select v-model:value="apiSettings.responseFormat">
                <a-select-option value="standard">
                  标准格式 { code, message, data }
                </a-select-option>
                <a-select-option value="laravel">
                  Laravel格式 { success, data, message }
                </a-select-option>
                <a-select-option value="simple">
                  简单格式 (直接返回数据)
                </a-select-option>
              </a-select>
            </a-form-item>

            <!-- 认证设置 -->
            <a-form-item label="认证Token">
              <a-input-password
                v-model:value="apiSettings.authToken"
                placeholder="Bearer token (可选)"
              />
            </a-form-item>

            <!-- 调试选项 -->
            <a-form-item label="调试选项">
              <a-space direction="vertical">
                <a-checkbox v-model:checked="apiSettings.enableConsoleLog">
                  启用控制台日志
                </a-checkbox>
                <a-checkbox v-model:checked="apiSettings.enableMock">
                  API失败时使用Mock数据
                </a-checkbox>
              </a-space>
            </a-form-item>

            <!-- 操作按钮 -->
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit" :loading="saving">
                  保存配置
                </a-button>
                <a-button @click="handleResetConfig">
                  重置默认
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>

      <!-- 右侧测试区域 -->
      <a-col :span="12">
        <a-card title="API测试" :bordered="false">
          <!-- 连接状态 -->
          <div class="connection-status">
            <h4>连接状态</h4>
            <div class="status-indicator">
              <a-badge
                :status="getStatusBadge(connectionStatus.status)"
                :text="getStatusText(connectionStatus.status)"
              />
              <span v-if="connectionStatus.responseTime" class="response-time">
                ({{ connectionStatus.responseTime }}ms)
              </span>
            </div>
            <p class="status-details">{{ connectionStatus.details }}</p>
          </div>

          <!-- 测试按钮 -->
          <div class="test-actions">
            <a-space>
              <a-button
                type="primary"
                @click="handleTestConnection"
                :loading="testing"
              >
                <WifiOutlined />
                测试连接
              </a-button>
              
              <a-button
                @click="handleRunAllTests"
                :loading="runningTests"
              >
                <ExperimentOutlined />
                运行全部测试
              </a-button>
            </a-space>
          </div>

          <!-- 测试结果 -->
          <div v-if="testResults" class="test-results">
            <h4>测试结果</h4>
            
            <!-- 汇总信息 -->
            <div class="test-summary">
              <a-statistic-countdown
                title="总测试数"
                :value="testResults.totalTests"
                format="D"
              />
              <a-statistic
                title="通过"
                :value="testResults.passedTests"
                :value-style="{ color: '#3f8600' }"
              />
              <a-statistic
                title="失败"
                :value="testResults.failedTests"
                :value-style="{ color: '#cf1322' }"
              />
              <a-statistic
                title="成功率"
                :value="(testResults.passedTests / testResults.totalTests * 100).toFixed(1)"
                suffix="%"
                :value-style="{ 
                  color: testResults.passedTests === testResults.totalTests ? '#3f8600' : '#cf1322' 
                }"
              />
            </div>

            <!-- 详细结果 -->
            <div class="test-details">
              <a-list
                :data-source="testResults.results"
                size="small"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>
                        <span :class="item.success ? 'test-success' : 'test-failed'">
                          {{ item.success ? '✅' : '❌' }} {{ item.endpoint }}
                        </span>
                      </template>
                      <template #description>
                        <div v-if="item.success">
                          响应时间: {{ item.responseTime }}ms
                        </div>
                        <div v-else class="error-message">
                          错误: {{ item.error }}
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- API端点文档 -->
    <a-card title="API端点文档" style="margin-top: 24px">
      <a-collapse>
        <a-collapse-panel key="endpoints" header="查看所有API端点">
          <div class="api-endpoints">
            <div v-for="(group, groupName) in apiEndpoints" :key="groupName" class="endpoint-group">
              <h4>{{ groupName }}</h4>
              <a-table
                :columns="endpointColumns"
                :data-source="group"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'method'">
                    <a-tag :color="getMethodColor(record.method)">
                      {{ record.method }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.dataIndex === 'url'">
                    <code>{{ record.url }}</code>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { WifiOutlined, ExperimentOutlined } from '@ant-design/icons-vue';
import { apiTester, type ApiTestSuite } from '@/utils/apiTester';
import { apiConfig } from '@/api/config';
import { message } from 'ant-design-vue';

// 响应式数据
const saving = ref(false);
const testing = ref(false);
const runningTests = ref(false);

const apiSettings = reactive({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout,
  responseFormat: apiConfig.responseFormat,
  authToken: '',
  enableConsoleLog: apiConfig.enableConsoleLog,
  enableMock: apiConfig.enableMock,
});

const connectionStatus = reactive({
  status: 'unknown' as 'healthy' | 'degraded' | 'unhealthy' | 'unknown',
  details: '尚未测试',
  responseTime: 0,
});

const testResults = ref<ApiTestSuite | null>(null);

// API端点文档
const apiEndpoints = {
  '数据查询': [
    { method: 'GET', url: '/api/filter_data', description: '根据日期筛选数据' },
    { method: 'GET', url: '/api/filter_orders_by_customer_name', description: '按客户名查询订单' },
    { method: 'GET', url: '/api/filter_overdue_orders', description: '查询逾期订单' },
  ],
  '数据汇总': [
    { method: 'GET', url: '/api/customer_summary', description: '客户汇总数据' },
    { method: 'GET', url: '/api/summary_data', description: '数据汇总' },
    { method: 'GET', url: '/api/order_summary', description: '订单汇总' },
    { method: 'GET', url: '/api/overdue_summary', description: '逾期汇总' },
  ],
  '数据导出': [
    { method: 'POST', url: '/api/export', description: '导出数据' },
    { method: 'POST', url: '/api/export/excel', description: '导出Excel' },
    { method: 'POST', url: '/api/export/csv', description: '导出CSV' },
  ],
};

const endpointColumns = [
  { title: '方法', dataIndex: 'method', width: 80 },
  { title: 'URL', dataIndex: 'url', width: 300 },
  { title: '描述', dataIndex: 'description' },
];

// 方法
const handleSaveConfig = async () => {
  saving.value = true;
  try {
    // 这里可以保存到localStorage或发送到后端
    localStorage.setItem('apiConfig', JSON.stringify(apiSettings));
    message.success('配置保存成功');
    
    // 重新加载页面以应用新配置
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  } catch (error) {
    message.error('保存配置失败');
  } finally {
    saving.value = false;
  }
};

const handleResetConfig = () => {
  Object.assign(apiSettings, {
    baseURL: '/api',
    timeout: 30000,
    responseFormat: 'standard',
    authToken: '',
    enableConsoleLog: true,
    enableMock: false,
  });
  message.info('已重置为默认配置');
};

const handleTestConnection = async () => {
  testing.value = true;
  try {
    const health = await apiTester.getApiHealth();
    Object.assign(connectionStatus, health);
  } catch (error: any) {
    Object.assign(connectionStatus, {
      status: 'unhealthy',
      details: error.message || '连接测试失败',
      responseTime: 0,
    });
  } finally {
    testing.value = false;
  }
};

const handleRunAllTests = async () => {
  runningTests.value = true;
  try {
    testResults.value = await apiTester.runTestSuite();
  } catch (error: any) {
    message.error('测试运行失败: ' + error.message);
  } finally {
    runningTests.value = false;
  }
};

const getStatusBadge = (status: string) => {
  const badges = {
    healthy: 'success',
    degraded: 'warning',
    unhealthy: 'error',
    unknown: 'default',
  };
  return badges[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts = {
    healthy: '连接正常',
    degraded: '连接缓慢',
    unhealthy: '连接失败',
    unknown: '未知状态',
  };
  return texts[status] || '未知状态';
};

const getMethodColor = (method: string) => {
  const colors = {
    GET: 'blue',
    POST: 'green',
    PUT: 'orange',
    DELETE: 'red',
  };
  return colors[method] || 'default';
};

// 生命周期
onMounted(() => {
  // 加载保存的配置
  const savedConfig = localStorage.getItem('apiConfig');
  if (savedConfig) {
    try {
      Object.assign(apiSettings, JSON.parse(savedConfig));
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  }
  
  // 自动测试连接
  handleTestConnection();
});
</script>

<style lang="scss" scoped>
.api-config-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  margin-bottom: $space-6;
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.form-help {
  margin-top: $space-2;
  padding: $space-3;
  background: $bg-secondary;
  @include border-radius();
  font-size: $text-sm;
  
  p {
    margin: 0 0 $space-2 0;
    color: $text-secondary;
  }
  
  ul {
    margin: 0;
    padding-left: $space-4;
    
    li {
      color: $text-tertiary;
      margin-bottom: $space-1;
      font-family: $font-family-mono;
    }
  }
}

.connection-status {
  margin-bottom: $space-6;
  
  h4 {
    font-size: $text-base;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $space-3;
  }
  
  .status-indicator {
    @include flex-center-vertical;
    gap: $space-2;
    margin-bottom: $space-2;
    
    .response-time {
      font-size: $text-sm;
      color: $text-tertiary;
    }
  }
  
  .status-details {
    color: $text-secondary;
    font-size: $text-sm;
    margin: 0;
  }
}

.test-actions {
  margin-bottom: $space-6;
}

.test-results {
  .test-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $space-4;
    margin-bottom: $space-4;
    
    @include responsive('md') {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .test-details {
    .test-success {
      color: $success-600;
    }
    
    .test-failed {
      color: $error-600;
    }
    
    .error-message {
      color: $error-600;
      font-size: $text-sm;
    }
  }
}

.api-endpoints {
  .endpoint-group {
    margin-bottom: $space-6;
    
    h4 {
      font-size: $text-lg;
      font-weight: $font-semibold;
      color: $text-primary;
      margin-bottom: $space-3;
    }
  }
}
</style>
