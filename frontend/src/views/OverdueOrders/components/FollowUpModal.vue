<!-- 跟进记录模态框 -->
<template>
  <a-modal
    v-model:visible="modalVisible"
    title="添加跟进记录"
    :width="600"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirm-loading="submitting"
  >
    <div class="follow-up-modal">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <!-- 订单信息 -->
        <div v-if="order" class="order-info">
          <h4>订单信息</h4>
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="订单编号">
              {{ order.订单编号 }}
            </a-descriptions-item>
            <a-descriptions-item label="客户姓名">
              {{ order.客户姓名 }}
            </a-descriptions-item>
            <a-descriptions-item label="客户手机">
              {{ order.客户手机 }}
            </a-descriptions-item>
            <a-descriptions-item label="逾期天数">
              <a-tag color="red">{{ order.逾期天数 }}天</a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 跟进类型 -->
        <a-form-item name="type" label="跟进类型" required>
          <a-select v-model:value="formData.type" placeholder="请选择跟进类型">
            <a-select-option value="phone">电话联系</a-select-option>
            <a-select-option value="sms">短信通知</a-select-option>
            <a-select-option value="visit">上门拜访</a-select-option>
            <a-select-option value="email">邮件联系</a-select-option>
            <a-select-option value="other">其他方式</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 联系结果 -->
        <a-form-item name="result" label="联系结果" required>
          <a-select v-model:value="formData.result" placeholder="请选择联系结果">
            <a-select-option value="connected">联系成功</a-select-option>
            <a-select-option value="no_answer">无人接听</a-select-option>
            <a-select-option value="busy">占线</a-select-option>
            <a-select-option value="wrong_number">号码错误</a-select-option>
            <a-select-option value="refused">拒绝接听</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 客户态度 -->
        <a-form-item name="attitude" label="客户态度">
          <a-radio-group v-model:value="formData.attitude">
            <a-radio value="cooperative">配合</a-radio>
            <a-radio value="neutral">中性</a-radio>
            <a-radio value="resistant">抵触</a-radio>
            <a-radio value="aggressive">恶劣</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 还款承诺 -->
        <a-form-item name="promise" label="还款承诺">
          <a-radio-group v-model:value="formData.promise">
            <a-radio value="immediate">立即还款</a-radio>
            <a-radio value="within_week">一周内还款</a-radio>
            <a-radio value="within_month">一月内还款</a-radio>
            <a-radio value="negotiate">协商还款</a-radio>
            <a-radio value="refuse">拒绝还款</a-radio>
            <a-radio value="none">无承诺</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 承诺还款日期 -->
        <a-form-item 
          v-if="formData.promise && formData.promise !== 'refuse' && formData.promise !== 'none'"
          name="promiseDate" 
          label="承诺还款日期"
        >
          <a-date-picker
            v-model:value="formData.promiseDate"
            style="width: 100%"
            placeholder="请选择承诺还款日期"
          />
        </a-form-item>

        <!-- 跟进内容 -->
        <a-form-item name="content" label="跟进内容" required>
          <a-textarea
            v-model:value="formData.content"
            placeholder="请详细描述跟进情况，包括客户反馈、问题等"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <!-- 下次跟进时间 -->
        <a-form-item name="nextFollowTime" label="下次跟进时间">
          <a-date-picker
            v-model:value="formData.nextFollowTime"
            show-time
            style="width: 100%"
            placeholder="请选择下次跟进时间"
          />
        </a-form-item>

        <!-- 备注 -->
        <a-form-item name="remark" label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="其他需要说明的情况"
            :rows="2"
            :maxlength="200"
            show-count
          />
        </a-form-item>

        <!-- 附件上传 -->
        <a-form-item name="attachments" label="附件">
          <a-upload
            v-model:file-list="formData.attachments"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            multiple
          >
            <a-button>
              <UploadOutlined />
              上传附件
            </a-button>
          </a-upload>
          <div class="upload-tip">
            支持上传图片、文档等文件，单个文件不超过10MB
          </div>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { UploadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

interface Props {
  visible: boolean;
  order: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  submit: [data: any];
}>();

// 响应式数据
const formRef = ref();
const submitting = ref(false);

const formData = reactive({
  type: '',
  result: '',
  attitude: '',
  promise: '',
  promiseDate: null as Dayjs | null,
  content: '',
  nextFollowTime: null as Dayjs | null,
  remark: '',
  attachments: []
});

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单验证规则
const formRules = {
  type: [
    { required: true, message: '请选择跟进类型', trigger: 'change' }
  ],
  result: [
    { required: true, message: '请选择联系结果', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入跟进内容', trigger: 'blur' },
    { min: 10, message: '跟进内容至少10个字符', trigger: 'blur' }
  ]
};

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    
    submitting.value = true;
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const submitData = {
      ...formData,
      orderId: props.order?.订单编号,
      customerName: props.order?.客户姓名,
      followTime: new Date().toISOString()
    };
    
    emit('submit', submitData);
    message.success('跟进记录添加成功');
    
    // 重置表单
    resetForm();
    
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

const resetForm = () => {
  Object.assign(formData, {
    type: '',
    result: '',
    attitude: '',
    promise: '',
    promiseDate: null,
    content: '',
    nextFollowTime: null,
    remark: '',
    attachments: []
  });
  
  formRef.value?.resetFields();
};

const beforeUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword'].includes(file.type);
  if (!isValidType) {
    message.error('只能上传图片、PDF或Word文档');
    return false;
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB');
    return false;
  }
  
  return false; // 阻止自动上传
};

const handleRemove = (file: any) => {
  const index = formData.attachments.indexOf(file);
  if (index > -1) {
    formData.attachments.splice(index, 1);
  }
};

// 监听器
watch(() => props.visible, (visible) => {
  if (!visible) {
    resetForm();
  }
});
</script>

<style lang="scss" scoped>
.follow-up-modal {
  .order-info {
    background: $bg-secondary;
    padding: $space-4;
    @include border-radius();
    margin-bottom: $space-4;
    
    h4 {
      font-size: $text-base;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0 0 $space-3 0;
    }
  }
  
  .upload-tip {
    font-size: $text-xs;
    color: $text-tertiary;
    margin-top: $space-2;
  }
}

:deep(.ant-form-item) {
  margin-bottom: $space-4;
}

:deep(.ant-radio-group) {
  .ant-radio-wrapper {
    margin-bottom: $space-2;
  }
}
</style>
