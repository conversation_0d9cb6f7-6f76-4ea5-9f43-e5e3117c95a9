<!-- 跟进历史组件 -->
<template>
  <div class="follow-up-history">
    <div class="history-header">
      <h4>跟进历史</h4>
      <a-space>
        <a-select
          v-model:value="filterType"
          style="width: 120px"
          size="small"
          @change="handleFilterChange"
        >
          <a-select-option value="">全部类型</a-select-option>
          <a-select-option value="phone">电话联系</a-select-option>
          <a-select-option value="sms">短信通知</a-select-option>
          <a-select-option value="visit">上门拜访</a-select-option>
          <a-select-option value="email">邮件联系</a-select-option>
          <a-select-option value="other">其他方式</a-select-option>
        </a-select>
        
        <a-button size="small" @click="handleRefresh" :loading="loading">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </div>

    <div class="history-content">
      <a-spin :spinning="loading">
        <div v-if="filteredHistory.length > 0" class="history-list">
          <a-timeline>
            <a-timeline-item
              v-for="item in filteredHistory"
              :key="item.id"
              :color="getTimelineColor(item.type)"
            >
              <div class="history-item">
                <div class="item-header">
                  <div class="item-info">
                    <span class="item-type">{{ getTypeLabel(item.type) }}</span>
                    <span class="item-result" :class="getResultClass(item.result)">
                      {{ getResultLabel(item.result) }}
                    </span>
                  </div>
                  <div class="item-time">
                    <DateCell :date="item.followTime" />
                  </div>
                </div>

                <div class="item-content">
                  <p>{{ item.content }}</p>
                  
                  <!-- 客户态度 -->
                  <div v-if="item.attitude" class="item-detail">
                    <span class="detail-label">客户态度：</span>
                    <a-tag :color="getAttitudeColor(item.attitude)">
                      {{ getAttitudeLabel(item.attitude) }}
                    </a-tag>
                  </div>

                  <!-- 还款承诺 -->
                  <div v-if="item.promise" class="item-detail">
                    <span class="detail-label">还款承诺：</span>
                    <a-tag :color="getPromiseColor(item.promise)">
                      {{ getPromiseLabel(item.promise) }}
                    </a-tag>
                    <span v-if="item.promiseDate" class="promise-date">
                      （承诺日期：<DateCell :date="item.promiseDate" />）
                    </span>
                  </div>

                  <!-- 下次跟进 -->
                  <div v-if="item.nextFollowTime" class="item-detail">
                    <span class="detail-label">下次跟进：</span>
                    <DateCell :date="item.nextFollowTime" />
                  </div>

                  <!-- 备注 -->
                  <div v-if="item.remark" class="item-detail">
                    <span class="detail-label">备注：</span>
                    <span class="remark-text">{{ item.remark }}</span>
                  </div>

                  <!-- 附件 -->
                  <div v-if="item.attachments?.length" class="item-attachments">
                    <span class="detail-label">附件：</span>
                    <div class="attachment-list">
                      <a-tag
                        v-for="file in item.attachments"
                        :key="file.name"
                        @click="downloadAttachment(file)"
                        style="cursor: pointer"
                      >
                        <FileOutlined />
                        {{ file.name }}
                      </a-tag>
                    </div>
                  </div>
                </div>

                <div class="item-footer">
                  <span class="item-operator">跟进人：{{ item.operator }}</span>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>

        <a-empty v-else description="暂无跟进记录" />
      </a-spin>
    </div>

    <!-- 分页 -->
    <div v-if="total > pageSize" class="history-pagination">
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :show-size-changer="false"
        :show-quick-jumper="true"
        size="small"
        @change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ReloadOutlined, FileOutlined } from '@ant-design/icons-vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import { message } from 'ant-design-vue';

interface Props {
  orderId: string;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const filterType = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const historyData = ref<any[]>([]);

// 计算属性
const filteredHistory = computed(() => {
  let data = historyData.value;
  
  if (filterType.value) {
    data = data.filter(item => item.type === filterType.value);
  }
  
  return data;
});

// 方法
const loadHistory = async () => {
  if (!props.orderId) return;

  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    historyData.value = [
      {
        id: 1,
        type: 'phone',
        result: 'connected',
        attitude: 'cooperative',
        promise: 'within_week',
        promiseDate: '2024-01-22',
        content: '客户接听电话，态度配合。了解到客户最近资金紧张，承诺一周内还款。已告知具体还款方式和账户信息。',
        nextFollowTime: '2024-01-20 10:00:00',
        remark: '客户表示理解，会尽快安排还款',
        attachments: [],
        followTime: '2024-01-15 14:30:00',
        operator: '客服A'
      },
      {
        id: 2,
        type: 'sms',
        result: 'connected',
        attitude: '',
        promise: '',
        promiseDate: '',
        content: '发送还款提醒短信，告知逾期情况和还款方式',
        nextFollowTime: '',
        remark: '',
        attachments: [],
        followTime: '2024-01-14 09:00:00',
        operator: '系统'
      },
      {
        id: 3,
        type: 'phone',
        result: 'no_answer',
        attitude: '',
        promise: '',
        promiseDate: '',
        content: '拨打客户电话，无人接听。已留言提醒还款事宜。',
        nextFollowTime: '2024-01-15 14:00:00',
        remark: '多次拨打均无人接听',
        attachments: [],
        followTime: '2024-01-13 16:20:00',
        operator: '客服B'
      }
    ];
    
    total.value = historyData.value.length;
  } catch (error) {
    console.error('加载跟进历史失败:', error);
    message.error('加载跟进历史失败');
  } finally {
    loading.value = false;
  }
};

const handleFilterChange = () => {
  currentPage.value = 1;
};

const handleRefresh = () => {
  loadHistory();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const downloadAttachment = (file: any) => {
  // 模拟下载附件
  message.info(`下载附件：${file.name}`);
};

// 工具方法
const getTimelineColor = (type: string) => {
  const colors = {
    phone: 'blue',
    sms: 'green',
    visit: 'purple',
    email: 'orange',
    other: 'gray'
  };
  return colors[type] || 'gray';
};

const getTypeLabel = (type: string) => {
  const labels = {
    phone: '电话联系',
    sms: '短信通知',
    visit: '上门拜访',
    email: '邮件联系',
    other: '其他方式'
  };
  return labels[type] || type;
};

const getResultLabel = (result: string) => {
  const labels = {
    connected: '联系成功',
    no_answer: '无人接听',
    busy: '占线',
    wrong_number: '号码错误',
    refused: '拒绝接听',
    other: '其他'
  };
  return labels[result] || result;
};

const getResultClass = (result: string) => {
  if (result === 'connected') return 'result-success';
  if (['no_answer', 'busy', 'refused'].includes(result)) return 'result-warning';
  if (result === 'wrong_number') return 'result-error';
  return '';
};

const getAttitudeLabel = (attitude: string) => {
  const labels = {
    cooperative: '配合',
    neutral: '中性',
    resistant: '抵触',
    aggressive: '恶劣'
  };
  return labels[attitude] || attitude;
};

const getAttitudeColor = (attitude: string) => {
  const colors = {
    cooperative: 'green',
    neutral: 'blue',
    resistant: 'orange',
    aggressive: 'red'
  };
  return colors[attitude] || 'default';
};

const getPromiseLabel = (promise: string) => {
  const labels = {
    immediate: '立即还款',
    within_week: '一周内还款',
    within_month: '一月内还款',
    negotiate: '协商还款',
    refuse: '拒绝还款',
    none: '无承诺'
  };
  return labels[promise] || promise;
};

const getPromiseColor = (promise: string) => {
  const colors = {
    immediate: 'green',
    within_week: 'blue',
    within_month: 'orange',
    negotiate: 'purple',
    refuse: 'red',
    none: 'gray'
  };
  return colors[promise] || 'default';
};

// 监听器
watch(() => props.orderId, (orderId) => {
  if (orderId) {
    loadHistory();
  }
}, { immediate: true });
</script>

<style lang="scss" scoped>
.follow-up-history {
  .history-header {
    @include flex-between;
    margin-bottom: $space-4;
    
    h4 {
      font-size: $text-base;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .history-content {
    .history-list {
      .history-item {
        .item-header {
          @include flex-between;
          margin-bottom: $space-2;
          
          .item-info {
            @include flex-center-vertical;
            gap: $space-2;
            
            .item-type {
              font-weight: $font-medium;
              color: $text-primary;
            }
            
            .item-result {
              font-size: $text-sm;
              
              &.result-success {
                color: $success-600;
              }
              
              &.result-warning {
                color: $warning-600;
              }
              
              &.result-error {
                color: $error-600;
              }
            }
          }
          
          .item-time {
            font-size: $text-sm;
            color: $text-tertiary;
          }
        }
        
        .item-content {
          margin-bottom: $space-3;
          
          p {
            color: $text-secondary;
            line-height: $leading-relaxed;
            margin-bottom: $space-2;
          }
          
          .item-detail {
            @include flex-center-vertical;
            gap: $space-2;
            margin-bottom: $space-2;
            font-size: $text-sm;
            
            .detail-label {
              color: $text-tertiary;
              flex-shrink: 0;
            }
            
            .promise-date {
              color: $text-tertiary;
              font-size: $text-xs;
            }
            
            .remark-text {
              color: $text-secondary;
            }
          }
          
          .item-attachments {
            .detail-label {
              color: $text-tertiary;
              font-size: $text-sm;
              margin-bottom: $space-2;
              display: block;
            }
            
            .attachment-list {
              display: flex;
              flex-wrap: wrap;
              gap: $space-2;
            }
          }
        }
        
        .item-footer {
          .item-operator {
            font-size: $text-xs;
            color: $text-tertiary;
          }
        }
      }
    }
  }
  
  .history-pagination {
    margin-top: $space-4;
    text-align: center;
  }
}
</style>
