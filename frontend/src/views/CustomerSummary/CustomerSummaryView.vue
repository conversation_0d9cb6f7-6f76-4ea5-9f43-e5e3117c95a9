<!-- 客户汇总页面 -->
<template>
  <div class="customer-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">客户汇总分析</h1>
        <p class="page-description">客户数据统计分析，了解客户分布和业务情况</p>
      </div>
      
      <div class="header-actions">
        <a-space>
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
          
          <a-button @click="handleRefresh" :loading="loading">
            <ReloadOutlined />
            刷新数据
          </a-button>
          
          <a-button
            v-if="userStore.canExportData"
            @click="handleExport"
            :loading="exportLoading"
          >
            <DownloadOutlined />
            导出报告
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 客户概览统计 -->
    <div class="overview-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <StatCard
            title="总客户数"
            :value="summaryData.totalCustomers"
            :trend="summaryData.customersTrend"
            icon="UserOutlined"
            color="blue"
            :loading="loading"
          />
        </a-col>
        <a-col :span="6">
          <StatCard
            title="活跃客户"
            :value="summaryData.activeCustomers"
            :trend="summaryData.activeTrend"
            icon="UserSwitchOutlined"
            color="green"
            :loading="loading"
          />
        </a-col>
        <a-col :span="6">
          <StatCard
            title="新增客户"
            :value="summaryData.newCustomers"
            :trend="summaryData.newTrend"
            icon="UserAddOutlined"
            color="purple"
            :loading="loading"
          />
        </a-col>
        <a-col :span="6">
          <StatCard
            title="流失客户"
            :value="summaryData.churnCustomers"
            :trend="summaryData.churnTrend"
            icon="UserDeleteOutlined"
            color="red"
            :loading="loading"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 客户分布图 -->
        <a-col :span="12">
          <div class="chart-card">
            <div class="card-header">
              <h3>客户地区分布</h3>
              <a-select
                v-model:value="regionChartType"
                style="width: 120px"
                size="small"
                @change="updateRegionChart"
              >
                <a-select-option value="pie">饼图</a-select-option>
                <a-select-option value="bar">柱状图</a-select-option>
              </a-select>
            </div>
            
            <ChartComponent
              :type="regionChartType"
              :data="regionDistributionData"
              :loading="loading"
              height="350px"
              @refresh="loadRegionData"
            />
          </div>
        </a-col>

        <!-- 客户价值分布 -->
        <a-col :span="12">
          <div class="chart-card">
            <div class="card-header">
              <h3>客户价值分布</h3>
            </div>
            
            <ChartComponent
              type="pie"
              :data="customerValueData"
              :loading="loading"
              height="350px"
              @refresh="loadValueData"
            />
          </div>
        </a-col>

        <!-- 客户增长趋势 -->
        <a-col :span="24">
          <div class="chart-card">
            <div class="card-header">
              <h3>客户增长趋势</h3>
              <a-radio-group v-model:value="growthPeriod" size="small" @change="updateGrowthChart">
                <a-radio-button value="month">按月</a-radio-button>
                <a-radio-button value="quarter">按季度</a-radio-button>
                <a-radio-button value="year">按年</a-radio-button>
              </a-radio-group>
            </div>
            
            <ChartComponent
              type="line"
              :data="customerGrowthData"
              :loading="loading"
              height="400px"
              @refresh="loadGrowthData"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 客户排行榜 -->
    <div class="ranking-section">
      <a-row :gutter="16">
        <!-- 高价值客户排行 -->
        <a-col :span="12">
          <div class="ranking-card">
            <div class="card-header">
              <h3>高价值客户TOP10</h3>
              <a-select
                v-model:value="valueMetric"
                style="width: 120px"
                size="small"
                @change="updateValueRanking"
              >
                <a-select-option value="total_amount">总金额</a-select-option>
                <a-select-option value="order_count">订单数</a-select-option>
                <a-select-option value="avg_amount">平均金额</a-select-option>
              </a-select>
            </div>
            
            <div class="ranking-list">
              <div
                v-for="(customer, index) in topValueCustomers"
                :key="customer.name"
                class="ranking-item"
                @click="viewCustomerDetail(customer)"
              >
                <div class="ranking-number">
                  <a-badge
                    :count="index + 1"
                    :number-style="getRankingStyle(index)"
                  />
                </div>
                <div class="customer-info">
                  <div class="customer-name">{{ customer.name }}</div>
                  <div class="customer-metric">
                    {{ formatMetricValue(customer.value, valueMetric) }}
                  </div>
                </div>
                <div class="customer-trend">
                  <a-tag :color="customer.trend > 0 ? 'green' : 'red'">
                    {{ customer.trend > 0 ? '+' : '' }}{{ customer.trend }}%
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 活跃客户排行 -->
        <a-col :span="12">
          <div class="ranking-card">
            <div class="card-header">
              <h3>活跃客户TOP10</h3>
              <a-select
                v-model:value="activityMetric"
                style="width: 120px"
                size="small"
                @change="updateActivityRanking"
              >
                <a-select-option value="recent_orders">近期订单</a-select-option>
                <a-select-option value="login_frequency">登录频次</a-select-option>
                <a-select-option value="interaction_score">互动评分</a-select-option>
              </a-select>
            </div>
            
            <div class="ranking-list">
              <div
                v-for="(customer, index) in topActiveCustomers"
                :key="customer.name"
                class="ranking-item"
                @click="viewCustomerDetail(customer)"
              >
                <div class="ranking-number">
                  <a-badge
                    :count="index + 1"
                    :number-style="getRankingStyle(index)"
                  />
                </div>
                <div class="customer-info">
                  <div class="customer-name">{{ customer.name }}</div>
                  <div class="customer-metric">
                    {{ formatMetricValue(customer.value, activityMetric) }}
                  </div>
                </div>
                <div class="customer-status">
                  <a-tag :color="getActivityColor(customer.status)">
                    {{ customer.status }}
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 客户分析表格 -->
    <div class="analysis-table">
      <div class="table-header">
        <h3>客户详细分析</h3>
        <a-space>
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索客户"
            style="width: 200px"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
          
          <a-select
            v-model:value="customerSegment"
            placeholder="客户分群"
            style="width: 120px"
            @change="handleSegmentChange"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="high_value">高价值</a-select-option>
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="potential">潜在</a-select-option>
            <a-select-option value="risk">风险</a-select-option>
          </a-select>
        </a-space>
      </div>

      <DataTable
        :columns="tableColumns"
        :data-source="filteredCustomerData"
        :loading="loading"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100'],
          defaultPageSize: 20
        }"
        @refresh="handleRefresh"
        @export="handleTableExport"
      >
        <!-- 自定义单元格渲染 -->
        <template #cell="{ column, record, value }">
          <!-- 客户姓名列 -->
          <template v-if="column.dataIndex === 'customerName'">
            <a-button
              type="link"
              @click="viewCustomerDetail(record)"
            >
              {{ value }}
            </a-button>
          </template>
          
          <!-- 金额列 -->
          <template v-else-if="column.dataIndex === 'totalAmount' || column.dataIndex === 'avgAmount'">
            <AmountCell :amount="value" />
          </template>
          
          <!-- 客户分群列 -->
          <template v-else-if="column.dataIndex === 'segment'">
            <a-tag :color="getSegmentColor(value)">
              {{ getSegmentText(value) }}
            </a-tag>
          </template>
          
          <!-- 风险等级列 -->
          <template v-else-if="column.dataIndex === 'riskLevel'">
            <a-tag :color="getRiskColor(value)">
              {{ getRiskText(value) }}
            </a-tag>
          </template>
          
          <!-- 最后订单日期列 -->
          <template v-else-if="column.dataIndex === 'lastOrderDate'">
            <DateCell :date="value" />
          </template>
          
          <!-- 默认渲染 -->
          <template v-else>
            {{ value }}
          </template>
        </template>
      </DataTable>
    </div>

    <!-- 客户详情模态框 -->
    <CustomerDetailModal
      v-model:visible="customerModalVisible"
      :customer-name="selectedCustomer"
      @close="customerModalVisible = false"
    />

    <!-- 导出模态框 -->
    <ExportModal
      v-model:visible="exportModalVisible"
      :data="filteredCustomerData"
      :filename="`客户汇总分析_${Date.now()}`"
      @export="handleConfirmExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ReloadOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { useDataStore } from '@/stores/data';
import { useUserStore } from '@/stores/user';
import StatCard from '@/views/Dashboard/components/StatCard.vue';
import ChartComponent from '@/components/Charts/ChartComponent.vue';
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import CustomerDetailModal from '@/views/CustomerQuery/components/CustomerDetailModal.vue';
import ExportModal from '@/components/common/ExportModal.vue';
import { message } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';

const dataStore = useDataStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const dateRange = ref<[Dayjs, Dayjs]>([
  dayjs().subtract(1, 'month'),
  dayjs()
]);

const regionChartType = ref<'pie' | 'bar'>('pie');
const growthPeriod = ref('month');
const valueMetric = ref('total_amount');
const activityMetric = ref('recent_orders');
const searchKeyword = ref('');
const customerSegment = ref('');
const customerModalVisible = ref(false);
const exportModalVisible = ref(false);
const selectedCustomer = ref('');

// 汇总数据
const summaryData = reactive({
  totalCustomers: 0,
  activeCustomers: 0,
  newCustomers: 0,
  churnCustomers: 0,
  customersTrend: 0,
  activeTrend: 0,
  newTrend: 0,
  churnTrend: 0
});

// 图表数据
const regionDistributionData = ref({
  labels: [],
  datasets: []
});

const customerValueData = ref({
  labels: [],
  datasets: []
});

const customerGrowthData = ref({
  labels: [],
  datasets: []
});

// 排行榜数据
const topValueCustomers = ref([]);
const topActiveCustomers = ref([]);

// 客户分析数据
const customerAnalysisData = ref([]);

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '客户分群',
    dataIndex: 'segment',
    key: 'segment',
    width: 100
  },
  {
    title: '订单数量',
    dataIndex: 'orderCount',
    key: 'orderCount',
    width: 100,
    sorter: true
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 120,
    sorter: true
  },
  {
    title: '平均金额',
    dataIndex: 'avgAmount',
    key: 'avgAmount',
    width: 120,
    sorter: true
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    key: 'riskLevel',
    width: 100
  },
  {
    title: '最后订单',
    dataIndex: 'lastOrderDate',
    key: 'lastOrderDate',
    width: 120
  },
  {
    title: '客户价值',
    dataIndex: 'customerValue',
    key: 'customerValue',
    width: 100
  }
]);

// 计算属性
const filteredCustomerData = computed(() => {
  let data = customerAnalysisData.value;
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    data = data.filter((item: any) => 
      item.customerName?.toLowerCase().includes(keyword)
    );
  }
  
  // 分群过滤
  if (customerSegment.value) {
    data = data.filter((item: any) => item.segment === customerSegment.value);
  }
  
  return data;
});

// 方法
const loadSummaryData = async () => {
  loading.value = true;
  try {
    // 模拟加载汇总数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Object.assign(summaryData, {
      totalCustomers: 1256,
      activeCustomers: 892,
      newCustomers: 156,
      churnCustomers: 23,
      customersTrend: 12.5,
      activeTrend: 8.3,
      newTrend: 25.6,
      churnTrend: -15.2
    });
  } catch (error) {
    console.error('加载汇总数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const loadChartData = async () => {
  // 加载地区分布数据
  regionDistributionData.value = {
    labels: ['北京', '上海', '广州', '深圳', '杭州', '其他'],
    datasets: [{
      label: '客户分布',
      data: [320, 280, 180, 150, 120, 200],
      backgroundColor: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2']
    }]
  };

  // 加载客户价值数据
  customerValueData.value = {
    labels: ['高价值客户', '中价值客户', '低价值客户', '潜在客户'],
    datasets: [{
      label: '客户价值分布',
      data: [25, 35, 30, 10],
      backgroundColor: ['#52c41a', '#1890ff', '#faad14', '#f5222d']
    }]
  };

  // 加载增长趋势数据
  customerGrowthData.value = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '新增客户',
      data: [120, 150, 180, 200, 160, 190],
      borderColor: '#1890ff',
      backgroundColor: 'rgba(24, 144, 255, 0.1)',
      fill: true
    }, {
      label: '活跃客户',
      data: [800, 820, 850, 880, 860, 890],
      borderColor: '#52c41a',
      backgroundColor: 'rgba(82, 196, 26, 0.1)',
      fill: true
    }]
  };
};

const loadRankingData = async () => {
  // 模拟排行榜数据
  topValueCustomers.value = [
    { name: '张三', value: 1250000, trend: 15.2 },
    { name: '李四', value: 980000, trend: 8.5 },
    { name: '王五', value: 850000, trend: -2.1 },
    { name: '赵六', value: 720000, trend: 12.8 },
    { name: '钱七', value: 650000, trend: 5.6 }
  ];

  topActiveCustomers.value = [
    { name: '张三', value: 25, status: '高活跃' },
    { name: '李四', value: 22, status: '高活跃' },
    { name: '王五', value: 18, status: '中活跃' },
    { name: '赵六', value: 15, status: '中活跃' },
    { name: '钱七', value: 12, status: '低活跃' }
  ];
};

const loadCustomerAnalysisData = async () => {
  // 模拟客户分析数据
  customerAnalysisData.value = [
    {
      customerName: '张三',
      segment: 'high_value',
      orderCount: 25,
      totalAmount: 1250000,
      avgAmount: 50000,
      riskLevel: 'low',
      lastOrderDate: '2024-01-15',
      customerValue: 'A'
    },
    {
      customerName: '李四',
      segment: 'active',
      orderCount: 18,
      totalAmount: 980000,
      avgAmount: 54444,
      riskLevel: 'medium',
      lastOrderDate: '2024-01-12',
      customerValue: 'B'
    }
    // ... 更多数据
  ];
};

const handleDateRangeChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    dateRange.value = dates;
    loadAllData();
  }
};

const handleRefresh = () => {
  loadAllData();
};

const loadAllData = async () => {
  await Promise.all([
    loadSummaryData(),
    loadChartData(),
    loadRankingData(),
    loadCustomerAnalysisData()
  ]);
};

// 图表更新方法
const updateRegionChart = () => {
  loadChartData();
};

const updateGrowthChart = () => {
  loadChartData();
};

const updateValueRanking = () => {
  loadRankingData();
};

const updateActivityRanking = () => {
  loadRankingData();
};

// 工具方法
const getRankingStyle = (index: number) => {
  const colors = ['#f5222d', '#fa8c16', '#faad14'];
  return {
    backgroundColor: colors[index] || '#1890ff',
    color: '#fff'
  };
};

const formatMetricValue = (value: number, metric: string) => {
  switch (metric) {
    case 'total_amount':
    case 'avg_amount':
      return `¥${value.toLocaleString()}`;
    case 'order_count':
    case 'recent_orders':
      return `${value}单`;
    case 'login_frequency':
      return `${value}次`;
    case 'interaction_score':
      return `${value}分`;
    default:
      return value;
  }
};

const getActivityColor = (status: string) => {
  const colors = {
    '高活跃': 'green',
    '中活跃': 'blue',
    '低活跃': 'orange'
  };
  return colors[status] || 'default';
};

const getSegmentColor = (segment: string) => {
  const colors = {
    'high_value': 'red',
    'active': 'green',
    'potential': 'blue',
    'risk': 'orange'
  };
  return colors[segment] || 'default';
};

const getSegmentText = (segment: string) => {
  const texts = {
    'high_value': '高价值',
    'active': '活跃',
    'potential': '潜在',
    'risk': '风险'
  };
  return texts[segment] || segment;
};

const getRiskColor = (level: string) => {
  const colors = {
    'low': 'green',
    'medium': 'orange',
    'high': 'red'
  };
  return colors[level] || 'default';
};

const getRiskText = (level: string) => {
  const texts = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  };
  return texts[level] || level;
};

const viewCustomerDetail = (customer: any) => {
  selectedCustomer.value = customer.customerName || customer.name;
  customerModalVisible.value = true;
};

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const handleSegmentChange = () => {
  // 分群过滤逻辑已在计算属性中处理
};

const handleExport = () => {
  exportModalVisible.value = true;
};

const handleTableExport = (format: 'excel' | 'csv') => {
  handleConfirmExport(format);
};

const handleConfirmExport = async (format: 'excel' | 'csv') => {
  exportLoading.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    message.success('导出成功');
    exportModalVisible.value = false;
  } catch (error) {
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadAllData();
});
</script>

<style lang="scss" scoped>
.customer-summary-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  @include flex-between;
  margin-bottom: $space-6;
  
  @include responsive('md') {
    flex-direction: column;
    gap: $space-4;
    align-items: flex-start;
  }
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.overview-stats {
  margin-bottom: $space-8;
}

.charts-section {
  margin-bottom: $space-8;
  
  .chart-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    height: 100%;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
  }
}

.ranking-section {
  margin-bottom: $space-8;
  
  .ranking-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    height: 100%;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .ranking-list {
      .ranking-item {
        @include flex-between;
        padding: $space-3;
        @include border-radius();
        @include transition(background-color);
        cursor: pointer;
        
        &:hover {
          background: $bg-secondary;
        }
        
        .ranking-number {
          flex-shrink: 0;
        }
        
        .customer-info {
          flex: 1;
          margin-left: $space-3;
          
          .customer-name {
            font-weight: $font-medium;
            color: $text-primary;
            margin-bottom: $space-1;
          }
          
          .customer-metric {
            font-size: $text-sm;
            color: $text-secondary;
          }
        }
        
        .customer-trend,
        .customer-status {
          flex-shrink: 0;
        }
      }
    }
  }
}

.analysis-table {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
  
  .table-header {
    @include flex-between;
    padding: $space-6 $space-6 0;
    
    h3 {
      font-size: $text-xl;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0;
    }
  }
}
</style>
