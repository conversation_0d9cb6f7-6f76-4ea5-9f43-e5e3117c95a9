<!-- 客户详情模态框 -->
<template>
  <a-modal
    v-model:visible="modalVisible"
    title="客户详情"
    :width="800"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="customer-detail-modal">
      <a-spin :spinning="loading">
        <div v-if="customerData" class="customer-content">
          <!-- 客户基本信息 -->
          <div class="customer-header">
            <a-avatar size="large" :style="{ backgroundColor: '#1890ff' }">
              {{ customerData.name?.charAt(0) }}
            </a-avatar>
            <div class="customer-info">
              <h3>{{ customerData.name }}</h3>
              <p>{{ customerData.phone }}</p>
            </div>
            <div class="customer-status">
              <a-tag :color="getCustomerStatusColor(customerData.status)">
                {{ customerData.status }}
              </a-tag>
            </div>
          </div>

          <!-- 客户统计 -->
          <div class="customer-stats">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总订单数"
                  :value="customerData.totalOrders"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="总金额"
                  :value="customerData.totalAmount"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="当前待收"
                  :value="customerData.currentAmount"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#faad14' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="逾期金额"
                  :value="customerData.overdueAmount"
                  :precision="2"
                  suffix="元"
                  :value-style="{ color: '#ff4d4f' }"
                />
              </a-col>
            </a-row>
          </div>

          <!-- 详细信息 -->
          <a-tabs>
            <a-tab-pane key="basic" tab="基本信息">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="客户姓名">
                  {{ customerData.name }}
                </a-descriptions-item>
                <a-descriptions-item label="手机号码">
                  {{ customerData.phone }}
                </a-descriptions-item>
                <a-descriptions-item label="身份证号">
                  {{ customerData.idCard || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="客户状态">
                  <a-tag :color="getCustomerStatusColor(customerData.status)">
                    {{ customerData.status }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="注册时间">
                  <DateCell :date="customerData.registerTime" />
                </a-descriptions-item>
                <a-descriptions-item label="最后登录">
                  <DateCell :date="customerData.lastLoginTime" />
                </a-descriptions-item>
                <a-descriptions-item label="客户来源">
                  {{ customerData.source || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="客服">
                  {{ customerData.service || '-' }}
                </a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>

            <a-tab-pane key="orders" tab="订单列表">
              <DataTable
                :columns="orderColumns"
                :data-source="customerData.orders || []"
                :pagination="{ pageSize: 5 }"
                size="small"
              >
                <template #cell="{ column, record, value }">
                  <template v-if="column.dataIndex === 'orderAmount'">
                    <AmountCell :amount="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'orderDate'">
                    <DateCell :date="value" />
                  </template>
                  <template v-else-if="column.dataIndex === 'status'">
                    <StatusTag :status="value" />
                  </template>
                  <template v-else>
                    {{ value }}
                  </template>
                </template>
              </DataTable>
            </a-tab-pane>

            <a-tab-pane key="contact" tab="联系记录">
              <div class="contact-records">
                <div
                  v-for="record in customerData.contactRecords || []"
                  :key="record.id"
                  class="contact-item"
                >
                  <div class="contact-header">
                    <span class="contact-type">{{ record.type }}</span>
                    <span class="contact-time">
                      <DateCell :date="record.time" />
                    </span>
                  </div>
                  <div class="contact-content">{{ record.content }}</div>
                  <div class="contact-operator">操作人：{{ record.operator }}</div>
                </div>
                
                <a-empty v-if="!customerData.contactRecords?.length" description="暂无联系记录" />
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>

        <a-empty v-else description="客户信息不存在" />
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import StatusTag from '@/components/DataTable/StatusTag.vue';
import { message } from 'ant-design-vue';

interface Props {
  visible: boolean;
  customerName: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  close: [];
}>();

// 响应式数据
const loading = ref(false);
const customerData = ref<any>(null);

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 订单列配置
const orderColumns = [
  {
    title: '订单编号',
    dataIndex: 'orderNumber',
    key: 'orderNumber',
    width: 150
  },
  {
    title: '订单日期',
    dataIndex: 'orderDate',
    key: 'orderDate',
    width: 120
  },
  {
    title: '订单金额',
    dataIndex: 'orderAmount',
    key: 'orderAmount',
    width: 120
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    key: 'businessType',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  }
];

// 方法
const loadCustomerData = async (customerName: string) => {
  if (!customerName) return;

  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    customerData.value = {
      name: customerName,
      phone: '138****8888',
      idCard: '110101199001011234',
      status: '正常',
      registerTime: '2023-01-15',
      lastLoginTime: '2024-01-15',
      source: '线上推广',
      service: '客服A',
      totalOrders: 15,
      totalAmount: 750000,
      currentAmount: 250000,
      overdueAmount: 0,
      orders: [
        {
          orderNumber: 'ORD20240115001',
          orderDate: '2024-01-15',
          orderAmount: 50000,
          businessType: '电商',
          status: '正常'
        },
        {
          orderNumber: 'ORD20240110002',
          orderDate: '2024-01-10',
          orderAmount: 80000,
          businessType: '租赁',
          status: '结清'
        }
      ],
      contactRecords: [
        {
          id: 1,
          type: '电话联系',
          time: '2024-01-15 10:30:00',
          content: '客户咨询还款事宜，已告知具体还款方式',
          operator: '客服A'
        },
        {
          id: 2,
          type: '短信通知',
          time: '2024-01-14 09:00:00',
          content: '发送还款提醒短信',
          operator: '系统'
        }
      ]
    };
  } catch (error) {
    console.error('加载客户数据失败:', error);
    message.error('加载客户数据失败');
  } finally {
    loading.value = false;
  }
};

const getCustomerStatusColor = (status: string) => {
  const colors = {
    '正常': 'green',
    '逾期': 'red',
    '黑名单': 'volcano',
    '潜在': 'blue'
  };
  return colors[status] || 'default';
};

const handleClose = () => {
  emit('close');
};

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.customerName) {
    loadCustomerData(props.customerName);
  }
});

watch(() => props.customerName, (customerName) => {
  if (props.visible && customerName) {
    loadCustomerData(customerName);
  }
});
</script>

<style lang="scss" scoped>
.customer-detail-modal {
  .customer-content {
    .customer-header {
      @include flex-center-vertical;
      gap: $space-4;
      margin-bottom: $space-6;
      padding-bottom: $space-4;
      border-bottom: 1px solid $border-light;
      
      .customer-info {
        flex: 1;
        
        h3 {
          font-size: $text-xl;
          font-weight: $font-semibold;
          color: $text-primary;
          margin: 0 0 $space-1 0;
        }
        
        p {
          color: $text-secondary;
          margin: 0;
        }
      }
      
      .customer-status {
        flex-shrink: 0;
      }
    }
    
    .customer-stats {
      margin-bottom: $space-6;
    }
  }
  
  .contact-records {
    .contact-item {
      padding: $space-4;
      border: 1px solid $border-light;
      @include border-radius();
      margin-bottom: $space-3;
      
      .contact-header {
        @include flex-between;
        margin-bottom: $space-2;
        
        .contact-type {
          font-weight: $font-medium;
          color: $text-primary;
        }
        
        .contact-time {
          font-size: $text-sm;
          color: $text-tertiary;
        }
      }
      
      .contact-content {
        color: $text-secondary;
        line-height: $leading-relaxed;
        margin-bottom: $space-2;
      }
      
      .contact-operator {
        font-size: $text-sm;
        color: $text-tertiary;
      }
    }
  }
}
</style>
