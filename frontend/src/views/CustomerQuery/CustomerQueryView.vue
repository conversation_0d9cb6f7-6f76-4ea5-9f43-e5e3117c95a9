<!-- 客户查询页面 -->
<template>
  <div class="customer-query-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">客户查询</h1>
        <p class="page-description">查询客户的订单信息和历史记录</p>
      </div>
    </div>

    <!-- 查询表单 -->
    <div class="query-form-card">
      <a-form
        ref="formRef"
        :model="queryForm"
        :rules="formRules"
        layout="inline"
        class="query-form"
        @finish="handleQuery"
      >
        <a-form-item name="customerName" label="客户姓名">
          <a-auto-complete
            v-model:value="queryForm.customerName"
            :options="customerOptions"
            placeholder="请输入客户姓名"
            style="width: 200px"
            @search="handleCustomerSearch"
            @select="handleCustomerSelect"
          />
        </a-form-item>

        <a-form-item name="phone" label="客户手机">
          <a-input
            v-model:value="queryForm.phone"
            placeholder="请输入手机号"
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </a-form-item>

        <a-form-item name="orderStatus" label="订单状态">
          <a-select
            v-model:value="queryForm.orderStatus"
            placeholder="请选择状态"
            style="width: 120px"
            allow-clear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="逾期">逾期</a-select-option>
            <a-select-option value="结清">结清</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button
              type="primary"
              html-type="submit"
              :loading="dataStore.loading.customer"
              @click="handleQuery"
            >
              <SearchOutlined />
              查询
            </a-button>
            
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 客户信息卡片 -->
    <div v-if="customerInfo" class="customer-info-card">
      <div class="customer-header">
        <div class="customer-basic">
          <a-avatar size="large" :style="{ backgroundColor: '#1890ff' }">
            {{ customerInfo.customer_name?.charAt(0) }}
          </a-avatar>
          <div class="customer-details">
            <h3>{{ customerInfo.customer_name }}</h3>
            <p>{{ customerInfo.phone }}</p>
          </div>
        </div>
        
        <div class="customer-stats">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="总订单数"
                :value="customerInfo.total_orders"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="总融资额"
                :value="customerInfo.total_financing"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="当前待收"
                :value="customerInfo.current_outstanding"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#faad14' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="逾期金额"
                :value="customerInfo.overdue_amount"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#ff4d4f' }"
              />
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 客户订单图表 -->
      <div v-if="customerInfo.chart_data" class="customer-chart">
        <ChartComponent
          type="line"
          :data="customerInfo.chart_data"
          title="订单趋势"
          height="300px"
          :loading="dataStore.loading.customer"
        />
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="orders-table-card">
      <div class="table-header">
        <h3>订单列表</h3>
        <a-space v-if="dataStore.hasCustomerData">
          <a-button
            v-if="userStore.canExportData"
            @click="handleExport"
            :loading="exportLoading"
          >
            <DownloadOutlined />
            导出订单
          </a-button>
          
          <a-button @click="handleRefresh">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <DataTable
        :columns="tableColumns"
        :data-source="tableData"
        :loading="dataStore.loading.customer"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100'],
          defaultPageSize: 20
        }"
        @refresh="handleRefresh"
        @export="handleTableExport"
      >
        <!-- 自定义单元格渲染 -->
        <template #cell="{ column, record, value }">
          <!-- 金额列 -->
          <template v-if="column.dataIndex === '金额' || column.dataIndex === '当前待收'">
            <AmountCell :amount="value" />
          </template>
          
          <!-- 日期列 -->
          <template v-else-if="column.dataIndex === '订单日期' || column.dataIndex === '账单日期'">
            <DateCell :date="value" />
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.dataIndex === '状态'">
            <StatusTag :status="value" />
          </template>
          
          <!-- 逾期天数列 -->
          <template v-else-if="column.dataIndex === '逾期天数'">
            <a-tag v-if="value > 0" color="red">{{ value }}天</a-tag>
            <span v-else>-</span>
          </template>
          
          <!-- 订单编号列 -->
          <template v-else-if="column.dataIndex === '订单编号'">
            <a-button
              type="link"
              size="small"
              @click="viewOrderDetail(record)"
            >
              {{ value }}
            </a-button>
          </template>
          
          <!-- 默认渲染 -->
          <template v-else>
            {{ value }}
          </template>
        </template>

        <!-- 展开行内容 -->
        <template #expanded-row="{ record }">
          <div class="expanded-detail">
            <a-descriptions :column="3" size="small" bordered>
              <a-descriptions-item label="订单编号">
                {{ record.订单编号 }}
              </a-descriptions-item>
              <a-descriptions-item label="成本">
                <AmountCell :amount="record.成本" />
              </a-descriptions-item>
              <a-descriptions-item label="总待收">
                <AmountCell :amount="record.总待收" />
              </a-descriptions-item>
              <a-descriptions-item label="业务类型">
                {{ record.业务 }}
              </a-descriptions-item>
              <a-descriptions-item label="产品类型">
                {{ record.产品 }}
              </a-descriptions-item>
              <a-descriptions-item label="客服">
                {{ record.客服 }}
              </a-descriptions-item>
              
              <!-- 期数信息 -->
              <template v-for="(value, key) in getInstallmentInfo(record)" :key="key">
                <a-descriptions-item :label="key">
                  <StatusTag :status="value" />
                </a-descriptions-item>
              </template>
            </a-descriptions>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- 订单详情模态框 -->
    <OrderDetailModal
      v-model:visible="orderModalVisible"
      :order-id="selectedOrderId"
      @close="orderModalVisible = false"
    />

    <!-- 导出模态框 -->
    <ExportModal
      v-model:visible="exportModalVisible"
      :data="tableData"
      :filename="`客户订单_${queryForm.customerName}_${Date.now()}`"
      @export="handleConfirmExport"
    />
  </div>
</template>

<script setup lang="ts">
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { useDataStore } from '@/stores/data';
import { useUserStore } from '@/stores/user';
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import StatusTag from '@/components/DataTable/StatusTag.vue';
import ChartComponent from '@/components/Charts/ChartComponent.vue';
import OrderDetailModal from './components/OrderDetailModal.vue';
import ExportModal from '@/components/common/ExportModal.vue';
import { message } from 'ant-design-vue';

const dataStore = useDataStore();
const userStore = useUserStore();

// 表单引用
const formRef = ref();

// 响应式数据
const queryForm = reactive({
  customerName: '',
  phone: '',
  orderStatus: ''
});

const customerOptions = ref<Array<{ value: string }>>([]);
const customerInfo = ref<any>(null);
const exportLoading = ref(false);
const orderModalVisible = ref(false);
const exportModalVisible = ref(false);
const selectedOrderId = ref('');

// 表单验证规则
const formRules = {
  customerName: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ]
};

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '订单编号',
    dataIndex: '订单编号',
    key: '订单编号',
    width: 150,
    fixed: 'left'
  },
  {
    title: '订单日期',
    dataIndex: '订单日期',
    key: '订单日期',
    width: 120,
    sorter: true
  },
  {
    title: '金额',
    dataIndex: '金额',
    key: '金额',
    width: 120,
    sorter: true
  },
  {
    title: '当前待收',
    dataIndex: '当前待收',
    key: '当前待收',
    width: 120,
    sorter: true
  },
  {
    title: '业务',
    dataIndex: '业务',
    key: '业务',
    width: 100
  },
  {
    title: '产品',
    dataIndex: '产品',
    key: '产品',
    width: 120
  },
  {
    title: '客服',
    dataIndex: '客服',
    key: '客服',
    width: 100
  },
  {
    title: '逾期天数',
    dataIndex: '逾期天数',
    key: '逾期天数',
    width: 100,
    sorter: true
  },
  {
    title: '状态',
    dataIndex: '状态',
    key: '状态',
    width: 100
  }
]);

// 计算属性
const tableData = computed(() => {
  if (!dataStore.customerOrders?.results) return [];
  
  return dataStore.customerOrders.results.map(record => ({
    ...record,
    key: record.订单编号
  }));
});

// 方法
const handleCustomerSearch = (searchText: string) => {
  // 模拟客户名称搜索
  if (searchText) {
    customerOptions.value = [
      { value: '张三' },
      { value: '李四' },
      { value: '王五' },
      { value: '赵六' }
    ].filter(option => option.value.includes(searchText));
  } else {
    customerOptions.value = [];
  }
};

const handleCustomerSelect = (value: string) => {
  queryForm.customerName = value;
};

const handleQuery = async () => {
  try {
    await formRef.value?.validate();
    
    const params = {
      customer_name: queryForm.customerName
    };
    
    // 获取客户订单数据
    await dataStore.fetchCustomerOrders(params);
    
    // 获取客户汇总信息
    try {
      customerInfo.value = await dataStore.fetchCustomerSummary(params);
    } catch (error) {
      console.error('获取客户汇总信息失败:', error);
    }
    
    if (dataStore.hasCustomerData) {
      message.success(`查询成功，共找到 ${tableData.value.length} 条订单`);
    } else {
      message.info('未找到该客户的订单信息');
      customerInfo.value = null;
    }
  } catch (error) {
    console.error('查询失败:', error);
  }
};

const handleReset = () => {
  queryForm.customerName = '';
  queryForm.phone = '';
  queryForm.orderStatus = '';
  customerInfo.value = null;
  
  // 清除查询结果
  dataStore.clearCache('customer');
};

const handleRefresh = () => {
  if (queryForm.customerName) {
    handleQuery();
  }
};

const handleExport = () => {
  exportModalVisible.value = true;
};

const handleTableExport = (format: 'excel' | 'csv') => {
  handleConfirmExport(format);
};

const handleConfirmExport = async (format: 'excel' | 'csv') => {
  if (!userStore.canExportData) {
    message.warning('您没有权限导出数据');
    return;
  }
  
  exportLoading.value = true;
  try {
    // 这里调用导出API
    const params = {
      format,
      data_type: 'customer',
      search_params: queryForm,
      filename: `客户订单_${queryForm.customerName}_${Date.now()}.${format === 'excel' ? 'xlsx' : 'csv'}`
    };
    
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    message.success('导出成功');
    exportModalVisible.value = false;
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

const viewOrderDetail = (record: any) => {
  selectedOrderId.value = record.订单编号;
  orderModalVisible.value = true;
};

const getInstallmentInfo = (record: any) => {
  const installmentInfo: Record<string, string> = {};
  
  Object.keys(record).forEach(key => {
    if (key.startsWith('期数') && key !== '期数') {
      installmentInfo[key] = record[key];
    }
  });
  
  return installmentInfo;
};
</script>

<style lang="scss" scoped>
.customer-query-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  margin-bottom: $space-6;
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.query-form-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  margin-bottom: $space-6;
  
  .query-form {
    :deep(.ant-form-item) {
      margin-bottom: $space-4;
      
      @include responsive('md') {
        margin-right: 0;
        margin-bottom: $space-3;
      }
    }
    
    @include responsive('md') {
      .ant-form-item {
        display: block;
        width: 100%;
      }
    }
  }
}

.customer-info-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  margin-bottom: $space-6;
  
  .customer-header {
    margin-bottom: $space-6;
    
    .customer-basic {
      @include flex-center-vertical;
      gap: $space-4;
      margin-bottom: $space-6;
      
      .customer-details {
        h3 {
          font-size: $text-xl;
          font-weight: $font-semibold;
          color: $text-primary;
          margin: 0 0 $space-1 0;
        }
        
        p {
          color: $text-secondary;
          margin: 0;
        }
      }
    }
  }
  
  .customer-chart {
    border-top: 1px solid $border-light;
    padding-top: $space-6;
  }
}

.orders-table-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
  
  .table-header {
    @include flex-between;
    padding: $space-6 $space-6 0;
    
    h3 {
      font-size: $text-xl;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0;
    }
  }
}

.expanded-detail {
  padding: $space-4;
  background: $bg-secondary;
  @include border-radius();
  margin: $space-2;
}
</style>
