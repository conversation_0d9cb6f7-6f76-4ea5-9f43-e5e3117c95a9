<!-- 订单指标卡片组件 -->
<template>
  <div class="order-metric-card" :class="[`order-metric-card--${color}`, { 'order-metric-card--loading': loading }]">
    <div class="metric-card__content">
      <div class="metric-card__header">
        <div class="metric-card__icon">
          <component :is="iconComponent" />
        </div>
        <div class="metric-card__trend" v-if="!loading && trend !== undefined">
          <span class="trend-value" :class="trendClass">
            <ArrowUpOutlined v-if="trend > 0" />
            <ArrowDownOutlined v-if="trend < 0" />
            <MinusOutlined v-if="trend === 0" />
            {{ Math.abs(trend) }}%
          </span>
        </div>
      </div>
      
      <div class="metric-card__body">
        <div class="metric-card__title">{{ title }}</div>
        <div class="metric-card__value">
          <a-skeleton-input v-if="loading" active size="large" />
          <span v-else>{{ displayValue }}</span>
        </div>
      </div>
    </div>
    
    <div class="metric-card__footer" v-if="!loading">
      <span class="footer-text">
        较上期
        <span :class="trendClass">
          {{ getTrendText(trend) }}
        </span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ShoppingCartOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CheckSquareOutlined,
  ClockCircleOutlined,
  PercentageOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue';

interface Props {
  title: string;
  value: string | number;
  trend?: number;
  icon: string;
  color: 'blue' | 'green' | 'red' | 'purple' | 'orange' | 'cyan';
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 图标映射
const iconMap = {
  ShoppingCartOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CheckSquareOutlined,
  ClockCircleOutlined,
  PercentageOutlined
};

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || ShoppingCartOutlined;
});

const displayValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString();
  }
  return props.value;
});

const trendClass = computed(() => {
  if (props.trend === undefined) return '';
  
  if (props.trend > 0) {
    return 'trend-up';
  } else if (props.trend < 0) {
    return 'trend-down';
  } else {
    return 'trend-neutral';
  }
});

// 方法
const getTrendText = (trend?: number) => {
  if (trend === undefined) return '';
  
  if (trend > 0) {
    return '上升';
  } else if (trend < 0) {
    return '下降';
  } else {
    return '持平';
  }
};
</script>

<style lang="scss" scoped>
.order-metric-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-5;
  @include transition(all);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: currentColor;
  }
  
  &:hover {
    @include shadow('md');
    transform: translateY(-1px);
  }
  
  &--loading {
    &:hover {
      transform: none;
    }
  }
  
  // 颜色主题
  &--blue {
    color: $primary-500;
    
    .metric-card__icon {
      background: rgba($primary-500, 0.1);
      color: $primary-500;
    }
  }
  
  &--green {
    color: $success-500;
    
    .metric-card__icon {
      background: rgba($success-500, 0.1);
      color: $success-500;
    }
  }
  
  &--red {
    color: $error-500;
    
    .metric-card__icon {
      background: rgba($error-500, 0.1);
      color: $error-500;
    }
  }
  
  &--purple {
    color: #722ed1;
    
    .metric-card__icon {
      background: rgba(#722ed1, 0.1);
      color: #722ed1;
    }
  }
  
  &--orange {
    color: $warning-500;
    
    .metric-card__icon {
      background: rgba($warning-500, 0.1);
      color: $warning-500;
    }
  }
  
  &--cyan {
    color: #13c2c2;
    
    .metric-card__icon {
      background: rgba(#13c2c2, 0.1);
      color: #13c2c2;
    }
  }
}

.metric-card__content {
  margin-bottom: $space-3;
}

.metric-card__header {
  @include flex-between;
  margin-bottom: $space-3;
}

.metric-card__icon {
  width: 40px;
  height: 40px;
  @include border-radius('lg');
  @include flex-center;
  font-size: 20px;
}

.metric-card__trend {
  .trend-value {
    @include flex-center-vertical;
    gap: $space-1;
    font-size: $text-xs;
    font-weight: $font-medium;
    padding: $space-1 $space-2;
    @include border-radius('sm');
    
    &.trend-up {
      color: $success-600;
      background: rgba($success-500, 0.1);
    }
    
    &.trend-down {
      color: $error-600;
      background: rgba($error-500, 0.1);
    }
    
    &.trend-neutral {
      color: $text-secondary;
      background: $bg-secondary;
    }
  }
}

.metric-card__body {
  .metric-card__title {
    font-size: $text-xs;
    color: $text-secondary;
    margin-bottom: $space-2;
    font-weight: $font-medium;
  }
  
  .metric-card__value {
    font-size: $text-2xl;
    font-weight: $font-bold;
    color: $text-primary;
    line-height: 1;
  }
}

.metric-card__footer {
  border-top: 1px solid $border-light;
  padding-top: $space-2;
  
  .footer-text {
    font-size: $text-xs;
    color: $text-tertiary;
    
    .trend-up {
      color: $success-600;
    }
    
    .trend-down {
      color: $error-600;
    }
    
    .trend-neutral {
      color: $text-secondary;
    }
  }
}

// 响应式适配
@include responsive('md') {
  .order-metric-card {
    padding: $space-4;
    
    .metric-card__icon {
      width: 36px;
      height: 36px;
      font-size: 18px;
    }
    
    .metric-card__value {
      font-size: $text-xl;
    }
  }
}
</style>
