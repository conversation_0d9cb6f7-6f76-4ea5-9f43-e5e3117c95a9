<!-- 订单汇总页面 -->
<template>
  <div class="order-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">订单汇总分析</h1>
        <p class="page-description">订单数据深度分析，掌握业务运营状况</p>
      </div>
      
      <div class="header-actions">
        <a-space>
          <a-date-picker
            v-model:value="endDate"
            format="YYYY-MM-DD"
            placeholder="截止日期"
            @change="handleDateChange"
          />
          
          <a-select
            v-model:value="analysisType"
            style="width: 120px"
            @change="handleAnalysisTypeChange"
          >
            <a-select-option value="overview">总览</a-select-option>
            <a-select-option value="trend">趋势</a-select-option>
            <a-select-option value="comparison">对比</a-select-option>
            <a-select-option value="forecast">预测</a-select-option>
          </a-select>
          
          <a-button @click="handleRefresh" :loading="loading">
            <ReloadOutlined />
            刷新数据
          </a-button>
          
          <a-button
            v-if="userStore.canExportData"
            @click="handleExport"
            :loading="exportLoading"
          >
            <DownloadOutlined />
            导出分析
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 订单概览指标 -->
    <div class="overview-metrics">
      <a-row :gutter="16">
        <a-col :span="4">
          <OrderMetricCard
            title="总订单数"
            :value="orderMetrics.totalOrders"
            :trend="orderMetrics.ordersTrend"
            icon="ShoppingCartOutlined"
            color="blue"
            :loading="loading"
          />
        </a-col>
        <a-col :span="4">
          <OrderMetricCard
            title="有效订单"
            :value="orderMetrics.validOrders"
            :trend="orderMetrics.validTrend"
            icon="CheckCircleOutlined"
            color="green"
            :loading="loading"
          />
        </a-col>
        <a-col :span="4">
          <OrderMetricCard
            title="逾期订单"
            :value="orderMetrics.overdueOrders"
            :trend="orderMetrics.overdueTrend"
            icon="ExclamationCircleOutlined"
            color="red"
            :loading="loading"
          />
        </a-col>
        <a-col :span="4">
          <OrderMetricCard
            title="已结清订单"
            :value="orderMetrics.completedOrders"
            :trend="orderMetrics.completedTrend"
            icon="CheckSquareOutlined"
            color="purple"
            :loading="loading"
          />
        </a-col>
        <a-col :span="4">
          <OrderMetricCard
            title="平均订单周期"
            :value="`${orderMetrics.avgCycle}天`"
            :trend="orderMetrics.cycleTrend"
            icon="ClockCircleOutlined"
            color="orange"
            :loading="loading"
          />
        </a-col>
        <a-col :span="4">
          <OrderMetricCard
            title="订单完成率"
            :value="`${orderMetrics.completionRate}%`"
            :trend="orderMetrics.completionTrend"
            icon="PercentageOutlined"
            color="cyan"
            :loading="loading"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 订单状态分布 -->
    <div class="status-distribution">
      <div class="distribution-card">
        <div class="card-header">
          <h3>订单状态分布</h3>
          <a-radio-group v-model:value="statusViewType" size="small" @change="updateStatusChart">
            <a-radio-button value="count">按数量</a-radio-button>
            <a-radio-button value="amount">按金额</a-radio-button>
            <a-radio-button value="percentage">按比例</a-radio-button>
          </a-radio-group>
        </div>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <ChartComponent
              type="pie"
              :data="statusDistributionData"
              :loading="loading"
              height="350px"
              @refresh="loadStatusData"
            />
          </a-col>
          <a-col :span="12">
            <div class="status-details">
              <div
                v-for="(status, index) in statusDetails"
                :key="status.name"
                class="status-item"
              >
                <div class="status-indicator" :style="{ backgroundColor: status.color }"></div>
                <div class="status-info">
                  <div class="status-name">{{ status.name }}</div>
                  <div class="status-value">{{ status.value }}</div>
                  <div class="status-change" :class="getChangeClass(status.change)">
                    {{ status.change > 0 ? '+' : '' }}{{ status.change }}%
                  </div>
                </div>
                <div class="status-percentage">{{ status.percentage }}%</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 订单趋势分析 -->
    <div class="trend-analysis">
      <div class="trend-card">
        <div class="card-header">
          <h3>订单趋势分析</h3>
          <a-space>
            <a-radio-group v-model:value="trendPeriod" size="small" @change="updateTrendChart">
              <a-radio-button value="7d">近7天</a-radio-button>
              <a-radio-button value="30d">近30天</a-radio-button>
              <a-radio-button value="90d">近90天</a-radio-button>
              <a-radio-button value="1y">近1年</a-radio-button>
            </a-radio-group>
            
            <a-select
              v-model:value="trendMetric"
              style="width: 120px"
              size="small"
              @change="updateTrendChart"
            >
              <a-select-option value="orders">订单数</a-select-option>
              <a-select-option value="amount">订单金额</a-select-option>
              <a-select-option value="customers">客户数</a-select-option>
            </a-select>
          </a-space>
        </div>
        
        <ChartComponent
          type="line"
          :data="trendAnalysisData"
          :loading="loading"
          height="400px"
          @refresh="loadTrendData"
        />
      </div>
    </div>

    <!-- 业务维度分析 -->
    <div class="dimension-analysis">
      <a-row :gutter="16">
        <!-- 业务类型分析 -->
        <a-col :span="8">
          <div class="dimension-card">
            <div class="card-header">
              <h3>业务类型分析</h3>
            </div>
            
            <ChartComponent
              type="bar"
              :data="businessTypeData"
              :loading="loading"
              height="300px"
              @refresh="loadBusinessTypeData"
            />
            
            <div class="dimension-summary">
              <div class="summary-item">
                <span class="label">主要业务:</span>
                <span class="value">{{ mainBusinessType }}</span>
              </div>
              <div class="summary-item">
                <span class="label">增长最快:</span>
                <span class="value">{{ fastestGrowingBusiness }}</span>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 产品类型分析 -->
        <a-col :span="8">
          <div class="dimension-card">
            <div class="card-header">
              <h3>产品类型分析</h3>
            </div>
            
            <ChartComponent
              type="bar"
              :data="productTypeData"
              :loading="loading"
              height="300px"
              @refresh="loadProductTypeData"
            />
            
            <div class="dimension-summary">
              <div class="summary-item">
                <span class="label">热门产品:</span>
                <span class="value">{{ topProduct }}</span>
              </div>
              <div class="summary-item">
                <span class="label">市场份额:</span>
                <span class="value">{{ topProductShare }}%</span>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 客服分析 -->
        <a-col :span="8">
          <div class="dimension-card">
            <div class="card-header">
              <h3>客服业绩分析</h3>
            </div>
            
            <ChartComponent
              type="bar"
              :data="servicePerformanceData"
              :loading="loading"
              height="300px"
              @refresh="loadServiceData"
            />
            
            <div class="dimension-summary">
              <div class="summary-item">
                <span class="label">最佳客服:</span>
                <span class="value">{{ topService }}</span>
              </div>
              <div class="summary-item">
                <span class="label">处理订单:</span>
                <span class="value">{{ topServiceOrders }}单</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 订单质量分析 -->
    <div class="quality-analysis">
      <div class="quality-card">
        <div class="card-header">
          <h3>订单质量分析</h3>
          <a-space>
            <a-select
              v-model:value="qualityDimension"
              style="width: 120px"
              size="small"
              @change="updateQualityChart"
            >
              <a-select-option value="overdue">逾期分析</a-select-option>
              <a-select-option value="amount">金额分布</a-select-option>
              <a-select-option value="cycle">周期分析</a-select-option>
            </a-select>
          </a-space>
        </div>
        
        <a-row :gutter="16">
          <a-col :span="16">
            <ChartComponent
              :type="qualityChartType"
              :data="qualityAnalysisData"
              :loading="loading"
              height="350px"
              @refresh="loadQualityData"
            />
          </a-col>
          <a-col :span="8">
            <div class="quality-insights">
              <h4>质量洞察</h4>
              <div class="insight-list">
                <div
                  v-for="insight in qualityInsights"
                  :key="insight.title"
                  class="insight-item"
                >
                  <div class="insight-icon" :class="insight.type">
                    <component :is="insight.icon" />
                  </div>
                  <div class="insight-content">
                    <div class="insight-title">{{ insight.title }}</div>
                    <div class="insight-description">{{ insight.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 订单详细列表 -->
    <div class="order-details">
      <div class="details-header">
        <h3>订单详细数据</h3>
        <a-space>
          <a-select
            v-model:value="detailsGroupBy"
            placeholder="分组方式"
            style="width: 120px"
            @change="handleDetailsGroupChange"
          >
            <a-select-option value="date">按日期</a-select-option>
            <a-select-option value="status">按状态</a-select-option>
            <a-select-option value="business">按业务</a-select-option>
            <a-select-option value="service">按客服</a-select-option>
          </a-select>
          
          <a-input
            v-model:value="detailsSearch"
            placeholder="搜索订单"
            style="width: 200px"
            @keyup.enter="handleDetailsSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-space>
      </div>

      <DataTable
        :columns="detailsTableColumns"
        :data-source="filteredDetailsData"
        :loading="loading"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['20', '50', '100', '200'],
          defaultPageSize: 50
        }"
        @refresh="handleRefresh"
        @export="handleTableExport"
      >
        <!-- 自定义单元格渲染 -->
        <template #cell="{ column, record, value }">
          <!-- 订单编号列 -->
          <template v-if="column.dataIndex === 'orderNumber'">
            <a-button
              type="link"
              size="small"
              @click="viewOrderDetail(record)"
            >
              {{ value }}
            </a-button>
          </template>
          
          <!-- 客户姓名列 -->
          <template v-else-if="column.dataIndex === 'customerName'">
            <a-button
              type="link"
              size="small"
              @click="viewCustomerDetail(record)"
            >
              {{ value }}
            </a-button>
          </template>
          
          <!-- 金额列 -->
          <template v-else-if="column.dataIndex.includes('amount') || column.dataIndex.includes('Amount')">
            <AmountCell :amount="value" />
          </template>
          
          <!-- 日期列 -->
          <template v-else-if="column.dataIndex.includes('date') || column.dataIndex.includes('Date')">
            <DateCell :date="value" />
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.dataIndex === 'status'">
            <StatusTag :status="value" />
          </template>
          
          <!-- 逾期天数列 -->
          <template v-else-if="column.dataIndex === 'overdueDays'">
            <a-tag v-if="value > 0" color="red">{{ value }}天</a-tag>
            <span v-else>-</span>
          </template>
          
          <!-- 默认渲染 -->
          <template v-else>
            {{ value }}
          </template>
        </template>
      </DataTable>
    </div>

    <!-- 订单详情模态框 -->
    <OrderDetailModal
      v-model:visible="orderModalVisible"
      :order-id="selectedOrderId"
      @close="orderModalVisible = false"
    />

    <!-- 客户详情模态框 -->
    <CustomerDetailModal
      v-model:visible="customerModalVisible"
      :customer-name="selectedCustomer"
      @close="customerModalVisible = false"
    />

    <!-- 导出模态框 -->
    <ExportModal
      v-model:visible="exportModalVisible"
      :data="filteredDetailsData"
      :filename="`订单汇总分析_${analysisType}_${Date.now()}`"
      @export="handleConfirmExport"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ReloadOutlined,
  DownloadOutlined,
  SearchOutlined,
  TrendingUpOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';
import { useDataStore } from '@/stores/data';
import { useUserStore } from '@/stores/user';
import OrderMetricCard from './components/OrderMetricCard.vue';
import ChartComponent from '@/components/Charts/ChartComponent.vue';
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import StatusTag from '@/components/DataTable/StatusTag.vue';
import OrderDetailModal from '@/views/DataQuery/components/OrderDetailModal.vue';
import CustomerDetailModal from '@/views/CustomerQuery/components/CustomerDetailModal.vue';
import ExportModal from '@/components/common/ExportModal.vue';
import { message } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';

const dataStore = useDataStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const endDate = ref(dayjs());
const analysisType = ref('overview');
const statusViewType = ref('count');
const trendPeriod = ref('30d');
const trendMetric = ref('orders');
const qualityDimension = ref('overdue');
const detailsGroupBy = ref('date');
const detailsSearch = ref('');

const orderModalVisible = ref(false);
const customerModalVisible = ref(false);
const exportModalVisible = ref(false);
const selectedOrderId = ref('');
const selectedCustomer = ref('');

// 订单指标数据
const orderMetrics = reactive({
  totalOrders: 0,
  validOrders: 0,
  overdueOrders: 0,
  completedOrders: 0,
  avgCycle: 0,
  completionRate: 0,
  ordersTrend: 0,
  validTrend: 0,
  overdueTrend: 0,
  completedTrend: 0,
  cycleTrend: 0,
  completionTrend: 0
});

// 图表数据
const statusDistributionData = ref({ labels: [], datasets: [] });
const trendAnalysisData = ref({ labels: [], datasets: [] });
const businessTypeData = ref({ labels: [], datasets: [] });
const productTypeData = ref({ labels: [], datasets: [] });
const servicePerformanceData = ref({ labels: [], datasets: [] });
const qualityAnalysisData = ref({ labels: [], datasets: [] });

// 状态详情
const statusDetails = ref([]);

// 汇总信息
const mainBusinessType = ref('');
const fastestGrowingBusiness = ref('');
const topProduct = ref('');
const topProductShare = ref(0);
const topService = ref('');
const topServiceOrders = ref(0);

// 质量洞察
const qualityInsights = ref([]);

// 详细数据
const detailsData = ref([]);

// 计算属性
const qualityChartType = computed(() => {
  switch (qualityDimension.value) {
    case 'overdue':
      return 'bar';
    case 'amount':
      return 'pie';
    case 'cycle':
      return 'line';
    default:
      return 'bar';
  }
});

const detailsTableColumns = computed(() => [
  {
    title: '订单编号',
    dataIndex: 'orderNumber',
    key: 'orderNumber',
    width: 150,
    fixed: 'left'
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120
  },
  {
    title: '订单日期',
    dataIndex: 'orderDate',
    key: 'orderDate',
    width: 120,
    sorter: true
  },
  {
    title: '订单金额',
    dataIndex: 'orderAmount',
    key: 'orderAmount',
    width: 120,
    sorter: true
  },
  {
    title: '当前待收',
    dataIndex: 'currentAmount',
    key: 'currentAmount',
    width: 120,
    sorter: true
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    key: 'businessType',
    width: 100
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    key: 'productType',
    width: 120
  },
  {
    title: '客服',
    dataIndex: 'service',
    key: 'service',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDays',
    key: 'overdueDays',
    width: 100,
    sorter: true
  }
]);

const filteredDetailsData = computed(() => {
  let data = detailsData.value;
  
  if (detailsSearch.value) {
    const keyword = detailsSearch.value.toLowerCase();
    data = data.filter((item: any) => 
      Object.values(item).some(value => 
        String(value).toLowerCase().includes(keyword)
      )
    );
  }
  
  return data;
});

// 方法
const loadOrderMetrics = async () => {
  loading.value = true;
  try {
    // 模拟加载订单指标数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Object.assign(orderMetrics, {
      totalOrders: 15680,
      validOrders: 14250,
      overdueOrders: 890,
      completedOrders: 12800,
      avgCycle: 45,
      completionRate: 89.8,
      ordersTrend: 12.5,
      validTrend: 8.3,
      overdueTrend: -15.2,
      completedTrend: 18.7,
      cycleTrend: -5.6,
      completionTrend: 3.2
    });
  } catch (error) {
    console.error('加载订单指标失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const loadChartData = async () => {
  // 状态分布数据
  statusDistributionData.value = {
    labels: ['正常', '逾期', '结清', '其他'],
    datasets: [{
      label: '订单状态分布',
      data: [8500, 890, 5200, 1090],
      backgroundColor: ['#52c41a', '#ff4d4f', '#1890ff', '#faad14']
    }]
  };

  statusDetails.value = [
    { name: '正常', value: 8500, percentage: 54.2, change: 8.5, color: '#52c41a' },
    { name: '逾期', value: 890, percentage: 5.7, change: -12.3, color: '#ff4d4f' },
    { name: '结清', value: 5200, percentage: 33.2, change: 15.6, color: '#1890ff' },
    { name: '其他', value: 1090, percentage: 6.9, change: 2.1, color: '#faad14' }
  ];

  // 趋势分析数据
  trendAnalysisData.value = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '订单数',
      data: [1200, 1350, 1180, 1420, 1380, 1580],
      borderColor: '#1890ff',
      backgroundColor: 'rgba(24, 144, 255, 0.1)',
      fill: true
    }]
  };

  // 业务类型数据
  businessTypeData.value = {
    labels: ['电商', '租赁', '其他'],
    datasets: [{
      label: '业务类型',
      data: [9800, 4200, 1680],
      backgroundColor: '#1890ff'
    }]
  };

  // 产品类型数据
  productTypeData.value = {
    labels: ['分期产品', '租赁产品', '信贷产品'],
    datasets: [{
      label: '产品类型',
      data: [7200, 5800, 2680],
      backgroundColor: '#52c41a'
    }]
  };

  // 客服业绩数据
  servicePerformanceData.value = {
    labels: ['客服A', '客服B', '客服C', '客服D'],
    datasets: [{
      label: '处理订单数',
      data: [3200, 2800, 2400, 2000],
      backgroundColor: '#722ed1'
    }]
  };

  // 质量分析数据
  qualityAnalysisData.value = {
    labels: ['1-7天', '8-15天', '16-30天', '30天以上'],
    datasets: [{
      label: '逾期订单分布',
      data: [320, 280, 180, 110],
      backgroundColor: '#ff4d4f'
    }]
  };

  // 更新汇总信息
  mainBusinessType.value = '电商';
  fastestGrowingBusiness.value = '租赁';
  topProduct.value = '分期产品';
  topProductShare.value = 45.9;
  topService.value = '客服A';
  topServiceOrders.value = 3200;

  // 质量洞察
  qualityInsights.value = [
    {
      title: '逾期率下降',
      description: '本月逾期率较上月下降15.2%',
      type: 'success',
      icon: TrendingUpOutlined
    },
    {
      title: '长期逾期增加',
      description: '30天以上逾期订单增加8.5%',
      type: 'warning',
      icon: WarningOutlined
    },
    {
      title: '平均周期缩短',
      description: '订单平均处理周期缩短5.6天',
      type: 'info',
      icon: InfoCircleOutlined
    }
  ];
};

const loadDetailsData = async () => {
  // 模拟详细数据
  detailsData.value = [
    {
      orderNumber: 'ORD20240115001',
      customerName: '张三',
      orderDate: '2024-01-15',
      orderAmount: 50000,
      currentAmount: 35000,
      businessType: '电商',
      productType: '分期产品',
      service: '客服A',
      status: '正常',
      overdueDays: 0
    },
    {
      orderNumber: 'ORD20240114002',
      customerName: '李四',
      orderDate: '2024-01-14',
      orderAmount: 80000,
      currentAmount: 25000,
      businessType: '租赁',
      productType: '租赁产品',
      service: '客服B',
      status: '逾期',
      overdueDays: 15
    }
    // ... 更多数据
  ];
};

const handleDateChange = (date: Dayjs | null) => {
  if (date) {
    endDate.value = date;
    loadAllData();
  }
};

const handleAnalysisTypeChange = () => {
  loadAllData();
};

const handleRefresh = () => {
  loadAllData();
};

const loadAllData = async () => {
  await Promise.all([
    loadOrderMetrics(),
    loadChartData(),
    loadDetailsData()
  ]);
};

// 图表更新方法
const updateStatusChart = () => {
  loadChartData();
};

const updateTrendChart = () => {
  loadChartData();
};

const updateQualityChart = () => {
  loadChartData();
};

// 详情相关方法
const handleDetailsGroupChange = () => {
  loadDetailsData();
};

const handleDetailsSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const getChangeClass = (change: number) => {
  if (change > 0) return 'change-up';
  if (change < 0) return 'change-down';
  return 'change-neutral';
};

const viewOrderDetail = (record: any) => {
  selectedOrderId.value = record.orderNumber;
  orderModalVisible.value = true;
};

const viewCustomerDetail = (record: any) => {
  selectedCustomer.value = record.customerName;
  customerModalVisible.value = true;
};

// 导出相关
const handleExport = () => {
  exportModalVisible.value = true;
};

const handleTableExport = (format: 'excel' | 'csv') => {
  handleConfirmExport(format);
};

const handleConfirmExport = async (format: 'excel' | 'csv') => {
  exportLoading.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    message.success('导出成功');
    exportModalVisible.value = false;
  } catch (error) {
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadAllData();
});
</script>

<style lang="scss" scoped>
.order-summary-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  @include flex-between;
  margin-bottom: $space-6;
  
  @include responsive('md') {
    flex-direction: column;
    gap: $space-4;
    align-items: flex-start;
  }
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.overview-metrics {
  margin-bottom: $space-8;
}

.status-distribution {
  margin-bottom: $space-8;
  
  .distribution-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .status-details {
      .status-item {
        @include flex-between;
        padding: $space-3;
        @include border-radius();
        @include transition(background-color);
        margin-bottom: $space-2;
        
        &:hover {
          background: $bg-secondary;
        }
        
        .status-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          flex-shrink: 0;
        }
        
        .status-info {
          flex: 1;
          margin-left: $space-3;
          
          .status-name {
            font-weight: $font-medium;
            color: $text-primary;
            margin-bottom: $space-1;
          }
          
          .status-value {
            font-size: $text-sm;
            color: $text-secondary;
          }
          
          .status-change {
            font-size: $text-xs;
            
            &.change-up {
              color: $success-600;
            }
            
            &.change-down {
              color: $error-600;
            }
            
            &.change-neutral {
              color: $text-tertiary;
            }
          }
        }
        
        .status-percentage {
          font-weight: $font-medium;
          color: $text-primary;
        }
      }
    }
  }
}

.trend-analysis {
  margin-bottom: $space-8;
  
  .trend-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      @include responsive('md') {
        flex-direction: column;
        gap: $space-3;
        align-items: flex-start;
      }
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
  }
}

.dimension-analysis {
  margin-bottom: $space-8;
  
  .dimension-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    height: 100%;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .dimension-summary {
      margin-top: $space-4;
      padding-top: $space-4;
      border-top: 1px solid $border-light;
      
      .summary-item {
        @include flex-between;
        margin-bottom: $space-2;
        
        .label {
          color: $text-secondary;
          font-size: $text-sm;
        }
        
        .value {
          color: $text-primary;
          font-weight: $font-medium;
        }
      }
    }
  }
}

.quality-analysis {
  margin-bottom: $space-8;
  
  .quality-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .quality-insights {
      h4 {
        font-size: $text-base;
        font-weight: $font-semibold;
        color: $text-primary;
        margin-bottom: $space-4;
      }
      
      .insight-list {
        .insight-item {
          @include flex-center-vertical;
          gap: $space-3;
          padding: $space-3;
          @include border-radius();
          @include transition(background-color);
          margin-bottom: $space-3;
          
          &:hover {
            background: $bg-secondary;
          }
          
          .insight-icon {
            width: 32px;
            height: 32px;
            @include border-radius('lg');
            @include flex-center;
            font-size: 16px;
            
            &.success {
              background: rgba($success-500, 0.1);
              color: $success-500;
            }
            
            &.warning {
              background: rgba($warning-500, 0.1);
              color: $warning-500;
            }
            
            &.info {
              background: rgba($primary-500, 0.1);
              color: $primary-500;
            }
          }
          
          .insight-content {
            flex: 1;
            
            .insight-title {
              font-weight: $font-medium;
              color: $text-primary;
              margin-bottom: $space-1;
            }
            
            .insight-description {
              font-size: $text-sm;
              color: $text-secondary;
            }
          }
        }
      }
    }
  }
}

.order-details {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
  
  .details-header {
    @include flex-between;
    padding: $space-6 $space-6 0;
    
    h3 {
      font-size: $text-xl;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0;
    }
  }
}
</style>
