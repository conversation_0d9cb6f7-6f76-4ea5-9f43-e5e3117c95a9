<!-- 数据汇总页面 -->
<template>
  <div class="data-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">数据汇总分析</h1>
        <p class="page-description">全面的业务数据统计分析，洞察业务发展趋势</p>
      </div>
      
      <div class="header-actions">
        <a-space>
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
          
          <a-select
            v-model:value="summaryType"
            style="width: 120px"
            @change="handleSummaryTypeChange"
          >
            <a-select-option value="daily">日汇总</a-select-option>
            <a-select-option value="weekly">周汇总</a-select-option>
            <a-select-option value="monthly">月汇总</a-select-option>
            <a-select-option value="quarterly">季度汇总</a-select-option>
          </a-select>
          
          <a-button @click="handleRefresh" :loading="loading">
            <ReloadOutlined />
            刷新数据
          </a-button>
          
          <a-button
            v-if="userStore.canExportData"
            @click="handleExport"
            :loading="exportLoading"
          >
            <DownloadOutlined />
            导出报告
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <MetricCard
            title="总订单数"
            :value="metricsData.totalOrders"
            :trend="metricsData.ordersTrend"
            :target="metricsData.ordersTarget"
            icon="ShoppingCartOutlined"
            color="blue"
            :loading="loading"
          />
        </a-col>
        <a-col :span="6">
          <MetricCard
            title="总交易额"
            :value="formatCurrency(metricsData.totalAmount)"
            :trend="metricsData.amountTrend"
            :target="metricsData.amountTarget"
            icon="DollarOutlined"
            color="green"
            :loading="loading"
          />
        </a-col>
        <a-col :span="6">
          <MetricCard
            title="平均订单金额"
            :value="formatCurrency(metricsData.avgOrderAmount)"
            :trend="metricsData.avgAmountTrend"
            :target="metricsData.avgAmountTarget"
            icon="CalculatorOutlined"
            color="purple"
            :loading="loading"
          />
        </a-col>
        <a-col :span="6">
          <MetricCard
            title="逾期率"
            :value="`${metricsData.overdueRate}%`"
            :trend="metricsData.overdueRateTrend"
            :target="metricsData.overdueRateTarget"
            icon="ExclamationCircleOutlined"
            color="red"
            :loading="loading"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 多维度分析 -->
    <div class="analysis-section">
      <a-row :gutter="16">
        <!-- 业务分布分析 -->
        <a-col :span="8">
          <div class="analysis-card">
            <div class="card-header">
              <h3>业务分布分析</h3>
              <a-select
                v-model:value="businessMetric"
                style="width: 100px"
                size="small"
                @change="updateBusinessChart"
              >
                <a-select-option value="count">订单数</a-select-option>
                <a-select-option value="amount">金额</a-select-option>
              </a-select>
            </div>
            
            <ChartComponent
              type="pie"
              :data="businessDistributionData"
              :loading="loading"
              height="300px"
              @refresh="loadBusinessData"
            />
            
            <div class="chart-summary">
              <div class="summary-item">
                <span class="label">主要业务:</span>
                <span class="value">{{ mainBusiness }}</span>
              </div>
              <div class="summary-item">
                <span class="label">占比:</span>
                <span class="value">{{ mainBusinessRatio }}%</span>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 产品分析 -->
        <a-col :span="8">
          <div class="analysis-card">
            <div class="card-header">
              <h3>产品分析</h3>
              <a-select
                v-model:value="productMetric"
                style="width: 100px"
                size="small"
                @change="updateProductChart"
              >
                <a-select-option value="count">订单数</a-select-option>
                <a-select-option value="amount">金额</a-select-option>
                <a-select-option value="profit">利润</a-select-option>
              </a-select>
            </div>
            
            <ChartComponent
              type="bar"
              :data="productAnalysisData"
              :loading="loading"
              height="300px"
              @refresh="loadProductData"
            />
            
            <div class="chart-summary">
              <div class="summary-item">
                <span class="label">热门产品:</span>
                <span class="value">{{ topProduct }}</span>
              </div>
              <div class="summary-item">
                <span class="label">增长率:</span>
                <span class="value">{{ productGrowthRate }}%</span>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 地区分析 -->
        <a-col :span="8">
          <div class="analysis-card">
            <div class="card-header">
              <h3>地区分析</h3>
              <a-select
                v-model:value="regionMetric"
                style="width: 100px"
                size="small"
                @change="updateRegionChart"
              >
                <a-select-option value="count">订单数</a-select-option>
                <a-select-option value="amount">金额</a-select-option>
                <a-select-option value="customers">客户数</a-select-option>
              </a-select>
            </div>
            
            <ChartComponent
              type="bar"
              :data="regionAnalysisData"
              :loading="loading"
              height="300px"
              @refresh="loadRegionData"
            />
            
            <div class="chart-summary">
              <div class="summary-item">
                <span class="label">主要地区:</span>
                <span class="value">{{ topRegion }}</span>
              </div>
              <div class="summary-item">
                <span class="label">市场份额:</span>
                <span class="value">{{ regionMarketShare }}%</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 趋势分析 -->
    <div class="trend-section">
      <div class="trend-card">
        <div class="card-header">
          <h3>业务趋势分析</h3>
          <a-space>
            <a-radio-group v-model:value="trendPeriod" size="small" @change="updateTrendChart">
              <a-radio-button value="7d">近7天</a-radio-button>
              <a-radio-button value="30d">近30天</a-radio-button>
              <a-radio-button value="90d">近90天</a-radio-button>
              <a-radio-button value="1y">近1年</a-radio-button>
            </a-radio-group>
            
            <a-checkbox-group v-model:value="trendMetrics" @change="updateTrendChart">
              <a-checkbox value="orders">订单数</a-checkbox>
              <a-checkbox value="amount">交易额</a-checkbox>
              <a-checkbox value="customers">客户数</a-checkbox>
            </a-checkbox-group>
          </a-space>
        </div>
        
        <ChartComponent
          type="line"
          :data="trendAnalysisData"
          :loading="loading"
          height="400px"
          @refresh="loadTrendData"
        />
      </div>
    </div>

    <!-- 对比分析 -->
    <div class="comparison-section">
      <a-row :gutter="16">
        <!-- 同比分析 -->
        <a-col :span="12">
          <div class="comparison-card">
            <div class="card-header">
              <h3>同比分析</h3>
              <a-select
                v-model:value="comparisonMetric"
                style="width: 120px"
                size="small"
                @change="updateComparisonChart"
              >
                <a-select-option value="orders">订单数</a-select-option>
                <a-select-option value="amount">交易额</a-select-option>
                <a-select-option value="profit">利润</a-select-option>
              </a-select>
            </div>
            
            <ChartComponent
              type="bar"
              :data="yearOverYearData"
              :loading="loading"
              height="300px"
              @refresh="loadComparisonData"
            />
          </div>
        </a-col>

        <!-- 环比分析 -->
        <a-col :span="12">
          <div class="comparison-card">
            <div class="card-header">
              <h3>环比分析</h3>
              <a-select
                v-model:value="sequentialMetric"
                style="width: 120px"
                size="small"
                @change="updateSequentialChart"
              >
                <a-select-option value="orders">订单数</a-select-option>
                <a-select-option value="amount">交易额</a-select-option>
                <a-select-option value="growth">增长率</a-select-option>
              </a-select>
            </div>
            
            <ChartComponent
              type="line"
              :data="sequentialData"
              :loading="loading"
              height="300px"
              @refresh="loadSequentialData"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="detail-table">
      <div class="table-header">
        <h3>详细数据</h3>
        <a-space>
          <a-select
            v-model:value="tableGroupBy"
            placeholder="分组方式"
            style="width: 120px"
            @change="handleGroupByChange"
          >
            <a-select-option value="date">按日期</a-select-option>
            <a-select-option value="business">按业务</a-select-option>
            <a-select-option value="product">按产品</a-select-option>
            <a-select-option value="region">按地区</a-select-option>
          </a-select>
          
          <a-input
            v-model:value="tableSearch"
            placeholder="搜索"
            style="width: 200px"
            @keyup.enter="handleTableSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-space>
      </div>

      <DataTable
        :columns="detailTableColumns"
        :data-source="filteredDetailData"
        :loading="loading"
        :pagination="{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          pageSizeOptions: ['20', '50', '100', '200'],
          defaultPageSize: 50
        }"
        @refresh="handleRefresh"
        @export="handleTableExport"
      >
        <!-- 自定义单元格渲染 -->
        <template #cell="{ column, record, value }">
          <!-- 日期列 -->
          <template v-if="column.dataIndex === 'date'">
            <DateCell :date="value" />
          </template>
          
          <!-- 金额列 -->
          <template v-else-if="column.dataIndex.includes('amount') || column.dataIndex.includes('Amount')">
            <AmountCell :amount="value" />
          </template>
          
          <!-- 百分比列 -->
          <template v-else-if="column.dataIndex.includes('rate') || column.dataIndex.includes('Rate')">
            <span :class="getPercentageClass(value)">{{ value }}%</span>
          </template>
          
          <!-- 趋势列 -->
          <template v-else-if="column.dataIndex === 'trend'">
            <a-tag :color="value > 0 ? 'green' : value < 0 ? 'red' : 'default'">
              {{ value > 0 ? '+' : '' }}{{ value }}%
            </a-tag>
          </template>
          
          <!-- 默认渲染 -->
          <template v-else>
            {{ value }}
          </template>
        </template>
      </DataTable>
    </div>

    <!-- 导出模态框 -->
    <ExportModal
      v-model:visible="exportModalVisible"
      :data="filteredDetailData"
      :filename="`数据汇总分析_${summaryType}_${Date.now()}`"
      @export="handleConfirmExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ReloadOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { useDataStore } from '@/stores/data';
import { useUserStore } from '@/stores/user';
import MetricCard from './components/MetricCard.vue';
import ChartComponent from '@/components/Charts/ChartComponent.vue';
import DataTable from '@/components/DataTable/DataTable.vue';
import AmountCell from '@/components/DataTable/AmountCell.vue';
import DateCell from '@/components/DataTable/DateCell.vue';
import ExportModal from '@/components/common/ExportModal.vue';
import { message } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';

const dataStore = useDataStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const exportModalVisible = ref(false);

const dateRange = ref<[Dayjs, Dayjs]>([
  dayjs().subtract(1, 'month'),
  dayjs()
]);

const summaryType = ref('daily');
const businessMetric = ref('count');
const productMetric = ref('count');
const regionMetric = ref('count');
const trendPeriod = ref('30d');
const trendMetrics = ref(['orders', 'amount']);
const comparisonMetric = ref('orders');
const sequentialMetric = ref('orders');
const tableGroupBy = ref('date');
const tableSearch = ref('');

// 核心指标数据
const metricsData = reactive({
  totalOrders: 0,
  totalAmount: 0,
  avgOrderAmount: 0,
  overdueRate: 0,
  ordersTrend: 0,
  amountTrend: 0,
  avgAmountTrend: 0,
  overdueRateTrend: 0,
  ordersTarget: 0,
  amountTarget: 0,
  avgAmountTarget: 0,
  overdueRateTarget: 0
});

// 图表数据
const businessDistributionData = ref({ labels: [], datasets: [] });
const productAnalysisData = ref({ labels: [], datasets: [] });
const regionAnalysisData = ref({ labels: [], datasets: [] });
const trendAnalysisData = ref({ labels: [], datasets: [] });
const yearOverYearData = ref({ labels: [], datasets: [] });
const sequentialData = ref({ labels: [], datasets: [] });

// 汇总信息
const mainBusiness = ref('');
const mainBusinessRatio = ref(0);
const topProduct = ref('');
const productGrowthRate = ref(0);
const topRegion = ref('');
const regionMarketShare = ref(0);

// 详细数据
const detailData = ref([]);

// 表格列配置
const detailTableColumns = computed(() => {
  const baseColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      sorter: true
    }
  ];

  // 根据分组方式动态添加列
  switch (tableGroupBy.value) {
    case 'business':
      baseColumns.push({
        title: '业务类型',
        dataIndex: 'business',
        key: 'business',
        width: 120
      });
      break;
    case 'product':
      baseColumns.push({
        title: '产品类型',
        dataIndex: 'product',
        key: 'product',
        width: 120
      });
      break;
    case 'region':
      baseColumns.push({
        title: '地区',
        dataIndex: 'region',
        key: 'region',
        width: 120
      });
      break;
  }

  // 添加通用列
  baseColumns.push(
    {
      title: '订单数',
      dataIndex: 'orderCount',
      key: 'orderCount',
      width: 100,
      sorter: true
    },
    {
      title: '交易金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      sorter: true
    },
    {
      title: '平均金额',
      dataIndex: 'avgAmount',
      key: 'avgAmount',
      width: 120,
      sorter: true
    },
    {
      title: '逾期率',
      dataIndex: 'overdueRate',
      key: 'overdueRate',
      width: 100,
      sorter: true
    },
    {
      title: '环比趋势',
      dataIndex: 'trend',
      key: 'trend',
      width: 100
    }
  );

  return baseColumns;
});

// 计算属性
const filteredDetailData = computed(() => {
  let data = detailData.value;
  
  if (tableSearch.value) {
    const keyword = tableSearch.value.toLowerCase();
    data = data.filter((item: any) => 
      Object.values(item).some(value => 
        String(value).toLowerCase().includes(keyword)
      )
    );
  }
  
  return data;
});

// 方法
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount);
};

const loadMetricsData = async () => {
  loading.value = true;
  try {
    // 模拟加载核心指标数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Object.assign(metricsData, {
      totalOrders: 12580,
      totalAmount: 89650000,
      avgOrderAmount: 71250,
      overdueRate: 3.2,
      ordersTrend: 15.6,
      amountTrend: 22.8,
      avgAmountTrend: 6.2,
      overdueRateTrend: -8.5,
      ordersTarget: 15000,
      amountTarget: 100000000,
      avgAmountTarget: 75000,
      overdueRateTarget: 2.5
    });
  } catch (error) {
    console.error('加载指标数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const loadChartData = async () => {
  // 业务分布数据
  businessDistributionData.value = {
    labels: ['电商', '租赁', '其他'],
    datasets: [{
      label: '业务分布',
      data: [65, 28, 7],
      backgroundColor: ['#1890ff', '#52c41a', '#faad14']
    }]
  };

  // 产品分析数据
  productAnalysisData.value = {
    labels: ['分期产品', '租赁产品', '信贷产品', '其他产品'],
    datasets: [{
      label: '产品分析',
      data: [4500, 3200, 2800, 1080],
      backgroundColor: '#1890ff'
    }]
  };

  // 地区分析数据
  regionAnalysisData.value = {
    labels: ['北京', '上海', '广州', '深圳', '杭州'],
    datasets: [{
      label: '地区分析',
      data: [3200, 2800, 2400, 2000, 1600],
      backgroundColor: '#52c41a'
    }]
  };

  // 趋势分析数据
  trendAnalysisData.value = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '订单数',
      data: [1200, 1350, 1180, 1420, 1380, 1580],
      borderColor: '#1890ff',
      backgroundColor: 'rgba(24, 144, 255, 0.1)',
      fill: true
    }, {
      label: '交易额',
      data: [8500000, 9200000, 8800000, 10200000, 9800000, 11200000],
      borderColor: '#52c41a',
      backgroundColor: 'rgba(82, 196, 26, 0.1)',
      fill: true,
      yAxisID: 'y1'
    }]
  };

  // 同比数据
  yearOverYearData.value = {
    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
    datasets: [{
      label: '今年',
      data: [3500, 4200, 3800, 4500],
      backgroundColor: '#1890ff'
    }, {
      label: '去年',
      data: [3200, 3800, 3600, 4000],
      backgroundColor: '#52c41a'
    }]
  };

  // 环比数据
  sequentialData.value = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '环比增长率',
      data: [0, 12.5, -12.6, 20.3, -2.8, 14.5],
      borderColor: '#faad14',
      backgroundColor: 'rgba(250, 173, 20, 0.1)',
      fill: true
    }]
  };

  // 更新汇总信息
  mainBusiness.value = '电商';
  mainBusinessRatio.value = 65;
  topProduct.value = '分期产品';
  productGrowthRate.value = 18.5;
  topRegion.value = '北京';
  regionMarketShare.value = 28.6;
};

const loadDetailData = async () => {
  // 模拟详细数据
  detailData.value = [
    {
      date: '2024-01-15',
      business: '电商',
      product: '分期产品',
      region: '北京',
      orderCount: 125,
      totalAmount: 8950000,
      avgAmount: 71600,
      overdueRate: 2.8,
      trend: 15.6
    },
    {
      date: '2024-01-14',
      business: '租赁',
      product: '租赁产品',
      region: '上海',
      orderCount: 98,
      totalAmount: 6780000,
      avgAmount: 69184,
      overdueRate: 3.2,
      trend: -2.4
    }
    // ... 更多数据
  ];
};

const handleDateRangeChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    dateRange.value = dates;
    loadAllData();
  }
};

const handleSummaryTypeChange = () => {
  loadAllData();
};

const handleRefresh = () => {
  loadAllData();
};

const loadAllData = async () => {
  await Promise.all([
    loadMetricsData(),
    loadChartData(),
    loadDetailData()
  ]);
};

// 图表更新方法
const updateBusinessChart = () => {
  loadChartData();
};

const updateProductChart = () => {
  loadChartData();
};

const updateRegionChart = () => {
  loadChartData();
};

const updateTrendChart = () => {
  loadChartData();
};

const updateComparisonChart = () => {
  loadChartData();
};

const updateSequentialChart = () => {
  loadChartData();
};

// 表格相关方法
const handleGroupByChange = () => {
  loadDetailData();
};

const handleTableSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const getPercentageClass = (value: number) => {
  if (value > 5) return 'text-success';
  if (value < -5) return 'text-error';
  return 'text-warning';
};

// 导出相关
const handleExport = () => {
  exportModalVisible.value = true;
};

const handleTableExport = (format: 'excel' | 'csv') => {
  handleConfirmExport(format);
};

const handleConfirmExport = async (format: 'excel' | 'csv') => {
  exportLoading.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    message.success('导出成功');
    exportModalVisible.value = false;
  } catch (error) {
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadAllData();
});
</script>

<style lang="scss" scoped>
.data-summary-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.page-header {
  @include flex-between;
  margin-bottom: $space-6;
  
  @include responsive('md') {
    flex-direction: column;
    gap: $space-4;
    align-items: flex-start;
  }
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.metrics-overview {
  margin-bottom: $space-8;
}

.analysis-section {
  margin-bottom: $space-8;
  
  .analysis-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    height: 100%;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
    
    .chart-summary {
      margin-top: $space-4;
      padding-top: $space-4;
      border-top: 1px solid $border-light;
      
      .summary-item {
        @include flex-between;
        margin-bottom: $space-2;
        
        .label {
          color: $text-secondary;
          font-size: $text-sm;
        }
        
        .value {
          color: $text-primary;
          font-weight: $font-medium;
        }
      }
    }
  }
}

.trend-section {
  margin-bottom: $space-8;
  
  .trend-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      @include responsive('md') {
        flex-direction: column;
        gap: $space-3;
        align-items: flex-start;
      }
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
  }
}

.comparison-section {
  margin-bottom: $space-8;
  
  .comparison-card {
    background: $bg-primary;
    @include border-radius('lg');
    @include shadow('sm');
    padding: $space-6;
    height: 100%;
    
    .card-header {
      @include flex-between;
      margin-bottom: $space-4;
      
      h3 {
        font-size: $text-lg;
        font-weight: $font-semibold;
        color: $text-primary;
        margin: 0;
      }
    }
  }
}

.detail-table {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
  
  .table-header {
    @include flex-between;
    padding: $space-6 $space-6 0;
    
    h3 {
      font-size: $text-xl;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0;
    }
  }
}

// 工具类
.text-success {
  color: $success-600;
}

.text-error {
  color: $error-600;
}

.text-warning {
  color: $warning-600;
}
</style>
