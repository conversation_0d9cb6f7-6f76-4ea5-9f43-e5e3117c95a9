<!-- 指标卡片组件 - 带目标对比的增强版 -->
<template>
  <div class="metric-card" :class="[`metric-card--${color}`, { 'metric-card--loading': loading }]">
    <div class="metric-card__content">
      <div class="metric-card__header">
        <div class="metric-card__icon">
          <component :is="iconComponent" />
        </div>
        <div class="metric-card__trend" v-if="!loading && trend !== undefined">
          <span class="trend-value" :class="trendClass">
            <ArrowUpOutlined v-if="trend > 0" />
            <ArrowDownOutlined v-if="trend < 0" />
            {{ Math.abs(trend) }}%
          </span>
        </div>
      </div>
      
      <div class="metric-card__body">
        <div class="metric-card__title">{{ title }}</div>
        <div class="metric-card__value">
          <a-skeleton-input v-if="loading" active size="large" />
          <span v-else>{{ displayValue }}</span>
        </div>
        
        <!-- 目标进度条 -->
        <div v-if="!loading && target" class="metric-card__progress">
          <div class="progress-info">
            <span class="progress-label">目标完成度</span>
            <span class="progress-percentage">{{ targetPercentage }}%</span>
          </div>
          <a-progress
            :percent="targetPercentage"
            :stroke-color="progressColor"
            :show-info="false"
            size="small"
          />
        </div>
      </div>
    </div>
    
    <div class="metric-card__footer" v-if="!loading">
      <div class="footer-left">
        <span class="footer-text">
          较上期
          <span :class="trendClass">
            {{ trend > 0 ? '上升' : trend < 0 ? '下降' : '持平' }}
          </span>
        </span>
      </div>
      
      <div v-if="target" class="footer-right">
        <span class="target-text">
          目标: {{ formatTargetValue }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ShoppingCartOutlined,
  DollarOutlined,
  CalculatorOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue';

interface Props {
  title: string;
  value: string | number;
  trend?: number;
  target?: number;
  icon: string;
  color: 'blue' | 'green' | 'red' | 'purple' | 'orange';
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 图标映射
const iconMap = {
  ShoppingCartOutlined,
  DollarOutlined,
  CalculatorOutlined,
  ExclamationCircleOutlined,
  UserOutlined
};

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || UserOutlined;
});

const displayValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString();
  }
  return props.value;
});

const formatTargetValue = computed(() => {
  if (!props.target) return '';
  
  if (typeof props.value === 'string' && props.value.includes('¥')) {
    return `¥${props.target.toLocaleString()}`;
  } else if (typeof props.value === 'string' && props.value.includes('%')) {
    return `${props.target}%`;
  } else {
    return props.target.toLocaleString();
  }
});

const targetPercentage = computed(() => {
  if (!props.target) return 0;
  
  let currentValue = 0;
  if (typeof props.value === 'number') {
    currentValue = props.value;
  } else if (typeof props.value === 'string') {
    // 提取数字部分
    const match = props.value.match(/[\d,]+/);
    if (match) {
      currentValue = parseFloat(match[0].replace(/,/g, ''));
    }
  }
  
  const percentage = Math.min((currentValue / props.target) * 100, 100);
  return Math.round(percentage);
});

const progressColor = computed(() => {
  const percentage = targetPercentage.value;
  
  if (percentage >= 100) {
    return '#52c41a'; // 绿色 - 已完成
  } else if (percentage >= 80) {
    return '#1890ff'; // 蓝色 - 接近完成
  } else if (percentage >= 60) {
    return '#faad14'; // 黄色 - 进行中
  } else {
    return '#ff4d4f'; // 红色 - 需要关注
  }
});

const trendClass = computed(() => {
  if (props.trend === undefined) return '';
  
  if (props.trend > 0) {
    return 'trend-up';
  } else if (props.trend < 0) {
    return 'trend-down';
  } else {
    return 'trend-neutral';
  }
});
</script>

<style lang="scss" scoped>
.metric-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  @include transition(all);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: currentColor;
  }
  
  &:hover {
    @include shadow('md');
    transform: translateY(-2px);
  }
  
  &--loading {
    &:hover {
      transform: none;
    }
  }
  
  // 颜色主题
  &--blue {
    color: $primary-500;
    
    .metric-card__icon {
      background: rgba($primary-500, 0.1);
      color: $primary-500;
    }
  }
  
  &--green {
    color: $success-500;
    
    .metric-card__icon {
      background: rgba($success-500, 0.1);
      color: $success-500;
    }
  }
  
  &--red {
    color: $error-500;
    
    .metric-card__icon {
      background: rgba($error-500, 0.1);
      color: $error-500;
    }
  }
  
  &--purple {
    color: #722ed1;
    
    .metric-card__icon {
      background: rgba(#722ed1, 0.1);
      color: #722ed1;
    }
  }
  
  &--orange {
    color: $warning-500;
    
    .metric-card__icon {
      background: rgba($warning-500, 0.1);
      color: $warning-500;
    }
  }
}

.metric-card__content {
  margin-bottom: $space-4;
}

.metric-card__header {
  @include flex-between;
  margin-bottom: $space-4;
}

.metric-card__icon {
  width: 48px;
  height: 48px;
  @include border-radius('lg');
  @include flex-center;
  font-size: 24px;
}

.metric-card__trend {
  .trend-value {
    @include flex-center-vertical;
    gap: $space-1;
    font-size: $text-sm;
    font-weight: $font-medium;
    padding: $space-1 $space-2;
    @include border-radius('sm');
    
    &.trend-up {
      color: $success-600;
      background: rgba($success-500, 0.1);
    }
    
    &.trend-down {
      color: $error-600;
      background: rgba($error-500, 0.1);
    }
    
    &.trend-neutral {
      color: $text-secondary;
      background: $bg-secondary;
    }
  }
}

.metric-card__body {
  .metric-card__title {
    font-size: $text-sm;
    color: $text-secondary;
    margin-bottom: $space-2;
  }
  
  .metric-card__value {
    font-size: $text-3xl;
    font-weight: $font-bold;
    color: $text-primary;
    line-height: 1;
    margin-bottom: $space-3;
  }
  
  .metric-card__progress {
    .progress-info {
      @include flex-between;
      margin-bottom: $space-2;
      
      .progress-label {
        font-size: $text-xs;
        color: $text-tertiary;
      }
      
      .progress-percentage {
        font-size: $text-xs;
        font-weight: $font-medium;
        color: $text-secondary;
      }
    }
  }
}

.metric-card__footer {
  @include flex-between;
  border-top: 1px solid $border-light;
  padding-top: $space-3;
  
  .footer-left {
    .footer-text {
      font-size: $text-sm;
      color: $text-tertiary;
      
      .trend-up {
        color: $success-600;
      }
      
      .trend-down {
        color: $error-600;
      }
      
      .trend-neutral {
        color: $text-secondary;
      }
    }
  }
  
  .footer-right {
    .target-text {
      font-size: $text-sm;
      color: $text-tertiary;
    }
  }
}

// 响应式适配
@include responsive('md') {
  .metric-card {
    padding: $space-4;
    
    .metric-card__icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }
    
    .metric-card__value {
      font-size: $text-2xl;
    }
    
    .metric-card__footer {
      flex-direction: column;
      gap: $space-2;
      align-items: flex-start;
    }
  }
}
</style>
