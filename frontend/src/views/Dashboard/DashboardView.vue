<!-- 仪表板页面 -->
<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title">数据仪表板</h1>
        <p class="page-description">实时监控业务数据和关键指标</p>
      </div>
      
      <div class="header-actions">
        <a-space>
          <a-select
            v-model:value="selectedDateRange"
            style="width: 150px"
            @change="handleDateRangeChange"
          >
            <a-select-option value="today">今日</a-select-option>
            <a-select-option value="week">本周</a-select-option>
            <a-select-option value="month">本月</a-select-option>
            <a-select-option value="quarter">本季度</a-select-option>
          </a-select>
          
          <a-button @click="refreshData" :loading="isRefreshing">
            <ReloadOutlined />
            刷新数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <StatCard
        title="总订单数"
        :value="dashboardData.totalOrders"
        :trend="dashboardData.ordersTrend"
        icon="ShoppingCartOutlined"
        color="blue"
        :loading="loading"
      />
      
      <StatCard
        title="总金额"
        :value="formatCurrency(dashboardData.totalAmount)"
        :trend="dashboardData.amountTrend"
        icon="DollarOutlined"
        color="green"
        :loading="loading"
      />
      
      <StatCard
        title="逾期订单"
        :value="dashboardData.overdueOrders"
        :trend="dashboardData.overdueTrend"
        icon="ExclamationCircleOutlined"
        color="red"
        :loading="loading"
      />
      
      <StatCard
        title="客户数量"
        :value="dashboardData.totalCustomers"
        :trend="dashboardData.customersTrend"
        icon="UserOutlined"
        color="purple"
        :loading="loading"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 订单趋势图 -->
      <div class="chart-card">
        <div class="card-header">
          <h3>订单趋势</h3>
          <a-space>
            <a-radio-group v-model:value="orderChartType" size="small">
              <a-radio-button value="line">折线图</a-radio-button>
              <a-radio-button value="bar">柱状图</a-radio-button>
            </a-radio-group>
          </a-space>
        </div>
        
        <ChartComponent
          :type="orderChartType"
          :data="orderTrendData"
          :loading="loading"
          height="300px"
          @refresh="refreshOrderTrend"
        />
      </div>

      <!-- 业务分布图 -->
      <div class="chart-card">
        <div class="card-header">
          <h3>业务分布</h3>
        </div>
        
        <ChartComponent
          type="pie"
          :data="businessDistributionData"
          :loading="loading"
          height="300px"
          @refresh="refreshBusinessDistribution"
        />
      </div>

      <!-- 逾期分析图 -->
      <div class="chart-card">
        <div class="card-header">
          <h3>逾期分析</h3>
        </div>
        
        <ChartComponent
          type="bar"
          :data="overdueAnalysisData"
          :loading="loading"
          height="300px"
          @refresh="refreshOverdueAnalysis"
        />
      </div>

      <!-- 客服业绩图 -->
      <div class="chart-card">
        <div class="card-header">
          <h3>客服业绩</h3>
        </div>
        
        <ChartComponent
          type="bar"
          :data="servicePerformanceData"
          :loading="loading"
          height="300px"
          @refresh="refreshServicePerformance"
        />
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <h3>快速操作</h3>
      <div class="actions-grid">
        <QuickActionCard
          title="数据查询"
          description="按日期查询订单数据"
          icon="SearchOutlined"
          @click="goToDataQuery"
        />
        
        <QuickActionCard
          title="客户查询"
          description="查询客户订单信息"
          icon="UserOutlined"
          @click="goToCustomerQuery"
        />
        
        <QuickActionCard
          title="逾期订单"
          description="查看逾期订单列表"
          icon="ExclamationCircleOutlined"
          @click="goToOverdueOrders"
        />
        
        <QuickActionCard
          title="数据导出"
          description="导出Excel或CSV文件"
          icon="DownloadOutlined"
          @click="showExportModal"
        />
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h3>最近活动</h3>
      <a-list
        :data-source="recentActivities"
        :loading="loading"
        class="activity-list"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :style="{ backgroundColor: item.color }">
                  <component :is="item.icon" />
                </a-avatar>
              </template>
              <template #title>{{ item.title }}</template>
              <template #description>{{ item.description }}</template>
            </a-list-item-meta>
            <div class="activity-time">{{ item.time }}</div>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ReloadOutlined } from '@ant-design/icons-vue';
import { useDataStore } from '@/stores/data';
import { useUserStore } from '@/stores/user';
import StatCard from './components/StatCard.vue';
import ChartComponent from '@/components/Charts/ChartComponent.vue';
import QuickActionCard from './components/QuickActionCard.vue';
import { message } from 'ant-design-vue';

const router = useRouter();
const dataStore = useDataStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const isRefreshing = ref(false);
const selectedDateRange = ref('month');
const orderChartType = ref<'line' | 'bar'>('line');

// 仪表板数据
const dashboardData = reactive({
  totalOrders: 0,
  totalAmount: 0,
  overdueOrders: 0,
  totalCustomers: 0,
  ordersTrend: 0,
  amountTrend: 0,
  overdueTrend: 0,
  customersTrend: 0
});

// 图表数据
const orderTrendData = ref({
  labels: [],
  datasets: []
});

const businessDistributionData = ref({
  labels: [],
  datasets: []
});

const overdueAnalysisData = ref({
  labels: [],
  datasets: []
});

const servicePerformanceData = ref({
  labels: [],
  datasets: []
});

// 最近活动数据
const recentActivities = ref([
  {
    title: '数据查询',
    description: '用户查询了2024-01-15的订单数据',
    time: '2分钟前',
    icon: 'SearchOutlined',
    color: '#1890ff'
  },
  {
    title: '数据导出',
    description: '导出了逾期订单Excel文件',
    time: '10分钟前',
    icon: 'DownloadOutlined',
    color: '#52c41a'
  },
  {
    title: '客户查询',
    description: '查询了客户"张三"的订单信息',
    time: '30分钟前',
    icon: 'UserOutlined',
    color: '#722ed1'
  }
]);

// 方法
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount);
};

const loadDashboardData = async () => {
  loading.value = true;
  try {
    // 模拟加载仪表板数据
    // 实际项目中这里会调用API获取数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 更新统计数据
    Object.assign(dashboardData, {
      totalOrders: 1234,
      totalAmount: 5678900,
      overdueOrders: 56,
      totalCustomers: 789,
      ordersTrend: 12.5,
      amountTrend: 8.3,
      overdueTrend: -5.2,
      customersTrend: 15.7
    });

    // 更新图表数据
    orderTrendData.value = {
      labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
      datasets: [{
        label: '订单数量',
        data: [120, 190, 300, 500, 200, 300],
        backgroundColor: '#1890ff',
        borderColor: '#1890ff'
      }]
    };

    businessDistributionData.value = {
      labels: ['电商业务', '租赁业务', '其他业务'],
      datasets: [{
        label: '业务分布',
        data: [60, 30, 10],
        backgroundColor: ['#1890ff', '#52c41a', '#faad14']
      }]
    };

    overdueAnalysisData.value = {
      labels: ['1-7天', '8-15天', '16-30天', '30天以上'],
      datasets: [{
        label: '逾期订单数',
        data: [25, 15, 10, 6],
        backgroundColor: '#ff4d4f'
      }]
    };

    servicePerformanceData.value = {
      labels: ['客服A', '客服B', '客服C', '客服D', '客服E'],
      datasets: [{
        label: '处理订单数',
        data: [150, 120, 180, 90, 160],
        backgroundColor: '#52c41a'
      }]
    };

  } catch (error) {
    console.error('加载仪表板数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const refreshData = async () => {
  isRefreshing.value = true;
  try {
    await loadDashboardData();
    message.success('数据刷新成功');
  } finally {
    isRefreshing.value = false;
  }
};

const handleDateRangeChange = (value: string) => {
  selectedDateRange.value = value;
  loadDashboardData();
};

// 图表刷新方法
const refreshOrderTrend = () => {
  // 刷新订单趋势数据
  loadDashboardData();
};

const refreshBusinessDistribution = () => {
  // 刷新业务分布数据
  loadDashboardData();
};

const refreshOverdueAnalysis = () => {
  // 刷新逾期分析数据
  loadDashboardData();
};

const refreshServicePerformance = () => {
  // 刷新客服业绩数据
  loadDashboardData();
};

// 快速操作方法
const goToDataQuery = () => {
  router.push({ name: 'DataQuery' });
};

const goToCustomerQuery = () => {
  router.push({ name: 'CustomerQuery' });
};

const goToOverdueOrders = () => {
  if (userStore.canAccessOverdueOrders) {
    router.push({ name: 'OverdueOrders' });
  } else {
    message.warning('您没有权限访问逾期订单');
  }
};

const showExportModal = () => {
  // 显示导出模态框
  message.info('导出功能开发中...');
};

// 生命周期
onMounted(() => {
  loadDashboardData();
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: $space-6;
  
  @include responsive('md') {
    padding: $space-4;
  }
}

.dashboard-header {
  @include flex-between;
  margin-bottom: $space-8;
  
  @include responsive('md') {
    flex-direction: column;
    gap: $space-4;
    align-items: flex-start;
  }
  
  .header-content {
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $space-6;
  margin-bottom: $space-8;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: $space-6;
  margin-bottom: $space-8;
  
  @include responsive('md') {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  
  .card-header {
    @include flex-between;
    margin-bottom: $space-4;
    
    h3 {
      font-size: $text-lg;
      font-weight: $font-semibold;
      color: $text-primary;
      margin: 0;
    }
  }
}

.quick-actions {
  margin-bottom: $space-8;
  
  h3 {
    font-size: $text-xl;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $space-4;
  }
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $space-4;
  }
}

.recent-activity {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  
  h3 {
    font-size: $text-xl;
    font-weight: $font-semibold;
    color: $text-primary;
    margin-bottom: $space-4;
  }
  
  .activity-list {
    .activity-time {
      color: $text-tertiary;
      font-size: $text-sm;
    }
  }
}
</style>
