<!-- 统计卡片组件 -->
<template>
  <div class="stat-card" :class="[`stat-card--${color}`, { 'stat-card--loading': loading }]">
    <div class="stat-card__content">
      <div class="stat-card__header">
        <div class="stat-card__icon">
          <component :is="iconComponent" />
        </div>
        <div class="stat-card__trend" v-if="!loading && trend !== undefined">
          <span class="trend-value" :class="trendClass">
            <ArrowUpOutlined v-if="trend > 0" />
            <ArrowDownOutlined v-if="trend < 0" />
            {{ Math.abs(trend) }}%
          </span>
        </div>
      </div>
      
      <div class="stat-card__body">
        <div class="stat-card__title">{{ title }}</div>
        <div class="stat-card__value">
          <a-skeleton-input v-if="loading" active size="large" />
          <span v-else>{{ displayValue }}</span>
        </div>
      </div>
    </div>
    
    <div class="stat-card__footer" v-if="!loading">
      <span class="footer-text">
        较上期
        <span :class="trendClass">
          {{ trend > 0 ? '上升' : trend < 0 ? '下降' : '持平' }}
        </span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ShoppingCartOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue';

interface Props {
  title: string;
  value: string | number;
  trend?: number;
  icon: string;
  color: 'blue' | 'green' | 'red' | 'purple' | 'orange';
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 图标映射
const iconMap = {
  ShoppingCartOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  UserOutlined
};

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || UserOutlined;
});

const displayValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString();
  }
  return props.value;
});

const trendClass = computed(() => {
  if (props.trend === undefined) return '';
  
  if (props.trend > 0) {
    return 'trend-up';
  } else if (props.trend < 0) {
    return 'trend-down';
  } else {
    return 'trend-neutral';
  }
});
</script>

<style lang="scss" scoped>
.stat-card {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  padding: $space-6;
  @include transition(all);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: currentColor;
  }
  
  &:hover {
    @include shadow('md');
    transform: translateY(-2px);
  }
  
  &--loading {
    &:hover {
      transform: none;
    }
  }
  
  // 颜色主题
  &--blue {
    color: $primary-500;
    
    .stat-card__icon {
      background: rgba($primary-500, 0.1);
      color: $primary-500;
    }
  }
  
  &--green {
    color: $success-500;
    
    .stat-card__icon {
      background: rgba($success-500, 0.1);
      color: $success-500;
    }
  }
  
  &--red {
    color: $error-500;
    
    .stat-card__icon {
      background: rgba($error-500, 0.1);
      color: $error-500;
    }
  }
  
  &--purple {
    color: #722ed1;
    
    .stat-card__icon {
      background: rgba(#722ed1, 0.1);
      color: #722ed1;
    }
  }
  
  &--orange {
    color: $warning-500;
    
    .stat-card__icon {
      background: rgba($warning-500, 0.1);
      color: $warning-500;
    }
  }
}

.stat-card__content {
  margin-bottom: $space-4;
}

.stat-card__header {
  @include flex-between;
  margin-bottom: $space-4;
}

.stat-card__icon {
  width: 48px;
  height: 48px;
  @include border-radius('lg');
  @include flex-center;
  font-size: 24px;
}

.stat-card__trend {
  .trend-value {
    @include flex-center-vertical;
    gap: $space-1;
    font-size: $text-sm;
    font-weight: $font-medium;
    padding: $space-1 $space-2;
    @include border-radius('sm');
    
    &.trend-up {
      color: $success-600;
      background: rgba($success-500, 0.1);
    }
    
    &.trend-down {
      color: $error-600;
      background: rgba($error-500, 0.1);
    }
    
    &.trend-neutral {
      color: $text-secondary;
      background: $bg-secondary;
    }
  }
}

.stat-card__body {
  .stat-card__title {
    font-size: $text-sm;
    color: $text-secondary;
    margin-bottom: $space-2;
  }
  
  .stat-card__value {
    font-size: $text-3xl;
    font-weight: $font-bold;
    color: $text-primary;
    line-height: 1;
  }
}

.stat-card__footer {
  border-top: 1px solid $border-light;
  padding-top: $space-3;
  
  .footer-text {
    font-size: $text-sm;
    color: $text-tertiary;
    
    .trend-up {
      color: $success-600;
    }
    
    .trend-down {
      color: $error-600;
    }
    
    .trend-neutral {
      color: $text-secondary;
    }
  }
}

// 响应式适配
@include responsive('md') {
  .stat-card {
    padding: $space-4;
    
    .stat-card__icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }
    
    .stat-card__value {
      font-size: $text-2xl;
    }
  }
}
</style>
