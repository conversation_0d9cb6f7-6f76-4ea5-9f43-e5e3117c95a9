<!-- 快速操作卡片组件 -->
<template>
  <div class="quick-action-card" @click="handleClick">
    <div class="action-card__icon">
      <component :is="iconComponent" />
    </div>
    
    <div class="action-card__content">
      <h4 class="action-card__title">{{ title }}</h4>
      <p class="action-card__description">{{ description }}</p>
    </div>
    
    <div class="action-card__arrow">
      <RightOutlined />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  SearchOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  SettingOutlined,
  BarChartOutlined,
  RightOutlined
} from '@ant-design/icons-vue';

interface Props {
  title: string;
  description: string;
  icon: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  click: [];
}>();

// 图标映射
const iconMap = {
  SearchOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  SettingOutlined,
  BarChartOutlined
};

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || SearchOutlined;
});

// 方法
const handleClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.quick-action-card {
  display: flex;
  align-items: center;
  gap: $space-4;
  padding: $space-4;
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  @include transition(all);
  cursor: pointer;
  border: 1px solid $border-light;
  
  &:hover {
    @include shadow('md');
    transform: translateY(-2px);
    border-color: $primary-500;
    
    .action-card__icon {
      background: $primary-500;
      color: white;
    }
    
    .action-card__arrow {
      color: $primary-500;
      transform: translateX(4px);
    }
  }
}

.action-card__icon {
  width: 48px;
  height: 48px;
  @include border-radius('lg');
  @include flex-center;
  background: $bg-secondary;
  color: $text-secondary;
  font-size: 20px;
  @include transition(all);
  flex-shrink: 0;
}

.action-card__content {
  flex: 1;
  
  .action-card__title {
    font-size: $text-base;
    font-weight: $font-semibold;
    color: $text-primary;
    margin: 0 0 $space-1 0;
  }
  
  .action-card__description {
    font-size: $text-sm;
    color: $text-secondary;
    margin: 0;
    line-height: $leading-normal;
  }
}

.action-card__arrow {
  color: $text-tertiary;
  font-size: $text-sm;
  @include transition(all);
  flex-shrink: 0;
}

// 响应式适配
@include responsive('md') {
  .quick-action-card {
    padding: $space-3;
    gap: $space-3;
    
    .action-card__icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
    }
    
    .action-card__title {
      font-size: $text-sm;
    }
    
    .action-card__description {
      font-size: $text-xs;
    }
  }
}
</style>
