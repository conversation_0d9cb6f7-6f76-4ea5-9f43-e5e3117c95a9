<!-- 登录页面 -->
<template>
  <div class="login-container">
    <div class="login-background">
      <!-- 背景装饰 -->
      <div class="bg-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
    </div>

    <div class="login-content">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <div class="info-content">
          <h1 class="system-title">{{ appStore.appConfig.title }}</h1>
          <p class="system-description">{{ appStore.appConfig.description }}</p>
          
          <div class="feature-list">
            <div class="feature-item">
              <CheckCircleOutlined class="feature-icon" />
              <span>现代化数据查询界面</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined class="feature-icon" />
              <span>实时数据分析与展示</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined class="feature-icon" />
              <span>多维度报表统计</span>
            </div>
            <div class="feature-item">
              <CheckCircleOutlined class="feature-icon" />
              <span>安全的权限管理</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form-wrapper">
          <!-- 表单头部 -->
          <div class="form-header">
            <img src="/logo.png" alt="Logo" class="logo" />
            <h2 class="form-title">用户登录</h2>
            <p class="form-subtitle">请输入您的账号信息</p>
          </div>

          <!-- 登录表单 -->
          <a-form
            ref="formRef"
            :model="loginForm"
            :rules="formRules"
            @finish="handleLogin"
            class="login-form"
            layout="vertical"
          >
            <!-- 用户名 -->
            <a-form-item name="username" label="用户名">
              <a-input
                v-model:value="loginForm.username"
                size="large"
                placeholder="请输入用户名"
                :prefix="h(UserOutlined)"
                @keyup.enter="handleLogin"
              />
            </a-form-item>

            <!-- 密码 -->
            <a-form-item name="password" label="密码">
              <a-input-password
                v-model:value="loginForm.password"
                size="large"
                placeholder="请输入密码"
                :prefix="h(LockOutlined)"
                @keyup.enter="handleLogin"
              />
            </a-form-item>

            <!-- 验证码 -->
            <a-form-item name="captcha" label="验证码">
              <div class="captcha-container">
                <a-input
                  v-model:value="loginForm.captcha"
                  size="large"
                  placeholder="请输入验证码"
                  :prefix="h(SafetyOutlined)"
                  @keyup.enter="handleLogin"
                />
                <div class="captcha-image" @click="refreshCaptcha">
                  <img
                    v-if="captchaUrl"
                    :src="captchaUrl"
                    alt="验证码"
                    class="captcha-img"
                  />
                  <a-spin v-else size="small" />
                </div>
              </div>
            </a-form-item>

            <!-- 记住登录 -->
            <a-form-item>
              <div class="form-options">
                <a-checkbox v-model:checked="rememberLogin">
                  记住登录状态
                </a-checkbox>
                <a href="#" class="forgot-password">忘记密码？</a>
              </div>
            </a-form-item>

            <!-- 登录按钮 -->
            <a-form-item>
              <a-button
                type="primary"
                size="large"
                block
                :loading="userStore.loginLoading"
                @click="handleLogin"
                class="login-button"
              >
                {{ userStore.loginLoading ? '登录中...' : '登录' }}
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 其他登录方式 -->
          <div class="other-login">
            <a-divider>其他登录方式</a-divider>
            <div class="social-login">
              <a-tooltip title="企业微信登录">
                <a-button shape="circle" size="large">
                  <WechatOutlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="钉钉登录">
                <a-button shape="circle" size="large">
                  <DingdingOutlined />
                </a-button>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="login-footer">
      <span>© 2024 太享科技. All rights reserved.</span>
      <span>v{{ appStore.appConfig.version }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue';
import {
  UserOutlined,
  LockOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  WechatOutlined,
  DingdingOutlined
} from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/user';
import { useAppStore } from '@/stores/app';
import { AuthService } from '@/api/services/authService';
import { message } from 'ant-design-vue';
import type { FormInstance, Rule } from 'ant-design-vue/es/form';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const appStore = useAppStore();

// 表单引用
const formRef = ref<FormInstance>();

// 响应式数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: ''
});

const rememberLogin = ref(false);
const captchaUrl = ref('');
const captchaKey = ref('');

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码为4位字符', trigger: 'blur' }
  ]
};

// 方法
const getCaptcha = async () => {
  try {
    const response = await AuthService.getCaptcha();
    
    if (!response) {
      throw new Error('响应数据为空');
    }
    
    if (!response.captcha_url) {
      throw new Error('响应中缺少 captcha_url 字段');
    }
    
    captchaUrl.value = response.captcha_url;
    captchaKey.value = response.captcha_key;
  } catch (error: any) {
    console.error('获取验证码失败:', error);
    message.error('获取验证码失败: ' + (error.message || '未知错误'));
  }
};

const refreshCaptcha = () => {
  getCaptcha();
  loginForm.captcha = '';
};

const handleLogin = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();
    
    // 执行登录
    const credentials = {
      username: loginForm.username,
      password: loginForm.password,
      captcha: loginForm.captcha
    };
    
    await userStore.login(credentials);
    
    message.success('登录成功');
    
    // 跳转到目标页面
    const redirect = route.query.redirect as string;
    router.push(redirect || { name: 'Dashboard' });
    
  } catch (error: any) {
    console.error('登录失败:', error);
    
    // 刷新验证码
    refreshCaptcha();
    
    // 显示错误信息
    if (error.message) {
      message.error(error.message);
    } else {
      message.error('登录失败，请检查用户名和密码');
    }
  }
};

// 生命周期
onMounted(() => {
  // 获取验证码
  getCaptcha();
  
  // 如果已经登录，直接跳转
  if (userStore.isAuthenticated) {
    router.push({ name: 'Dashboard' });
  }
});
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  @include flex-center;
  background: linear-gradient(135deg, $primary-500 0%, $primary-700 100%);
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  
  .bg-decoration {
    position: relative;
    width: 100%;
    height: 100%;
    
    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      
      &.circle-1 {
        width: 300px;
        height: 300px;
        top: -150px;
        right: -150px;
      }
      
      &.circle-2 {
        width: 200px;
        height: 200px;
        bottom: -100px;
        left: -100px;
      }
      
      &.circle-3 {
        width: 150px;
        height: 150px;
        top: 50%;
        left: 10%;
        transform: translateY(-50%);
      }
    }
  }
}

.login-content {
  position: relative;
  z-index: 2;
  display: flex;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  @include shadow('xl');
  @include border-radius('2xl');
  overflow: hidden;
  background: $bg-primary;
  
  @include responsive('lg') {
    flex-direction: column;
    max-width: 400px;
    margin: $space-4;
  }
}

.login-info {
  flex: 1;
  background: linear-gradient(135deg, $primary-600 0%, $primary-800 100%);
  color: white;
  padding: $space-16;
  @include flex-center;
  
  @include responsive('lg') {
    display: none;
  }
  
  .info-content {
    max-width: 400px;
  }
  
  .system-title {
    font-size: $text-4xl;
    font-weight: $font-bold;
    margin-bottom: $space-4;
    color: white;
  }
  
  .system-description {
    font-size: $text-lg;
    margin-bottom: $space-8;
    opacity: 0.9;
    line-height: $leading-relaxed;
  }
  
  .feature-list {
    .feature-item {
      @include flex-center-vertical;
      gap: $space-3;
      margin-bottom: $space-4;
      
      .feature-icon {
        color: $success-400;
        font-size: $text-lg;
      }
      
      span {
        font-size: $text-base;
      }
    }
  }
}

.login-form-container {
  flex: 1;
  @include flex-center;
  padding: $space-12;
  
  @include responsive('lg') {
    padding: $space-8;
  }
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: $space-8;
  
  .logo {
    width: 64px;
    height: 64px;
    margin-bottom: $space-4;
  }
  
  .form-title {
    font-size: $text-3xl;
    font-weight: $font-bold;
    color: $text-primary;
    margin-bottom: $space-2;
  }
  
  .form-subtitle {
    color: $text-secondary;
    font-size: $text-base;
  }
}

.login-form {
  .captcha-container {
    display: flex;
    gap: $space-3;
    
    .ant-input {
      flex: 1;
    }
    
    .captcha-image {
      width: 120px;
      height: 40px;
      border: 1px solid $border-medium;
      @include border-radius();
      @include flex-center;
      cursor: pointer;
      @include transition(border-color);
      
      &:hover {
        border-color: $primary-500;
      }
      
      .captcha-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        @include border-radius();
      }
    }
  }
  
  .form-options {
    @include flex-between;
    
    .forgot-password {
      color: $primary-500;
      font-size: $text-sm;
      
      &:hover {
        color: $primary-600;
      }
    }
  }
  
  .login-button {
    height: 48px;
    font-size: $text-base;
    font-weight: $font-medium;
    @include border-radius();
  }
}

.other-login {
  margin-top: $space-6;
  
  .social-login {
    @include flex-center;
    gap: $space-4;
    
    .ant-btn {
      @include flex-center;
      width: 48px;
      height: 48px;
      border-color: $border-medium;
      
      &:hover {
        border-color: $primary-500;
        color: $primary-500;
      }
    }
  }
}

.login-footer {
  position: absolute;
  bottom: $space-6;
  left: 50%;
  transform: translateX(-50%);
  @include flex-center;
  gap: $space-4;
  color: rgba(255, 255, 255, 0.8);
  font-size: $text-sm;
  z-index: 2;
  
  @include responsive('lg') {
    position: static;
    transform: none;
    margin-top: $space-6;
    color: $text-tertiary;
  }
}

// 响应式适配
@include responsive('md') {
  .login-content {
    margin: $space-2;
  }
  
  .login-form-container {
    padding: $space-6;
  }
  
  .form-header {
    .form-title {
      font-size: $text-2xl;
    }
  }
}
</style>
