<!-- 个人资料页面 -->
<template>
  <div class="profile">
    <div class="page-header">
      <h1 class="page-title">个人资料</h1>
      <p class="page-description">查看和编辑个人信息</p>
    </div>
    
    <a-card>
      <div class="coming-soon">
        <a-result
          status="info"
          title="功能开发中"
          sub-title="个人资料功能正在开发中，敬请期待"
        >
          <template #extra>
            <a-button type="primary" @click="goBack">
              返回
            </a-button>
          </template>
        </a-result>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.go(-1);
};
</script>

<style lang="scss" scoped>
.profile {
  padding: $space-6;
  
  .page-header {
    margin-bottom: $space-6;
    
    .page-title {
      font-size: $text-3xl;
      font-weight: $font-bold;
      color: $text-primary;
      margin-bottom: $space-2;
    }
    
    .page-description {
      color: $text-secondary;
      font-size: $text-base;
    }
  }
}
</style>
