// SCSS变量定义 - 设计系统的核心变量
// 这些变量将被自动导入到所有SCSS文件中

// ==================== 色彩系统 ====================

// 主色调 - 专业蓝色系
$primary-50: #eff6ff;
$primary-100: #dbeafe;
$primary-200: #bfdbfe;
$primary-300: #93c5fd;
$primary-400: #60a5fa;
$primary-500: #3b82f6;  // 主色
$primary-600: #2563eb;
$primary-700: #1d4ed8;
$primary-800: #1e40af;
$primary-900: #1e3a8a;

// 辅助色
$success-50: #f0fdf4;
$success-100: #dcfce7;
$success-200: #bbf7d0;
$success-300: #86efac;
$success-400: #4ade80;
$success-500: #22c55e;
$success-600: #16a34a;
$success-700: #15803d;
$success-800: #166534;
$success-900: #14532d;

$warning-50: #fffbeb;
$warning-100: #fef3c7;
$warning-200: #fde68a;
$warning-300: #fcd34d;
$warning-400: #fbbf24;
$warning-500: #f59e0b;
$warning-600: #d97706;
$warning-700: #b45309;
$warning-800: #92400e;
$warning-900: #78350f;

$error-50: #fef2f2;
$error-100: #fee2e2;
$error-200: #fecaca;
$error-300: #fca5a5;
$error-400: #f87171;
$error-500: #ef4444;
$error-600: #dc2626;
$error-700: #b91c1c;
$error-800: #991b1b;
$error-900: #7f1d1d;

// 中性色系
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// ==================== 圆角系统 ====================

$radius-sm: 0.25rem;
$radius-md: 0.375rem;
$radius-lg: 0.5rem;
$radius-xl: 0.75rem;
$radius-2xl: 1rem;

// 语义化颜色
$bg-primary: #ffffff;
$bg-secondary: $gray-50;
$bg-tertiary: $gray-100;
$bg-hover: $gray-100;  // 添加悬停背景色

$text-primary: $gray-900;
$text-secondary: $gray-600;
$text-tertiary: $gray-400;

$border-light: $gray-200;
$border-medium: $gray-300;
$border-dark: $gray-400;
$border-color: $gray-300;  // 添加通用边框色
$border-radius: $radius-md;  // 添加通用圆角
$border-radius-lg: $radius-lg;  // 添加大圆角

// ==================== 间距系统 ====================

$space-1: 0.25rem;   // 4px
$space-2: 0.5rem;    // 8px
$space-3: 0.75rem;   // 12px
$space-4: 1rem;      // 16px
$space-5: 1.25rem;   // 20px
$space-6: 1.5rem;    // 24px
$space-8: 2rem;      // 32px
$space-10: 2.5rem;   // 40px
$space-12: 3rem;     // 48px
$space-16: 4rem;     // 64px

// ==================== 字体系统 ====================

$font-family-sans: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

// 字体大小
$text-xs: 0.75rem;     // 12px
$text-sm: 0.875rem;    // 14px
$text-base: 1rem;      // 16px
$text-lg: 1.125rem;    // 18px
$text-xl: 1.25rem;     // 20px
$text-2xl: 1.5rem;     // 24px
$text-3xl: 1.875rem;   // 30px
$text-4xl: 2.25rem;    // 36px

// 行高
$leading-tight: 1.25;
$leading-normal: 1.5;
$leading-relaxed: 1.75;

// 字重
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;

// ==================== 阴影系统 ====================

$shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
$shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
$shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
$shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

// ==================== 过渡动画 ====================

$transition-fast: 150ms ease-in-out;
$transition-normal: 250ms ease-in-out;
$transition-slow: 350ms ease-in-out;

// ==================== Z-index层级 ====================

$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// ==================== 响应式断点 ====================

$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// 断点映射
$breakpoints: (
  'sm': $breakpoint-sm,
  'md': $breakpoint-md,
  'lg': $breakpoint-lg,
  'xl': $breakpoint-xl,
  '2xl': $breakpoint-2xl
);

// ==================== 组件特定变量 ====================

// 侧边栏
$sidebar-width: 250px;
$sidebar-collapsed-width: 60px;
$sidebar-bg: $gray-800;
$sidebar-text: #ffffff;

// 头部
$header-height: 64px;
$header-bg: $bg-primary;
$header-border: $border-light;

// 表格
$table-header-bg: $bg-tertiary;
$table-row-hover-bg: $bg-secondary;
$table-border: $border-light;

// 卡片
$card-bg: $bg-primary;
$card-border: $border-light;
$card-shadow: $shadow-sm;

// 按钮
$button-height-sm: 24px;
$button-height-md: 32px;
$button-height-lg: 40px;

// 输入框
$input-height-sm: 24px;
$input-height-md: 32px;
$input-height-lg: 40px;
$input-border: $border-medium;
$input-focus-border: $primary-500;

// ==================== 状态颜色 ====================

// 订单状态颜色
$status-overdue: $error-500;
$status-normal: $success-500;
$status-warning: $warning-500;
$status-pending: $gray-500;

// 金额颜色
$amount-positive: $success-600;
$amount-negative: $error-600;
$amount-zero: $gray-500;

// ==================== 图表颜色 ====================

$chart-colors: (
  $primary-500,
  $success-500,
  $warning-500,
  $error-500,
  $gray-500,
  $primary-300,
  $success-300,
  $warning-300,
  $error-300,
  $gray-300
);

// ==================== 动画缓动函数 ====================

$ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out-cubic: cubic-bezier(0, 0, 0.2, 1);
$ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);

// ==================== 布局变量 ====================

$container-max-width: 1200px;
$content-padding: $space-6;
$section-spacing: $space-12;

// ==================== 表单变量 ====================

$form-label-color: $text-secondary;
$form-label-font-weight: $font-medium;
$form-error-color: $error-500;
$form-success-color: $success-500;

// ==================== 加载状态 ====================

$loading-bg: rgba(255, 255, 255, 0.8);
$loading-spinner-color: $primary-500;
