// Ant Design Vue 组件样式覆盖
// 用于自定义 Ant Design Vue 组件的默认样式

// ==================== 全局覆盖 ====================

// 主色调覆盖
.ant-btn-primary {
  background-color: $primary-500;
  border-color: $primary-500;
  
  &:hover,
  &:focus {
    background-color: $primary-600;
    border-color: $primary-600;
  }
  
  &:active {
    background-color: $primary-700;
    border-color: $primary-700;
  }
}

// 表格样式优化
.ant-table {
  .ant-table-thead > tr > th {
    background-color: $bg-secondary;
    font-weight: $font-semibold;
    color: $text-primary;
  }
  
  .ant-table-tbody > tr:hover > td {
    background-color: $bg-hover;
  }
}

// 卡片样式优化
.ant-card {
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  
  .ant-card-head {
    border-bottom: 1px solid $border-color;
    
    .ant-card-head-title {
      font-weight: $font-semibold;
      color: $text-primary;
    }
  }
}

// 表单样式优化
.ant-form-item-label > label {
  font-weight: $font-medium;
  color: $text-primary;
}

.ant-input,
.ant-select-selector {
  border-radius: $border-radius;
  
  &:focus,
  &:hover {
    border-color: $primary-500;
  }
}

// 分页样式优化
.ant-pagination {
  .ant-pagination-item-active {
    background-color: $primary-500;
    border-color: $primary-500;
    
    a {
      color: white;
    }
  }
}

// 消息提示样式
.ant-message {
  .ant-message-success {
    .anticon {
      color: $success-500;
    }
  }
  
  .ant-message-error {
    .anticon {
      color: $error-500;
    }
  }
  
  .ant-message-warning {
    .anticon {
      color: $warning-500;
    }
  }
}

// 模态框样式
.ant-modal {
  .ant-modal-header {
    border-bottom: 1px solid $border-color;
    
    .ant-modal-title {
      font-weight: $font-semibold;
      color: $text-primary;
    }
  }
  
  .ant-modal-footer {
    border-top: 1px solid $border-color;
  }
}

// 抽屉样式
.ant-drawer {
  .ant-drawer-header {
    border-bottom: 1px solid $border-color;
    
    .ant-drawer-title {
      font-weight: $font-semibold;
      color: $text-primary;
    }
  }
}

// 标签页样式
.ant-tabs {
  .ant-tabs-tab {
    &.ant-tabs-tab-active {
      .ant-tabs-tab-btn {
        color: $primary-500;
      }
    }
  }
  
  .ant-tabs-ink-bar {
    background-color: $primary-500;
  }
}

// 菜单样式
.ant-menu {
  &.ant-menu-inline {
    .ant-menu-item-selected {
      background-color: $primary-50;
      
      &::after {
        border-right-color: $primary-500;
      }
    }
  }
}

// 步骤条样式
.ant-steps {
  .ant-steps-item-finish {
    .ant-steps-item-icon {
      background-color: $primary-500;
      border-color: $primary-500;
    }
  }
  
  .ant-steps-item-process {
    .ant-steps-item-icon {
      background-color: $primary-500;
      border-color: $primary-500;
    }
  }
}

// 进度条样式
.ant-progress {
  .ant-progress-bg {
    background-color: $primary-500;
  }
}

// 开关样式
.ant-switch-checked {
  background-color: $primary-500;
}

// 滑块样式
.ant-slider {
  .ant-slider-track {
    background-color: $primary-500;
  }
  
  .ant-slider-handle {
    border-color: $primary-500;
  }
}

// 评分样式
.ant-rate {
  .ant-rate-star-full {
    color: $warning-500;
  }
}

// 时间选择器样式
.ant-picker {
  border-radius: $border-radius;
  
  &:hover,
  &:focus {
    border-color: $primary-500;
  }
}

// 上传组件样式
.ant-upload {
  .ant-upload-drag {
    border-radius: $border-radius-lg;
    
    &:hover {
      border-color: $primary-500;
    }
  }
}

// 暗色主题适配
:global([data-theme="dark"]) {
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: $gray-800;
      color: $gray-100;
    }
  }
  
  .ant-card {
    background-color: $gray-800;
    border-color: $gray-700;
    
    .ant-card-head {
      background-color: $gray-800;
      border-bottom-color: $gray-700;
      
      .ant-card-head-title {
        color: $gray-100;
      }
    }
  }
  
  .ant-modal {
    .ant-modal-content {
      background-color: $gray-800;
    }
    
    .ant-modal-header {
      background-color: $gray-800;
      border-bottom-color: $gray-700;
      
      .ant-modal-title {
        color: $gray-100;
      }
    }
  }
}
