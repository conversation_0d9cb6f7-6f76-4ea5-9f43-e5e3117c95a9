// 工具类样式 - 提供常用的原子化CSS类

// ==================== 间距工具类 ====================

// 内边距
@for $i from 0 through 16 {
  .p-#{$i} { padding: #{$i * 0.25}rem !important; }
  .px-#{$i} { 
    padding-left: #{$i * 0.25}rem !important; 
    padding-right: #{$i * 0.25}rem !important; 
  }
  .py-#{$i} { 
    padding-top: #{$i * 0.25}rem !important; 
    padding-bottom: #{$i * 0.25}rem !important; 
  }
  .pt-#{$i} { padding-top: #{$i * 0.25}rem !important; }
  .pr-#{$i} { padding-right: #{$i * 0.25}rem !important; }
  .pb-#{$i} { padding-bottom: #{$i * 0.25}rem !important; }
  .pl-#{$i} { padding-left: #{$i * 0.25}rem !important; }
}

// 外边距
@for $i from 0 through 16 {
  .m-#{$i} { margin: #{$i * 0.25}rem !important; }
  .mx-#{$i} { 
    margin-left: #{$i * 0.25}rem !important; 
    margin-right: #{$i * 0.25}rem !important; 
  }
  .my-#{$i} { 
    margin-top: #{$i * 0.25}rem !important; 
    margin-bottom: #{$i * 0.25}rem !important; 
  }
  .mt-#{$i} { margin-top: #{$i * 0.25}rem !important; }
  .mr-#{$i} { margin-right: #{$i * 0.25}rem !important; }
  .mb-#{$i} { margin-bottom: #{$i * 0.25}rem !important; }
  .ml-#{$i} { margin-left: #{$i * 0.25}rem !important; }
}

// 自动边距
.mx-auto { margin-left: auto !important; margin-right: auto !important; }
.ml-auto { margin-left: auto !important; }
.mr-auto { margin-right: auto !important; }

// ==================== 文本工具类 ====================

// 字体大小
.text-xs { font-size: $text-xs !important; }
.text-sm { font-size: $text-sm !important; }
.text-base { font-size: $text-base !important; }
.text-lg { font-size: $text-lg !important; }
.text-xl { font-size: $text-xl !important; }
.text-2xl { font-size: $text-2xl !important; }
.text-3xl { font-size: $text-3xl !important; }
.text-4xl { font-size: $text-4xl !important; }

// 字体颜色
.text-primary { color: $text-primary !important; }
.text-secondary { color: $text-secondary !important; }
.text-tertiary { color: $text-tertiary !important; }
.text-success { color: $success-500 !important; }
.text-warning { color: $warning-500 !important; }
.text-error { color: $error-500 !important; }
.text-white { color: #ffffff !important; }

// 字体重量
.font-normal { font-weight: $font-normal !important; }
.font-medium { font-weight: $font-medium !important; }
.font-semibold { font-weight: $font-semibold !important; }
.font-bold { font-weight: $font-bold !important; }

// 文本对齐
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// 文本装饰
.underline { text-decoration: underline !important; }
.line-through { text-decoration: line-through !important; }
.no-underline { text-decoration: none !important; }

// 文本转换
.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }
.normal-case { text-transform: none !important; }

// 文本截断
.truncate {
  @include text-truncate;
}

.truncate-2 {
  @include text-truncate-lines(2);
}

.truncate-3 {
  @include text-truncate-lines(3);
}

// ==================== 布局工具类 ====================

// 显示类型
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.inline { display: inline !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }
.hidden { display: none !important; }

// Flexbox
.flex-row { flex-direction: row !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

.flex-grow { flex-grow: 1 !important; }
.flex-shrink { flex-shrink: 1 !important; }

// ==================== 定位工具类 ====================

.static { position: static !important; }
.fixed { position: fixed !important; }
.absolute { position: absolute !important; }
.relative { position: relative !important; }
.sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

.inset-0 { 
  top: 0 !important; 
  right: 0 !important; 
  bottom: 0 !important; 
  left: 0 !important; 
}

// ==================== 尺寸工具类 ====================

.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.w-fit { width: fit-content !important; }

.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.h-screen { height: 100vh !important; }

.min-h-0 { min-height: 0 !important; }
.min-h-full { min-height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

.max-w-none { max-width: none !important; }
.max-w-xs { max-width: 20rem !important; }
.max-w-sm { max-width: 24rem !important; }
.max-w-md { max-width: 28rem !important; }
.max-w-lg { max-width: 32rem !important; }
.max-w-xl { max-width: 36rem !important; }
.max-w-2xl { max-width: 42rem !important; }
.max-w-full { max-width: 100% !important; }

// ==================== 背景工具类 ====================

.bg-transparent { background-color: transparent !important; }
.bg-primary { background-color: $bg-primary !important; }
.bg-secondary { background-color: $bg-secondary !important; }
.bg-tertiary { background-color: $bg-tertiary !important; }

.bg-success { background-color: $success-500 !important; }
.bg-warning { background-color: $warning-500 !important; }
.bg-error { background-color: $error-500 !important; }

// ==================== 边框工具类 ====================

.border { border: 1px solid $border-light !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid $border-light !important; }
.border-r { border-right: 1px solid $border-light !important; }
.border-b { border-bottom: 1px solid $border-light !important; }
.border-l { border-left: 1px solid $border-light !important; }

.border-light { border-color: $border-light !important; }
.border-medium { border-color: $border-medium !important; }
.border-dark { border-color: $border-dark !important; }

// ==================== 圆角工具类 ====================

.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: $radius-sm !important; }
.rounded { border-radius: $radius-md !important; }
.rounded-lg { border-radius: $radius-lg !important; }
.rounded-xl { border-radius: $radius-xl !important; }
.rounded-2xl { border-radius: $radius-2xl !important; }
.rounded-full { border-radius: 50% !important; }

.rounded-t-none { border-top-left-radius: 0 !important; border-top-right-radius: 0 !important; }
.rounded-r-none { border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important; }
.rounded-b-none { border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; }
.rounded-l-none { border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; }

// ==================== 阴影工具类 ====================

.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: $shadow-sm !important; }
.shadow { box-shadow: $shadow-md !important; }
.shadow-lg { box-shadow: $shadow-lg !important; }
.shadow-xl { box-shadow: $shadow-xl !important; }

// ==================== 透明度工具类 ====================

.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

// ==================== 过渡工具类 ====================

.transition { @include transition(all); }
.transition-none { transition: none !important; }
.transition-fast { @include transition(all, $transition-fast); }
.transition-slow { @include transition(all, $transition-slow); }

// ==================== 光标工具类 ====================

.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

// ==================== 用户选择工具类 ====================

.select-none { @include no-select; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }
.select-auto { user-select: auto !important; }

// ==================== 溢出工具类 ====================

.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

// ==================== Z-index工具类 ====================

.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }
.z-auto { z-index: auto !important; }
