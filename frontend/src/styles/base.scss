// 基础样式重置和全局样式

// ==================== CSS重置 ====================

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  font-family: $font-family-sans;
  font-size: $text-base;
  line-height: $leading-normal;
  color: $text-primary;
  background-color: $bg-secondary;
  @include transition(background-color);
  
  // 防止水平滚动
  overflow-x: hidden;
}

// ==================== 基础元素样式 ====================

h1, h2, h3, h4, h5, h6 {
  font-weight: $font-semibold;
  line-height: $leading-tight;
  color: $text-primary;
  margin-bottom: $space-4;
}

h1 { font-size: $text-4xl; }
h2 { font-size: $text-3xl; }
h3 { font-size: $text-2xl; }
h4 { font-size: $text-xl; }
h5 { font-size: $text-lg; }
h6 { font-size: $text-base; }

p {
  margin-bottom: $space-4;
  color: $text-secondary;
}

a {
  color: $primary-500;
  text-decoration: none;
  @include transition(color);
  
  &:hover {
    color: $primary-600;
    text-decoration: underline;
  }
  
  &:focus {
    outline: 2px solid $primary-500;
    outline-offset: 2px;
  }
}

ul, ol {
  margin-bottom: $space-4;
  padding-left: $space-6;
}

li {
  margin-bottom: $space-1;
}

blockquote {
  border-left: 4px solid $primary-500;
  padding-left: $space-4;
  margin: $space-4 0;
  font-style: italic;
  color: $text-secondary;
}

code {
  font-family: $font-family-mono;
  font-size: 0.875em;
  background-color: $bg-tertiary;
  padding: $space-1 $space-2;
  border-radius: $radius-sm;
  color: $primary-600;
}

pre {
  font-family: $font-family-mono;
  background-color: $bg-tertiary;
  padding: $space-4;
  border-radius: $radius-md;
  overflow-x: auto;
  margin-bottom: $space-4;
  
  code {
    background: none;
    padding: 0;
  }
}

hr {
  border: none;
  height: 1px;
  background-color: $border-light;
  margin: $space-8 0;
}

// ==================== 表单元素样式 ====================

input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// ==================== 图片和媒体 ====================

img {
  max-width: 100%;
  height: auto;
  display: block;
}

svg {
  display: inline-block;
  vertical-align: middle;
}

// ==================== 表格样式 ====================

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: $space-4;
}

th, td {
  padding: $space-3 $space-4;
  text-align: left;
  border-bottom: 1px solid $border-light;
}

th {
  background-color: $bg-tertiary;
  font-weight: $font-semibold;
  color: $text-primary;
}

// ==================== 辅助类 ====================

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.clearfix {
  @include clearfix;
}

.no-scroll {
  overflow: hidden;
}

.full-height {
  height: 100vh;
}

.full-width {
  width: 100%;
}

// ==================== 响应式图片 ====================

.responsive-img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

// ==================== 容器样式 ====================

.container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $content-padding;
}

.section {
  padding: $section-spacing 0;
}

// ==================== 打印样式 ====================

@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  @page {
    margin: 0.5in;
  }
  
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2, h3 {
    page-break-after: avoid;
  }
}
