// SCSS混入 - 可复用的样式片段
@use 'sass:map';
@use 'sass:color';

// ==================== 响应式混入 ====================

@mixin responsive($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 移动端优先的响应式混入
@mixin mobile-first($breakpoint) {
  @include responsive($breakpoint) {
    @content;
  }
}

// 桌面端优先的响应式混入
@mixin desktop-first($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: map.get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  }
}

// ==================== 布局混入 ====================

// Flexbox居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// Flexbox水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// Flexbox两端对齐
@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 固定宽高比
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// ==================== 文本混入 ====================

// 文本截断
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本截断
@mixin text-truncate-lines($lines) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 文本选择禁用
@mixin no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// ==================== 视觉效果混入 ====================

// 阴影
@mixin shadow($level: 'md') {
  @if $level == 'sm' {
    box-shadow: $shadow-sm;
  } @else if $level == 'md' {
    box-shadow: $shadow-md;
  } @else if $level == 'lg' {
    box-shadow: $shadow-lg;
  } @else if $level == 'xl' {
    box-shadow: $shadow-xl;
  }
}

// 悬停阴影效果
@mixin hover-shadow($from: 'sm', $to: 'md') {
  @include shadow($from);
  transition: box-shadow $transition-normal;
  
  &:hover {
    @include shadow($to);
  }
}

// 圆角
@mixin border-radius($size: 'md') {
  @if $size == 'sm' {
    border-radius: $radius-sm;
  } @else if $size == 'md' {
    border-radius: $radius-md;
  } @else if $size == 'lg' {
    border-radius: $radius-lg;
  } @else if $size == 'xl' {
    border-radius: $radius-xl;
  } @else if $size == '2xl' {
    border-radius: $radius-2xl;
  } @else if $size == 'full' {
    border-radius: 50%;
  }
}

// 渐变背景
@mixin gradient-bg($from, $to, $direction: 'to right') {
  background: linear-gradient(#{$direction}, $from, $to);
}

// ==================== 动画混入 ====================

// 过渡动画
@mixin transition($properties: all, $duration: $transition-normal, $easing: ease-in-out) {
  transition: $properties $duration $easing;
}

// 悬停缩放效果
@mixin hover-scale($scale: 1.05) {
  @include transition(transform);
  
  &:hover {
    transform: scale($scale);
  }
}

// 淡入动画
@mixin fade-in($duration: $transition-normal) {
  opacity: 0;
  animation: fadeIn $duration ease-in-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

// 滑入动画
@mixin slide-in($direction: 'up', $distance: 20px, $duration: $transition-normal) {
  opacity: 0;
  
  @if $direction == 'up' {
    transform: translateY($distance);
  } @else if $direction == 'down' {
    transform: translateY(-$distance);
  } @else if $direction == 'left' {
    transform: translateX($distance);
  } @else if $direction == 'right' {
    transform: translateX(-$distance);
  }
  
  animation: slideIn#{capitalize($direction)} $duration ease-out forwards;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// ==================== 表单混入 ====================

// 输入框样式
@mixin input-style {
  padding: $space-2 $space-3;
  border: 1px solid $input-border;
  border-radius: $radius-md;
  font-size: $text-sm;
  line-height: $leading-normal;
  @include transition(border-color);
  
  &:focus {
    outline: none;
    border-color: $input-focus-border;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }
  
  &:disabled {
    background-color: $bg-secondary;
    color: $text-tertiary;
    cursor: not-allowed;
  }
}

// 按钮样式
@mixin button-style($variant: 'primary', $size: 'md') {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: $font-medium;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  @include transition(all);
  @include border-radius();
  
  // 尺寸
  @if $size == 'sm' {
    padding: $space-1 $space-3;
    font-size: $text-xs;
    height: $button-height-sm;
  } @else if $size == 'md' {
    padding: $space-2 $space-4;
    font-size: $text-sm;
    height: $button-height-md;
  } @else if $size == 'lg' {
    padding: $space-3 $space-6;
    font-size: $text-base;
    height: $button-height-lg;
  }
  
  // 变体
  @if $variant == 'primary' {
    background-color: $primary-500;
    color: white;
    
    &:hover {
      background-color: $primary-600;
    }
    
    &:active {
      background-color: $primary-700;
    }
  } @else if $variant == 'secondary' {
    background-color: $bg-secondary;
    color: $text-primary;
    border-color: $border-medium;
    
    &:hover {
      background-color: $bg-tertiary;
    }
  } @else if $variant == 'outline' {
    background-color: transparent;
    color: $primary-500;
    border-color: $primary-500;
    
    &:hover {
      background-color: $primary-50;
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: inherit;
    }
  }
}

// ==================== 表格混入 ====================

// 表格样式
@mixin table-style {
  width: 100%;
  border-collapse: collapse;
  
  th,
  td {
    padding: $space-3 $space-4;
    text-align: left;
    border-bottom: 1px solid $table-border;
  }
  
  th {
    background-color: $table-header-bg;
    font-weight: $font-semibold;
    color: $text-primary;
  }
  
  tbody tr {
    @include transition(background-color);
    
    &:hover {
      background-color: $table-row-hover-bg;
    }
  }
}

// ==================== 卡片混入 ====================

// 卡片样式
@mixin card-style {
  background-color: $card-bg;
  border: 1px solid $card-border;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
}

// ==================== 工具混入 ====================

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 自定义滚动条
@mixin custom-scrollbar($width: 8px, $track-color: $bg-secondary, $thumb-color: $gray-400) {
  &::-webkit-scrollbar {
    width: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: color.adjust($thumb-color, $lightness: -10%);
    }
  }
}
