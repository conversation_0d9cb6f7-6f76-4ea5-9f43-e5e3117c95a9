<!-- 现代化图表组件 - 基于ECharts -->
<template>
  <div class="chart-container">
    <!-- 图表工具栏 -->
    <div v-if="showToolbar" class="chart-toolbar">
      <div class="chart-title">
        <h3 v-if="title">{{ title }}</h3>
        <p v-if="subtitle" class="chart-subtitle">{{ subtitle }}</p>
      </div>
      
      <div class="chart-actions">
        <a-space>
          <!-- 图表类型切换 -->
          <a-select
            v-if="allowTypeChange"
            v-model:value="currentType"
            :options="chartTypeOptions"
            @change="handleTypeChange"
            style="width: 120px"
            size="small"
          />
          
          <!-- 刷新按钮 -->
          <a-button @click="handleRefresh" :loading="loading" size="small">
            <ReloadOutlined />
          </a-button>
          
          <!-- 全屏按钮 -->
          <a-button @click="toggleFullscreen" size="small">
            <FullscreenOutlined v-if="!isFullscreen" />
            <FullscreenExitOutlined v-else />
          </a-button>
          
          <!-- 导出菜单 -->
          <a-dropdown>
            <a-button size="small">
              <DownloadOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleExport">
                <a-menu-item key="png">导出PNG</a-menu-item>
                <a-menu-item key="jpg">导出JPG</a-menu-item>
                <a-menu-item key="svg">导出SVG</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 图表内容 -->
    <div 
      ref="chartRef" 
      class="chart-content"
      :class="{ 'fullscreen': isFullscreen }"
      :style="{ height: chartHeight }"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="chart-loading">
        <a-spin size="large">
          <template #indicator>
            <LoadingOutlined style="font-size: 24px" spin />
          </template>
        </a-spin>
        <p>图表加载中...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="chart-error">
        <ExclamationCircleOutlined />
        <p>{{ error }}</p>
        <a-button @click="handleRefresh" type="primary" size="small">
          重新加载
        </a-button>
      </div>
      
      <!-- 空数据状态 -->
      <div v-else-if="!hasData" class="chart-empty">
        <InboxOutlined />
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts/core';
import {
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import {
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  DownloadOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
  InboxOutlined
} from '@ant-design/icons-vue';

// 注册ECharts组件
echarts.use([
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
  CanvasRenderer
]);

// 图表类型定义
export type ChartType = 'line' | 'bar' | 'pie' | 'scatter' | 'radar' | 'gauge';

// Props定义
interface Props {
  type: ChartType;
  data: any;
  options?: any;
  title?: string;
  subtitle?: string;
  height?: string | number;
  loading?: boolean;
  error?: string;
  showToolbar?: boolean;
  allowTypeChange?: boolean;
  theme?: 'light' | 'dark' | 'auto';
}

const props = withDefaults(defineProps<Props>(), {
  type: 'line',
  height: '400px',
  loading: false,
  showToolbar: true,
  allowTypeChange: true,
  theme: 'auto'
});

// Emits定义
const emit = defineEmits<{
  refresh: [];
  typeChange: [type: ChartType];
  export: [format: string];
}>();

// 响应式数据
const chartRef = ref<HTMLElement>();
const chartInstance = ref<echarts.ECharts>();
const currentType = ref<ChartType>(props.type);
const isFullscreen = ref(false);

// 计算属性
const chartHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`;
  }
  return props.height;
});

const hasData = computed(() => {
  return props.data && 
         props.data.labels && 
         props.data.labels.length > 0 &&
         props.data.datasets && 
         props.data.datasets.length > 0;
});

const chartTypeOptions = computed(() => [
  { label: '折线图', value: 'line' },
  { label: '柱状图', value: 'bar' },
  { label: '饼图', value: 'pie' },
  { label: '散点图', value: 'scatter' },
  { label: '雷达图', value: 'radar' },
  { label: '仪表盘', value: 'gauge' }
]);

// 图表配置生成
const generateChartOption = () => {
  const baseOption = {
    backgroundColor: 'transparent',
    textStyle: {
      fontFamily: 'Inter, PingFang SC, Microsoft YaHei, sans-serif'
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      extraCssText: 'box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);'
    },
    legend: {
      top: 20,
      textStyle: {
        color: '#6b7280'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  };

  // 根据图表类型生成特定配置
  switch (currentType.value) {
    case 'line':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.labels || [],
          axisLine: { lineStyle: { color: '#e5e7eb' } },
          axisLabel: { color: '#6b7280' }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#e5e7eb' } },
          axisLabel: { color: '#6b7280' },
          splitLine: { lineStyle: { color: '#f3f4f6' } }
        },
        series: props.data.datasets?.map((dataset: any) => ({
          name: dataset.label,
          type: 'line',
          data: dataset.data,
          smooth: true,
          lineStyle: { width: 3 },
          itemStyle: { 
            color: dataset.borderColor || '#3b82f6',
            borderRadius: 4 
          },
          areaStyle: dataset.fill ? {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: dataset.backgroundColor || 'rgba(59, 130, 246, 0.3)'
              }, {
                offset: 1,
                color: dataset.backgroundColor || 'rgba(59, 130, 246, 0.1)'
              }]
            }
          } : undefined
        })) || []
      };

    case 'bar':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.labels || [],
          axisLine: { lineStyle: { color: '#e5e7eb' } },
          axisLabel: { color: '#6b7280' }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#e5e7eb' } },
          axisLabel: { color: '#6b7280' },
          splitLine: { lineStyle: { color: '#f3f4f6' } }
        },
        series: props.data.datasets?.map((dataset: any) => ({
          name: dataset.label,
          type: 'bar',
          data: dataset.data,
          itemStyle: { 
            color: dataset.backgroundColor || '#3b82f6',
            borderRadius: [4, 4, 0, 0]
          }
        })) || []
      };

    case 'pie':
      return {
        ...baseOption,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: props.title || '数据分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '60%'],
          data: props.data.labels?.map((label: string, index: number) => ({
            name: label,
            value: props.data.datasets?.[0]?.data?.[index] || 0,
            itemStyle: {
              color: Array.isArray(props.data.datasets?.[0]?.backgroundColor) 
                ? props.data.datasets[0].backgroundColor[index]
                : props.data.datasets?.[0]?.backgroundColor || '#3b82f6'
            }
          })) || [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            formatter: '{b}: {c} ({d}%)'
          }
        }]
      };

    default:
      return baseOption;
  }
};

// 方法
const initChart = async () => {
  if (!chartRef.value || !hasData.value) return;

  await nextTick();
  
  // 销毁现有实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }

  // 创建新实例
  chartInstance.value = echarts.init(chartRef.value, props.theme === 'dark' ? 'dark' : undefined);
  
  // 设置配置
  const option = { ...generateChartOption(), ...props.options };
  chartInstance.value.setOption(option);

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
};

const handleResize = () => {
  chartInstance.value?.resize();
};

const handleRefresh = () => {
  emit('refresh');
};

const handleTypeChange = (type: ChartType) => {
  currentType.value = type;
  emit('typeChange', type);
  initChart();
};

const handleExport = ({ key }: { key: string }) => {
  if (!chartInstance.value) return;

  const url = chartInstance.value.getDataURL({
    type: key === 'jpg' ? 'jpeg' : key,
    pixelRatio: 2,
    backgroundColor: '#fff'
  });

  const link = document.createElement('a');
  link.download = `chart.${key}`;
  link.href = url;
  link.click();

  emit('export', key);
};

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  setTimeout(() => {
    handleResize();
  }, 300);
};

// 监听器
watch(() => props.data, () => {
  initChart();
}, { deep: true });

watch(() => props.type, (newType) => {
  currentType.value = newType;
  initChart();
});

watch(() => props.theme, () => {
  initChart();
});

// 生命周期
onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
.chart-container {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
}

.chart-toolbar {
  @include flex-between;
  padding: $space-4;
  border-bottom: 1px solid $border-light;
  background: $bg-secondary;

  .chart-title {
    h3 {
      margin: 0;
      font-size: $text-lg;
      font-weight: $font-semibold;
      color: $text-primary;
    }

    .chart-subtitle {
      margin: $space-1 0 0;
      font-size: $text-sm;
      color: $text-secondary;
    }
  }

  .chart-actions {
    flex-shrink: 0;
  }
}

.chart-content {
  position: relative;
  @include transition(all);

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: $bg-primary;
    height: 100vh !important;
  }
}

.chart-loading,
.chart-error,
.chart-empty {
  @include flex-center;
  flex-direction: column;
  height: 100%;
  color: $text-secondary;

  p {
    margin: $space-3 0;
    font-size: $text-sm;
  }

  .anticon {
    font-size: 48px;
    color: $text-tertiary;
  }
}

.chart-error {
  color: $error-500;

  .anticon {
    color: $error-500;
  }
}

// 响应式设计
@include responsive('md') {
  .chart-toolbar {
    flex-direction: column;
    gap: $space-3;

    .chart-actions {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
