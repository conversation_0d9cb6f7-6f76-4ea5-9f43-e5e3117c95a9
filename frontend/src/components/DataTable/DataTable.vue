<!-- 现代化数据表格组件 - 替换现有的DataTables -->
<template>
  <div class="data-table-container">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <div v-if="searchable" class="search-box">
            <a-input
              v-model:value="searchValue"
              placeholder="搜索..."
              allow-clear
              @change="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </div>
        </slot>
      </div>
      
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <a-space>
            <!-- 列设置 -->
            <a-dropdown v-if="columnSettable">
              <a-button>
                <SettingOutlined />
                列设置
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    v-for="col in columns"
                    :key="col.key"
                  >
                    <a-checkbox 
                      :checked="!hiddenColumns.includes(col.key as string)"
                      @change="() => toggleColumn(col.key as string)"
                    >
                      {{ col.title }}
                    </a-checkbox>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            
            <!-- 导出按钮 -->
            <a-dropdown v-if="exportable">
              <a-button>
                <DownloadOutlined />
                导出
              </a-button>
              <template #overlay>
                <a-menu @click="handleExport">
                  <a-menu-item key="excel">
                    <FileExcelOutlined />
                    导出Excel
                  </a-menu-item>
                  <a-menu-item key="csv">
                    <FileTextOutlined />
                    导出CSV
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            
            <!-- 刷新按钮 -->
            <a-button @click="handleRefresh" :loading="loading">
              <ReloadOutlined />
              刷新
            </a-button>
          </a-space>
        </slot>
      </div>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="visibleColumns"
      :data-source="filteredData"
      :loading="loading"
      :pagination="paginationConfig"
      :scroll="scroll"
      :row-selection="rowSelection"
      :row-key="rowKey"
      :size="size"
      :bordered="bordered"
      @change="handleTableChange"
      class="modern-table"
    >
      <!-- 自定义列渲染 -->
      <template #bodyCell="{ column, record, index }">
        <slot
          name="cell"
          :column="column"
          :record="record"
          :index="index"
          :value="record[column.dataIndex]"
        >
          <!-- 默认单元格渲染 -->
          <template v-if="column.dataIndex === 'status'">
            <StatusTag :status="record[column.dataIndex]" />
          </template>
          
          <template v-else-if="column.dataIndex === 'amount'">
            <AmountCell :amount="record[column.dataIndex]" />
          </template>
          
          <template v-else-if="column.dataIndex === 'date'">
            <DateCell :date="record[column.dataIndex]" />
          </template>
          
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </slot>
      </template>

      <!-- 展开行内容 -->
      <template #expandedRowRender="{ record }" v-if="expandable">
        <slot name="expanded-row" :record="record">
          <div class="expanded-content">
            <a-descriptions :column="2" size="small">
              <a-descriptions-item
                v-for="(value, key) in getExpandedData(record)"
                :key="key"
                :label="key"
              >
                {{ value }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </slot>
      </template>
    </a-table>

    <!-- 表格统计信息 -->
    <div v-if="showFooter" class="table-footer">
      <div class="table-summary">
        <slot name="summary" :data="filteredData" :selected="selectedRows">
          <span class="summary-text">
            共 {{ filteredData.length }} 条记录
            <template v-if="selectedRows.length > 0">
              ，已选择 {{ selectedRows.length }} 条
            </template>
          </span>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import {
  SearchOutlined,
  SettingOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';

// 导入子组件
import StatusTag from './StatusTag.vue';
import AmountCell from './AmountCell.vue';
import DateCell from './DateCell.vue';

// Props定义
interface Props {
  columns: TableColumnsType;
  dataSource: any[];
  loading?: boolean;
  searchable?: boolean;
  exportable?: boolean;
  columnSettable?: boolean;
  expandable?: boolean;
  showToolbar?: boolean;
  showFooter?: boolean;
  rowKey?: string | ((record: any) => string);
  size?: 'small' | 'middle' | 'large';
  bordered?: boolean;
  scroll?: { x?: number; y?: number };
  pagination?: false | TableProps['pagination'];
  rowSelection?: TableProps['rowSelection'];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: true,
  exportable: true,
  columnSettable: true,
  expandable: false,
  showToolbar: true,
  showFooter: true,
  rowKey: 'id',
  size: 'middle',
  bordered: true,
  pagination: () => ({
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    pageSizeOptions: ['10', '20', '50', '100'],
    defaultPageSize: 20
  })
});

// Emits定义
const emit = defineEmits<{
  refresh: [];
  export: [format: 'excel' | 'csv'];
  change: [pagination: any, filters: any, sorter: any];
  select: [selectedRowKeys: any[], selectedRows: any[]];
}>();

// 响应式数据
const searchValue = ref('');
const hiddenColumns = ref<string[]>([]);
const selectedRows = ref<any[]>([]);

// 计算属性
const visibleColumns = computed(() => {
  return props.columns.filter(col => !hiddenColumns.value.includes(col.key as string));
});

const filteredData = computed(() => {
  if (!searchValue.value) return props.dataSource;
  
  const searchTerm = searchValue.value.toLowerCase();
  return props.dataSource.filter(record => {
    return Object.values(record).some(value => 
      String(value).toLowerCase().includes(searchTerm)
    );
  });
});

const paginationConfig = computed(() => {
  if (props.pagination === false) return false;
  
  return {
    ...props.pagination,
    total: filteredData.value.length
  };
});

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const handleRefresh = () => {
  emit('refresh');
};

const handleExport = ({ key }: { key: string }) => {
  emit('export', key as 'excel' | 'csv');
};

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  emit('change', pagination, filters, sorter);
};

const toggleColumn = (columnKey: string) => {
  const index = hiddenColumns.value.indexOf(columnKey);
  if (index > -1) {
    hiddenColumns.value.splice(index, 1);
  } else {
    hiddenColumns.value.push(columnKey);
  }
};

const getExpandedData = (record: any) => {
  // 返回需要在展开行中显示的数据
  const excludeKeys = ['id', 'key'];
  return Object.fromEntries(
    Object.entries(record).filter(([key]) => !excludeKeys.includes(key))
  );
};

// 行选择配置
const rowSelection = computed(() => {
  if (!props.rowSelection) return undefined;
  
  return {
    ...props.rowSelection,
    onChange: (selectedRowKeys: any[], rows: any[]) => {
      selectedRows.value = rows;
      emit('select', selectedRowKeys, rows);
    }
  };
});
</script>

<style lang="scss" scoped>
.data-table-container {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  overflow: hidden;
}

.table-toolbar {
  @include flex-between;
  padding: $space-4;
  border-bottom: 1px solid $border-light;
  background: $bg-secondary;

  .toolbar-left {
    flex: 1;
    
    .search-box {
      max-width: 300px;
    }
  }

  .toolbar-right {
    flex-shrink: 0;
  }
}

.modern-table {
  :deep(.ant-table) {
    font-size: $text-sm;
    
    .ant-table-thead > tr > th {
      background: $table-header-bg;
      font-weight: $font-semibold;
      color: $text-primary;
      border-bottom: 2px solid $border-medium;
    }
    
    .ant-table-tbody > tr > td {
      border-bottom: 1px solid $border-light;
      @include transition(background-color);
    }
    
    .ant-table-tbody > tr:hover > td {
      background: $table-row-hover-bg;
    }
  }
}

.table-footer {
  padding: $space-3 $space-4;
  background: $bg-secondary;
  border-top: 1px solid $border-light;
  
  .table-summary {
    @include flex-between;
    
    .summary-text {
      font-size: $text-sm;
      color: $text-secondary;
    }
  }
}

.expanded-content {
  padding: $space-4;
  background: $bg-secondary;
  @include border-radius();
  margin: $space-2;
}

// 响应式设计
@include responsive('md') {
  .table-toolbar {
    flex-direction: column;
    gap: $space-3;
    
    .toolbar-left,
    .toolbar-right {
      width: 100%;
    }
    
    .search-box {
      max-width: none;
    }
  }
}
</style>
