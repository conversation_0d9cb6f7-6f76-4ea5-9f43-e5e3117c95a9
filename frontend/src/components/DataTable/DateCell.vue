<!-- 日期单元格组件 -->
<template>
  <span class="date-cell">
    <CalendarOutlined v-if="showIcon" class="date-icon" />
    <span class="date-text">{{ formattedDate }}</span>
    <span v-if="showRelative" class="date-relative">{{ relativeTime }}</span>
  </span>
</template>

<script setup lang="ts">
import { CalendarOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface Props {
  date: string | Date;
  format?: string;
  showIcon?: boolean;
  showRelative?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  format: 'YYYY-MM-DD',
  showIcon: true,
  showRelative: false
});

// 计算属性
const dateObj = computed(() => {
  if (!props.date) return null;
  return dayjs(props.date);
});

const formattedDate = computed(() => {
  if (!dateObj.value || !dateObj.value.isValid()) {
    return '无效日期';
  }
  return dateObj.value.format(props.format);
});

const relativeTime = computed(() => {
  if (!dateObj.value || !dateObj.value.isValid()) {
    return '';
  }
  return dateObj.value.fromNow();
});
</script>

<style lang="scss" scoped>
.date-cell {
  display: inline-flex;
  align-items: center;
  gap: $space-1;
  font-size: $text-sm;
  
  .date-icon {
    color: $text-tertiary;
    font-size: $text-xs;
  }
  
  .date-text {
    color: $text-primary;
  }
  
  .date-relative {
    color: $text-tertiary;
    font-size: $text-xs;
    margin-left: $space-1;
  }
}
</style>
