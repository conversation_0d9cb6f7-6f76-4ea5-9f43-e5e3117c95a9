<!-- 金额单元格组件 -->
<template>
  <span :class="amountClass" class="amount-cell">
    <span v-if="showCurrency" class="currency">¥</span>
    {{ formattedAmount }}
  </span>
</template>

<script setup lang="ts">
interface Props {
  amount: number | string;
  showCurrency?: boolean;
  precision?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showCurrency: true,
  precision: 2
});

// 计算属性
const numericAmount = computed(() => {
  const num = typeof props.amount === 'string' ? parseFloat(props.amount) : props.amount;
  return isNaN(num) ? 0 : num;
});

const formattedAmount = computed(() => {
  return numericAmount.value.toLocaleString('zh-CN', {
    minimumFractionDigits: props.precision,
    maximumFractionDigits: props.precision
  });
});

const amountClass = computed(() => {
  const amount = numericAmount.value;
  
  if (amount > 0) {
    return 'amount-positive';
  } else if (amount < 0) {
    return 'amount-negative';
  } else {
    return 'amount-zero';
  }
});
</script>

<style lang="scss" scoped>
.amount-cell {
  font-family: $font-family-mono;
  font-weight: $font-medium;
  
  .currency {
    margin-right: $space-1;
    opacity: 0.7;
  }
  
  &.amount-positive {
    color: $amount-positive;
  }
  
  &.amount-negative {
    color: $amount-negative;
  }
  
  &.amount-zero {
    color: $amount-zero;
  }
}
</style>
