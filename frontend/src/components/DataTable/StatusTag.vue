<!-- 状态标签组件 -->
<template>
  <a-tag :color="tagColor" class="status-tag">
    <component :is="statusIcon" v-if="statusIcon" class="status-icon" />
    {{ displayText }}
  </a-tag>
</template>

<script setup lang="ts">
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  MinusCircleOutlined
} from '@ant-design/icons-vue';

interface Props {
  status: string;
  showIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showIcon: true
});

// 状态配置映射
const statusConfig = {
  '按时还款': {
    color: 'success',
    icon: CheckCircleOutlined,
    text: '按时还款'
  },
  '逾期未还': {
    color: 'error',
    icon: ExclamationCircleOutlined,
    text: '逾期未还'
  },
  '逾期还款': {
    color: 'warning',
    icon: WarningOutlined,
    text: '逾期还款'
  },
  '提前还款': {
    color: 'processing',
    icon: CheckCircleOutlined,
    text: '提前还款'
  },
  '账单日': {
    color: 'default',
    icon: ClockCircleOutlined,
    text: '账单日'
  }
};

// 计算属性
const config = computed(() => {
  return statusConfig[props.status as keyof typeof statusConfig] || {
    color: 'default',
    icon: MinusCircleOutlined,
    text: props.status || '未知状态'
  };
});

const tagColor = computed(() => config.value.color);
const statusIcon = computed(() => props.showIcon ? config.value.icon : null);
const displayText = computed(() => config.value.text);
</script>

<style lang="scss" scoped>
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: $space-1;
  font-size: $text-xs;
  font-weight: $font-medium;
  
  .status-icon {
    font-size: $text-xs;
  }
}
</style>
