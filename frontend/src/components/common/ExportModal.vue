<!-- 导出模态框组件 -->
<template>
  <a-modal
    v-model:visible="modalVisible"
    title="导出数据"
    :width="500"
    @ok="handleExport"
    @cancel="handleCancel"
  >
    <div class="export-modal-content">
      <!-- 导出格式选择 -->
      <div class="form-section">
        <h4>导出格式</h4>
        <a-radio-group v-model:value="exportFormat" class="format-options">
          <a-radio value="excel" class="format-option">
            <div class="option-content">
              <FileExcelOutlined class="option-icon" />
              <div class="option-info">
                <div class="option-title">Excel格式</div>
                <div class="option-desc">支持复杂格式，适合数据分析</div>
              </div>
            </div>
          </a-radio>
          
          <a-radio value="csv" class="format-option">
            <div class="option-content">
              <FileTextOutlined class="option-icon" />
              <div class="option-info">
                <div class="option-title">CSV格式</div>
                <div class="option-desc">纯文本格式，兼容性好</div>
              </div>
            </div>
          </a-radio>
        </a-radio-group>
      </div>

      <!-- 导出范围选择 -->
      <div class="form-section">
        <h4>导出范围</h4>
        <a-radio-group v-model:value="exportRange">
          <a-radio value="all">全部数据 ({{ totalCount }} 条)</a-radio>
          <a-radio value="current">当前页数据 ({{ currentPageCount }} 条)</a-radio>
          <a-radio value="selected" :disabled="selectedCount === 0">
            已选择数据 ({{ selectedCount }} 条)
          </a-radio>
        </a-radio-group>
      </div>

      <!-- 文件名设置 -->
      <div class="form-section">
        <h4>文件名</h4>
        <a-input
          v-model:value="fileName"
          placeholder="请输入文件名"
          :suffix="`.${exportFormat}`"
        />
      </div>

      <!-- 导出选项 -->
      <div class="form-section">
        <h4>导出选项</h4>
        <a-checkbox-group v-model:value="exportOptions" class="export-options">
          <a-checkbox value="includeHeader">包含表头</a-checkbox>
          <a-checkbox value="includeIndex">包含序号</a-checkbox>
          <a-checkbox value="formatNumbers">格式化数字</a-checkbox>
          <a-checkbox value="formatDates">格式化日期</a-checkbox>
        </a-checkbox-group>
      </div>

      <!-- 预览信息 -->
      <div class="preview-info">
        <a-alert
          :message="`将导出 ${getExportCount()} 条数据为 ${exportFormat.toUpperCase()} 格式`"
          type="info"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleExport" :loading="exporting">
          <DownloadOutlined />
          {{ exporting ? '导出中...' : '开始导出' }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import {
  FileExcelOutlined,
  FileTextOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';

interface Props {
  visible: boolean;
  data: any[];
  selectedData?: any[];
  filename?: string;
  currentPage?: number;
  pageSize?: number;
}

const props = withDefaults(defineProps<Props>(), {
  selectedData: () => [],
  filename: 'export_data',
  currentPage: 1,
  pageSize: 20
});

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  export: [format: 'excel' | 'csv', options: ExportOptions];
}>();

interface ExportOptions {
  format: 'excel' | 'csv';
  range: 'all' | 'current' | 'selected';
  filename: string;
  includeHeader: boolean;
  includeIndex: boolean;
  formatNumbers: boolean;
  formatDates: boolean;
  data: any[];
}

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const exportFormat = ref<'excel' | 'csv'>('excel');
const exportRange = ref<'all' | 'current' | 'selected'>('all');
const fileName = ref('');
const exportOptions = ref(['includeHeader', 'formatNumbers', 'formatDates']);
const exporting = ref(false);

// 计算属性
const totalCount = computed(() => props.data.length);

const currentPageCount = computed(() => {
  const start = (props.currentPage - 1) * props.pageSize;
  const end = start + props.pageSize;
  return Math.min(props.pageSize, totalCount.value - start);
});

const selectedCount = computed(() => props.selectedData.length);

const getExportCount = () => {
  switch (exportRange.value) {
    case 'all':
      return totalCount.value;
    case 'current':
      return currentPageCount.value;
    case 'selected':
      return selectedCount.value;
    default:
      return 0;
  }
};

const getExportData = () => {
  switch (exportRange.value) {
    case 'all':
      return props.data;
    case 'current':
      const start = (props.currentPage - 1) * props.pageSize;
      const end = start + props.pageSize;
      return props.data.slice(start, end);
    case 'selected':
      return props.selectedData;
    default:
      return [];
  }
};

// 方法
const handleExport = async () => {
  if (!fileName.value.trim()) {
    fileName.value = `export_${Date.now()}`;
  }

  exporting.value = true;
  
  try {
    const options: ExportOptions = {
      format: exportFormat.value,
      range: exportRange.value,
      filename: fileName.value,
      includeHeader: exportOptions.value.includes('includeHeader'),
      includeIndex: exportOptions.value.includes('includeIndex'),
      formatNumbers: exportOptions.value.includes('formatNumbers'),
      formatDates: exportOptions.value.includes('formatDates'),
      data: getExportData()
    };

    emit('export', exportFormat.value, options);
    
    // 延迟关闭模态框，给用户反馈时间
    setTimeout(() => {
      modalVisible.value = false;
    }, 1000);
    
  } catch (error) {
    console.error('导出失败:', error);
  } finally {
    exporting.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
};

// 监听visible变化，重置表单
watch(() => props.visible, (visible) => {
  if (visible) {
    // 重置表单
    exportFormat.value = 'excel';
    exportRange.value = 'all';
    fileName.value = props.filename || `export_${Date.now()}`;
    exportOptions.value = ['includeHeader', 'formatNumbers', 'formatDates'];
    exporting.value = false;
  }
});
</script>

<style lang="scss" scoped>
.export-modal-content {
  .form-section {
    margin-bottom: $space-6;
    
    h4 {
      font-size: $text-base;
      font-weight: $font-semibold;
      color: $text-primary;
      margin-bottom: $space-3;
    }
  }
  
  .format-options {
    width: 100%;
    
    .format-option {
      display: block;
      width: 100%;
      margin-bottom: $space-3;
      padding: $space-3;
      border: 1px solid $border-light;
      @include border-radius();
      @include transition(all);
      
      &:hover {
        border-color: $primary-500;
      }
      
      .option-content {
        @include flex-center-vertical;
        gap: $space-3;
        
        .option-icon {
          font-size: 24px;
          color: $primary-500;
        }
        
        .option-info {
          .option-title {
            font-weight: $font-medium;
            color: $text-primary;
            margin-bottom: $space-1;
          }
          
          .option-desc {
            font-size: $text-sm;
            color: $text-secondary;
          }
        }
      }
    }
  }
  
  .export-options {
    display: flex;
    flex-direction: column;
    gap: $space-2;
  }
  
  .preview-info {
    margin-top: $space-4;
  }
}

// 覆盖Ant Design样式
:deep(.ant-radio-wrapper) {
  width: 100%;
  margin: 0;
  
  .ant-radio {
    align-self: flex-start;
    margin-top: 2px;
  }
}

:deep(.ant-checkbox-group) {
  .ant-checkbox-wrapper {
    margin-bottom: $space-2;
  }
}
</style>
