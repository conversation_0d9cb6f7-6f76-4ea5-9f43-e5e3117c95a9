<!-- 应用主布局组件 -->
<template>
  <a-layout class="app-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="appStore.sidebarCollapsed"
      :trigger="null"
      collapsible
      :width="250"
      :collapsed-width="60"
      class="app-sidebar"
      :class="{ 'mobile-sidebar': appStore.isMobile }"
    >
      <!-- Logo区域 -->
      <div class="sidebar-logo">
        <img src="/logo.svg" alt="Logo" class="logo-image" />
        <span v-if="!appStore.sidebarCollapsed" class="logo-text">
          {{ appStore.appConfig.title }}
        </span>
      </div>

      <!-- 导航菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        class="sidebar-menu"
        @click="handleMenuClick"
      >
        <template v-for="route in menuRoutes" :key="route.name">
          <a-menu-item
            v-if="!route.children"
            :key="route.name"
            class="menu-item"
          >
            <component :is="getMenuIcon(route.meta?.icon)" />
            <span>{{ route.meta?.title }}</span>
          </a-menu-item>
          
          <a-sub-menu
            v-else
            :key="route.name"
            class="menu-submenu"
          >
            <template #icon>
              <component :is="getMenuIcon(route.meta?.icon)" />
            </template>
            <template #title>{{ route.meta?.title }}</template>
            
            <a-menu-item
              v-for="child in route.children"
              :key="child.name"
              class="menu-item"
            >
              <component :is="getMenuIcon(child.meta?.icon)" />
              <span>{{ child.meta?.title }}</span>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <a-layout class="app-main">
      <!-- 顶部导航栏 -->
      <a-layout-header class="app-header">
        <div class="header-left">
          <!-- 折叠按钮 -->
          <a-button
            type="text"
            @click="appStore.toggleSidebar"
            class="sidebar-trigger"
          >
            <MenuUnfoldOutlined v-if="appStore.sidebarCollapsed" />
            <MenuFoldOutlined v-else />
          </a-button>

          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.name"
            >
              <router-link v-if="item.path" :to="item.path">
                {{ item.title }}
              </router-link>
              <span v-else>{{ item.title }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 主题切换 -->
          <a-tooltip title="切换主题">
            <a-button
              type="text"
              @click="appStore.toggleTheme"
              class="theme-toggle"
            >
              <SunOutlined v-if="appStore.isDarkMode" />
              <MoonOutlined v-else />
            </a-button>
          </a-tooltip>

          <!-- 全屏切换 -->
          <a-tooltip title="全屏">
            <a-button
              type="text"
              @click="toggleFullscreen"
              class="fullscreen-toggle"
            >
              <FullscreenOutlined v-if="!isFullscreen" />
              <FullscreenExitOutlined v-else />
            </a-button>
          </a-tooltip>

          <!-- 用户菜单 -->
          <a-dropdown placement="bottomRight">
            <a-button type="text" class="user-menu">
              <a-avatar size="small" :src="userAvatar">
                <template #icon><UserOutlined /></template>
              </a-avatar>
              <span class="username">{{ userStore.userName }}</span>
              <DownOutlined />
            </a-button>
            
            <template #overlay>
              <a-menu @click="handleUserMenuClick">
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人中心
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="app-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component, route }">
            <transition name="page-transition" mode="out-in">
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </div>
      </a-layout-content>

      <!-- 底部信息 -->
      <a-layout-footer class="app-footer">
        <div class="footer-content">
          <span>{{ appStore.appConfig.title }} v{{ appStore.appConfig.version }}</span>
          <span>© 2024 太享科技. All rights reserved.</span>
        </div>
      </a-layout-footer>
    </a-layout>

    <!-- 移动端遮罩 -->
    <div
      v-if="appStore.isMobile && !appStore.sidebarCollapsed"
      class="mobile-overlay"
      @click="appStore.setSidebarCollapsed(true)"
    />
  </a-layout>
</template>

<script setup lang="ts">
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,
  SunOutlined,
  MoonOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  DashboardOutlined,
  SearchOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined,
  ToolOutlined
} from '@ant-design/icons-vue';
import { useAppStore } from '@/stores/app';
import { useUserStore } from '@/stores/user';
import { getMenuRoutes, getBreadcrumbs } from '@/router';
import { Modal } from 'ant-design-vue';

const router = useRouter();
const route = useRoute();
const appStore = useAppStore();
const userStore = useUserStore();

// 响应式数据
const selectedKeys = ref<string[]>([]);
const openKeys = ref<string[]>([]);
const isFullscreen = ref(false);
const userAvatar = ref('');

// 计算属性
const menuRoutes = computed(() => getMenuRoutes());
const breadcrumbs = computed(() => getBreadcrumbs(route));

// 图标映射
const iconMap = {
  DashboardOutlined,
  SearchOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined,
  ToolOutlined,
  SettingOutlined
};

// 方法
const getMenuIcon = (iconName?: string) => {
  if (!iconName) return UserOutlined;
  return iconMap[iconName as keyof typeof iconMap] || UserOutlined;
};

const handleMenuClick = ({ key }: { key: string }) => {
  router.push({ name: key });
  
  // 移动端点击菜单后自动折叠
  if (appStore.isMobile) {
    appStore.setSidebarCollapsed(true);
  }
};

const handleUserMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'profile':
      router.push({ name: 'Profile' });
      break;
    case 'settings':
      router.push({ name: 'SystemSettings' });
      break;
    case 'logout':
      handleLogout();
      break;
  }
};

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '您确定要退出系统吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await userStore.logout();
        router.push({ name: 'Login' });
      } catch (error) {
        console.error('Logout failed:', error);
      }
    }
  });
};

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
    isFullscreen.value = true;
  } else {
    document.exitFullscreen();
    isFullscreen.value = false;
  }
};

// 监听路由变化更新选中状态
watch(
  () => route.name,
  (newName) => {
    if (newName) {
      selectedKeys.value = [newName as string];
      
      // 展开父级菜单
      const parentRoute = menuRoutes.value.find(r => 
        r.children?.some(child => child.name === newName)
      );
      if (parentRoute) {
        openKeys.value = [parentRoute.name as string];
      }
    }
  },
  { immediate: true }
);

// 监听全屏状态变化
onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });
});
</script>

<style lang="scss" scoped>
.app-layout {
  min-height: 100vh;
}

.app-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: $z-fixed;
  @include transition(all);
  
  &.mobile-sidebar {
    @include responsive('md') {
      transform: translateX(-100%);
      
      &:not(.ant-layout-sider-collapsed) {
        transform: translateX(0);
      }
    }
  }
  
  :deep(.ant-layout-sider-children) {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

.sidebar-logo {
  @include flex-center;
  height: 64px;
  padding: $space-4;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  .logo-image {
    width: 32px;
    height: 32px;
  }
  
  .logo-text {
    margin-left: $space-3;
    color: white;
    font-weight: $font-semibold;
    font-size: $text-lg;
    white-space: nowrap;
  }
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  
  :deep(.ant-menu-item),
  :deep(.ant-menu-submenu-title) {
    @include flex-center-vertical;
    height: 48px;
    margin: 0;
    
    .anticon {
      font-size: 16px;
      margin-right: $space-3;
    }
  }
  
  :deep(.ant-menu-item-selected) {
    background-color: $primary-600 !important;
  }
}

.app-main {
  margin-left: 250px;
  @include transition(margin-left);
  
  .ant-layout-sider-collapsed + & {
    margin-left: 60px;
  }
  
  @include responsive('md') {
    margin-left: 0;
  }
}

.app-header {
  @include flex-between;
  padding: 0 $space-6;
  background: $bg-primary;
  border-bottom: 1px solid $border-light;
  box-shadow: $shadow-sm;
  
  .header-left {
    @include flex-center-vertical;
    gap: $space-4;
  }
  
  .header-right {
    @include flex-center-vertical;
    gap: $space-2;
  }
}

.sidebar-trigger {
  font-size: 18px;
  
  &:hover {
    background-color: $bg-secondary;
  }
}

.breadcrumb {
  :deep(.ant-breadcrumb-link) {
    color: $text-secondary;
    
    &:hover {
      color: $primary-500;
    }
  }
}

.theme-toggle,
.fullscreen-toggle {
  font-size: 16px;
  
  &:hover {
    background-color: $bg-secondary;
  }
}

.user-menu {
  @include flex-center-vertical;
  gap: $space-2;
  padding: $space-2 $space-3;
  
  .username {
    font-size: $text-sm;
    color: $text-primary;
  }
  
  &:hover {
    background-color: $bg-secondary;
  }
}

.app-content {
  margin: $space-6;
  min-height: calc(100vh - 64px - 70px - 48px); // header + footer + margins
  
  @include responsive('md') {
    margin: $space-4;
  }
}

.content-wrapper {
  background: $bg-primary;
  @include border-radius('lg');
  @include shadow('sm');
  min-height: 100%;
}

.app-footer {
  text-align: center;
  background: $bg-primary;
  border-top: 1px solid $border-light;
  
  .footer-content {
    @include flex-between;
    color: $text-tertiary;
    font-size: $text-sm;
    
    @include responsive('md') {
      flex-direction: column;
      gap: $space-2;
    }
  }
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: $z-modal-backdrop;
  
  @include responsive('lg') {
    display: none;
  }
}

// 页面切换动画
.page-transition-enter-active,
.page-transition-leave-active {
  transition: opacity $transition-normal, transform $transition-normal;
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
