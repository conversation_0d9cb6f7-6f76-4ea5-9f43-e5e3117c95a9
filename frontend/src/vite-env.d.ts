/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_API_RESPONSE_FORMAT: string
  readonly VITE_AUTH_TOKEN_KEY: string
  readonly VITE_AUTH_STORAGE_TYPE: string
  readonly VITE_ENABLE_MOCK: string
  readonly VITE_ENABLE_CONSOLE_LOG: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_ENV: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}