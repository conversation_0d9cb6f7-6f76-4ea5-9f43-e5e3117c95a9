// 数据状态管理
import { defineStore } from 'pinia';
import { DataService } from '@/api/services/dataService';
import type { 
  FilterDataResponse, 
  CustomerOrderResponse, 
  OverdueOrderResponse,
  CustomerSummaryData,
  DataSummaryResponse,
  OrderSummaryResponse
} from '@/api/types';

export const useDataStore = defineStore('data', () => {
  // 状态
  const filterData = ref<FilterDataResponse | null>(null);
  const customerOrders = ref<CustomerOrderResponse | null>(null);
  const overdueOrders = ref<OverdueOrderResponse | null>(null);
  const customerSummary = ref<CustomerSummaryData | null>(null);
  const dataSummary = ref<DataSummaryResponse | null>(null);
  const orderSummary = ref<OrderSummaryResponse | null>(null);
  
  const loading = ref({
    filter: false,
    customer: false,
    overdue: false,
    customerSummary: false,
    dataSummary: false,
    orderSummary: false
  });

  const errors = ref({
    filter: '',
    customer: '',
    overdue: '',
    customerSummary: '',
    dataSummary: '',
    orderSummary: ''
  });

  // 缓存时间戳
  const cacheTimestamps = ref({
    filter: 0,
    customer: 0,
    overdue: 0,
    customerSummary: 0,
    dataSummary: 0,
    orderSummary: 0
  });

  // 计算属性
  const hasFilterData = computed(() => 
    filterData.value?.results && filterData.value.results.length > 0
  );

  const hasCustomerData = computed(() => 
    customerOrders.value?.results && customerOrders.value.results.length > 0
  );

  const hasOverdueData = computed(() => 
    overdueOrders.value?.results && overdueOrders.value.results.length > 0
  );

  const hasCustomerSummary = computed(() => 
    customerSummary.value && Object.keys(customerSummary.value).length > 0
  );

  const hasDataSummary = computed(() => 
    dataSummary.value && Object.keys(dataSummary.value).length > 0
  );

  // 通用加载状态检查
  const isLoading = computed(() => 
    Object.values(loading.value).some(status => status)
  );

  // 通用错误检查
  const hasErrors = computed(() => 
    Object.values(errors.value).some(error => error !== '')
  );

  // 动作 - 数据获取
  const fetchFilterData = async (params: { date: string }, forceRefresh = false) => {
    const cacheKey = 'filter';
    const cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    
    // 检查缓存
    if (!forceRefresh && 
        filterData.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return filterData.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getFilterData(params);
      filterData.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取数据失败';
      errors.value[cacheKey] = errorMessage;
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchCustomerOrders = async (params: { customer_name: string }, forceRefresh = false) => {
    const cacheKey = 'customer';
    const cacheExpiry = 10 * 60 * 1000; // 10分钟缓存
    
    if (!forceRefresh && 
        customerOrders.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return customerOrders.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getCustomerOrders(params);
      customerOrders.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取客户数据失败';
      errors.value[cacheKey] = errorMessage;
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchOverdueOrders = async (params: { page?: number; limit?: number } = {}, forceRefresh = false) => {
    const cacheKey = 'overdue';
    const cacheExpiry = 3 * 60 * 1000; // 3分钟缓存（逾期数据更新频繁）
    
    if (!forceRefresh && 
        overdueOrders.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return overdueOrders.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getOverdueOrders(params);
      overdueOrders.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取逾期订单失败';
      errors.value[cacheKey] = errorMessage;
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchCustomerSummary = async (params: { customer_name: string }, forceRefresh = false) => {
    const cacheKey = 'customerSummary';
    const cacheExpiry = 15 * 60 * 1000; // 15分钟缓存
    
    if (!forceRefresh && 
        customerSummary.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return customerSummary.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getCustomerSummary(params);
      customerSummary.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取客户汇总失败';
      errors.value[cacheKey] = errorMessage;
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchDataSummary = async (params: { start_date: string; end_date: string }, forceRefresh = false) => {
    const cacheKey = 'dataSummary';
    const cacheExpiry = 30 * 60 * 1000; // 30分钟缓存
    
    if (!forceRefresh && 
        dataSummary.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return dataSummary.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getDataSummary(params);
      dataSummary.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取数据汇总失败';
      errors.value[cacheKey] = errorMessage;
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  const fetchOrderSummary = async (params: { end_date: string }, forceRefresh = false) => {
    const cacheKey = 'orderSummary';
    const cacheExpiry = 60 * 60 * 1000; // 1小时缓存
    
    if (!forceRefresh && 
        orderSummary.value && 
        Date.now() - cacheTimestamps.value[cacheKey] < cacheExpiry) {
      return orderSummary.value;
    }

    loading.value[cacheKey] = true;
    errors.value[cacheKey] = '';

    try {
      const response = await DataService.getOrderSummary(params);
      orderSummary.value = response;
      cacheTimestamps.value[cacheKey] = Date.now();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取订单汇总失败';
      errors.value[cacheKey] = errorMessage;
      throw error;
    } finally {
      loading.value[cacheKey] = false;
    }
  };

  // 缓存管理
  const clearCache = (type?: keyof typeof cacheTimestamps.value) => {
    if (type) {
      switch (type) {
        case 'filter':
          filterData.value = null;
          break;
        case 'customer':
          customerOrders.value = null;
          break;
        case 'overdue':
          overdueOrders.value = null;
          break;
        case 'customerSummary':
          customerSummary.value = null;
          break;
        case 'dataSummary':
          dataSummary.value = null;
          break;
        case 'orderSummary':
          orderSummary.value = null;
          break;
      }
      cacheTimestamps.value[type] = 0;
      errors.value[type] = '';
    } else {
      // 清除所有缓存
      filterData.value = null;
      customerOrders.value = null;
      overdueOrders.value = null;
      customerSummary.value = null;
      dataSummary.value = null;
      orderSummary.value = null;
      
      Object.keys(cacheTimestamps.value).forEach(key => {
        cacheTimestamps.value[key as keyof typeof cacheTimestamps.value] = 0;
        errors.value[key as keyof typeof errors.value] = '';
      });
    }
    
    // 同时清除DataService的缓存
    DataService.clearCache(type);
  };

  const clearError = (type: keyof typeof errors.value) => {
    errors.value[type] = '';
  };

  const clearAllErrors = () => {
    Object.keys(errors.value).forEach(key => {
      errors.value[key as keyof typeof errors.value] = '';
    });
  };

  return {
    // 状态
    filterData: readonly(filterData),
    customerOrders: readonly(customerOrders),
    overdueOrders: readonly(overdueOrders),
    customerSummary: readonly(customerSummary),
    dataSummary: readonly(dataSummary),
    orderSummary: readonly(orderSummary),
    loading: readonly(loading),
    errors: readonly(errors),
    
    // 计算属性
    hasFilterData,
    hasCustomerData,
    hasOverdueData,
    hasCustomerSummary,
    hasDataSummary,
    isLoading,
    hasErrors,
    
    // 动作
    fetchFilterData,
    fetchCustomerOrders,
    fetchOverdueOrders,
    fetchCustomerSummary,
    fetchDataSummary,
    fetchOrderSummary,
    clearCache,
    clearError,
    clearAllErrors
  };
});
