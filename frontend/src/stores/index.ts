// Pinia状态管理入口文件
import { createPinia } from 'pinia';
import { createPersistedState } from 'pinia-plugin-persistedstate';

// 创建Pinia实例
const pinia = createPinia();

// 配置持久化插件
pinia.use(
  createPersistedState({
    storage: localStorage,
    key: id => `hdsc_${id}`,
    auto: true, // 自动持久化所有store
  })
);

export default pinia;

// 导出所有store
export { useUserStore } from './user';
export { useDataStore } from './data';
export { useAppStore } from './app';
