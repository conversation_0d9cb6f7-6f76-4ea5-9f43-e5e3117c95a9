// 用户状态管理
import { defineStore } from 'pinia';
import { AuthService } from '@/api/services/authService';
import type { UserInfo, LoginCredentials } from '@/api/types';

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null);
  const token = ref<string>('');
  const isAuthenticated = ref(false);
  const loginLoading = ref(false);

  // 计算属性
  const hasPermission = computed(() => (level: 'limited' | 'standard' | 'full') => {
    if (!userInfo.value) return false;
    
    const permissions = {
      limited: ['limited', 'standard', 'full'],
      standard: ['standard', 'full'],
      full: ['full']
    };
    
    return permissions[level].includes(userInfo.value.permission_level);
  });

  const userName = computed(() => userInfo.value?.username || '');
  const permissionLevel = computed(() => userInfo.value?.permission_level || 'limited');
  
  const permissionText = computed(() => {
    const texts = {
      limited: '有限权限',
      standard: '标准权限',
      full: '完全权限'
    };
    return texts[permissionLevel.value] || '未知权限';
  });

  // 权限检查计算属性
  const canAccessOverdueOrders = computed(() => hasPermission.value('standard'));
  const canExportData = computed(() => hasPermission.value('standard'));
  const canAccessAdminFeatures = computed(() => hasPermission.value('full'));

  // 动作
  const login = async (credentials: LoginCredentials) => {
    loginLoading.value = true;
    try {
      const response = await AuthService.login(credentials);
      
      userInfo.value = response.user;
      token.value = response.token || '';
      isAuthenticated.value = true;
      
      // 保存token到AuthService
      if (response.token) {
        AuthService.setAuthToken(response.token, true); // 记住登录状态
      }
      
      return response;
    } catch (error) {
      // 清除状态
      userInfo.value = null;
      token.value = '';
      isAuthenticated.value = false;
      throw error;
    } finally {
      loginLoading.value = false;
    }
  };

  const logout = async () => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除状态
      userInfo.value = null;
      token.value = '';
      isAuthenticated.value = false;
      
      // 清除AuthService中的token
      AuthService.clearAuthToken();
      
      // 跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
  };

  const updateUserInfo = (info: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...info };
    }
  };

  const checkAuthStatus = async () => {
    try {
      // 检查是否有token
      const savedToken = AuthService.getAuthToken();
      if (!savedToken) {
        return false;
      }

      // 验证token有效性
      const user = await AuthService.getCurrentUser();
      userInfo.value = user;
      token.value = savedToken;
      isAuthenticated.value = true;
      
      return true;
    } catch (error) {
      // token无效，清除状态
      userInfo.value = null;
      token.value = '';
      isAuthenticated.value = false;
      AuthService.clearAuthToken();
      
      return false;
    }
  };

  const refreshToken = async () => {
    try {
      const response = await AuthService.refreshToken();
      token.value = response.token;
      return true;
    } catch (error) {
      // 刷新失败，需要重新登录
      await logout();
      return false;
    }
  };

  // 初始化时检查认证状态
  const initializeAuth = async () => {
    const isValid = await checkAuthStatus();
    return isValid;
  };

  return {
    // 状态
    userInfo: readonly(userInfo),
    token: readonly(token),
    isAuthenticated: readonly(isAuthenticated),
    loginLoading: readonly(loginLoading),
    
    // 计算属性
    hasPermission,
    userName,
    permissionLevel,
    permissionText,
    canAccessOverdueOrders,
    canExportData,
    canAccessAdminFeatures,
    
    // 动作
    login,
    logout,
    updateUserInfo,
    checkAuthStatus,
    refreshToken,
    initializeAuth
  };
}, {
  persist: {
    paths: ['userInfo', 'token', 'isAuthenticated'],
    storage: localStorage
  }
});
