// 应用状态管理
import { defineStore } from 'pinia';

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<'light' | 'dark' | 'auto'>('auto');
  const sidebarCollapsed = ref(false);
  const isMobile = ref(false);
  const currentRoute = ref('');
  
  // 全局加载状态
  const globalLoading = ref(false);
  const loadingText = ref('加载中...');

  // 应用配置
  const appConfig = ref({
    title: import.meta.env.VITE_APP_TITLE || '太享查询系统',
    version: import.meta.env.VITE_APP_VERSION || '2.0.0',
    description: import.meta.env.VITE_APP_DESCRIPTION || '现代化金融数据查询展示系统'
  });

  // 用户偏好设置
  const userPreferences = ref({
    tablePageSize: 20,
    dateFormat: 'YYYY-MM-DD',
    autoRefresh: false,
    autoRefreshInterval: 30000, // 30秒
    showWelcomeMessage: true,
    compactMode: false
  });

  // 计算属性
  const isDarkMode = computed(() => {
    if (theme.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return theme.value === 'dark';
  });

  const isDesktop = computed(() => !isMobile.value);

  const effectiveTheme = computed(() => {
    if (theme.value === 'auto') {
      return isDarkMode.value ? 'dark' : 'light';
    }
    return theme.value;
  });

  // 动作
  const toggleTheme = () => {
    const themes: Array<'light' | 'dark' | 'auto'> = ['light', 'dark', 'auto'];
    const currentIndex = themes.indexOf(theme.value);
    const nextIndex = (currentIndex + 1) % themes.length;
    theme.value = themes[nextIndex];
    
    // 应用主题到DOM
    applyTheme();
  };

  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme;
    applyTheme();
  };

  const applyTheme = () => {
    const root = document.documentElement;
    const effectiveThemeValue = effectiveTheme.value;
    
    // 移除所有主题类
    root.classList.remove('theme-light', 'theme-dark');
    
    // 添加当前主题类
    root.classList.add(`theme-${effectiveThemeValue}`);
    
    // 设置data属性供CSS使用
    root.setAttribute('data-theme', effectiveThemeValue);
  };

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  };

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed;
  };

  const setMobile = (mobile: boolean) => {
    isMobile.value = mobile;
    
    // 移动端自动折叠侧边栏
    if (mobile) {
      sidebarCollapsed.value = true;
    }
  };

  const setCurrentRoute = (route: string) => {
    currentRoute.value = route;
  };

  const showGlobalLoading = (text = '加载中...') => {
    globalLoading.value = true;
    loadingText.value = text;
  };

  const hideGlobalLoading = () => {
    globalLoading.value = false;
  };

  // 用户偏好设置管理
  const updateUserPreference = <K extends keyof typeof userPreferences.value>(
    key: K, 
    value: typeof userPreferences.value[K]
  ) => {
    userPreferences.value[key] = value;
  };

  const resetUserPreferences = () => {
    userPreferences.value = {
      tablePageSize: 20,
      dateFormat: 'YYYY-MM-DD',
      autoRefresh: false,
      autoRefreshInterval: 30000,
      showWelcomeMessage: true,
      compactMode: false
    };
  };

  // 响应式监听
  const initializeResponsive = () => {
    const checkMobile = () => {
      setMobile(window.innerWidth <= 768);
    };
    
    // 初始检查
    checkMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkMobile);
    
    // 返回清理函数
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  };

  // 主题监听
  const initializeThemeListener = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleThemeChange = () => {
      if (theme.value === 'auto') {
        applyTheme();
      }
    };
    
    // 监听系统主题变化
    mediaQuery.addEventListener('change', handleThemeChange);
    
    // 初始应用主题
    applyTheme();
    
    // 返回清理函数
    return () => {
      mediaQuery.removeEventListener('change', handleThemeChange);
    };
  };

  // 应用初始化
  const initializeApp = () => {
    const cleanupResponsive = initializeResponsive();
    const cleanupTheme = initializeThemeListener();
    
    // 返回清理函数
    return () => {
      cleanupResponsive();
      cleanupTheme();
    };
  };

  // 获取应用信息
  const getAppInfo = () => {
    return {
      ...appConfig.value,
      theme: effectiveTheme.value,
      isMobile: isMobile.value,
      sidebarCollapsed: sidebarCollapsed.value,
      currentRoute: currentRoute.value
    };
  };

  // 错误处理
  const handleError = (error: Error, context?: string) => {
    console.error(`[App Error]${context ? ` ${context}:` : ''}`, error);
    
    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  };

  return {
    // 状态
    theme: readonly(theme),
    sidebarCollapsed: readonly(sidebarCollapsed),
    isMobile: readonly(isMobile),
    currentRoute: readonly(currentRoute),
    globalLoading: readonly(globalLoading),
    loadingText: readonly(loadingText),
    appConfig: readonly(appConfig),
    userPreferences: readonly(userPreferences),
    
    // 计算属性
    isDarkMode,
    isDesktop,
    effectiveTheme,
    
    // 动作
    toggleTheme,
    setTheme,
    applyTheme,
    toggleSidebar,
    setSidebarCollapsed,
    setMobile,
    setCurrentRoute,
    showGlobalLoading,
    hideGlobalLoading,
    updateUserPreference,
    resetUserPreferences,
    initializeApp,
    getAppInfo,
    handleError
  };
}, {
  persist: {
    paths: ['theme', 'sidebarCollapsed', 'userPreferences'],
    storage: localStorage
  }
});
