// API类型定义 - 基于现有Flask API设计
// 这些类型定义与后端API保持一致

// ==================== 基础类型 ====================

export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
  results?: T;
  columns?: string[];
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface CacheInfo {
  last_update: string;
  next_update: string;
}

// ==================== 用户相关类型 ====================

export interface UserInfo {
  username: string;
  permission_level: 'limited' | 'standard' | 'full';
  is_authenticated: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
  captcha: string;
}

export interface LoginResponse {
  user: UserInfo;
  token?: string;
  message: string;
}

// ==================== 订单数据类型 ====================

export interface OrderRecord {
  订单编号: string;
  客户姓名: string;
  客户手机: string;
  订单日期: string;
  账单日期?: string;
  当前待收: number;
  总待收: number;
  成本: number;
  金额: number;
  业务: string;
  客服: string;
  产品: string;
  产品类型: string;
  逾期天数?: number;
  逾期期数?: number;
  首次逾期期数?: number;
  首次逾期日期?: string;
  状态?: string;
  备注?: string;
  // 动态期数字段
  [key: string]: any;
}

// ==================== 数据查询相关类型 ====================

export interface FilterDataParams {
  date: string; // YYYY-MM-DD格式
}

export interface FilterDataResponse extends ApiResponse<OrderRecord[]> {
  results: OrderRecord[];
  columns: string[];
  pagination?: Pagination;
  cache_info?: CacheInfo;
}

export interface CustomerQueryParams {
  customer_name: string;
}

export interface CustomerOrderResponse extends ApiResponse<OrderRecord[]> {
  results: OrderRecord[];
  columns: string[];
}

export interface OverdueOrderParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface OverdueOrderResponse extends ApiResponse<OrderRecord[]> {
  results: OrderRecord[];
  columns: string[];
  pagination: Pagination;
  cache_info: CacheInfo;
}

// ==================== 客户汇总类型 ====================

export interface CustomerSummaryParams {
  customer_name?: string;
  phone?: string;
}

export interface CustomerSummaryData {
  customer_name: string;
  phone: string;
  total_orders: number;
  total_financing: number;
  current_outstanding: number;
  total_repaid: number;
  overdue_amount: number;
  overdue_orders: number;
  order_details: OrderRecord[];
  // 图表数据
  chart_data?: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
    }>;
  };
}

// ==================== 数据汇总类型 ====================

export interface DataSummaryParams {
  start_date: string;
  end_date: string;
}

export interface DataSummaryResponse extends ApiResponse {
  summary_data: {
    total_orders: number;
    total_amount: number;
    overdue_orders: number;
    overdue_amount: number;
    [key: string]: any;
  };
  chart_data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
    }>;
  };
}

export interface OrderSummaryParams {
  end_date: string;
}

export interface OrderSummaryResponse extends ApiResponse {
  monthly_data: Array<{
    month: string;
    ecommerce_orders: number;
    rental_orders: number;
    total_orders: number;
  }>;
  chart_data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string;
      borderColor?: string;
    }>;
  };
}

// ==================== 导出相关类型 ====================

export interface ExportParams {
  format: 'excel' | 'csv';
  data_type: 'filter' | 'customer' | 'overdue';
  search_params?: Record<string, any>;
  filename?: string;
}

export interface ExportResponse {
  success: boolean;
  download_url?: string;
  filename?: string;
  error?: string;
}

// ==================== 工具相关类型 ====================

export interface QRCodeParams {
  text: string;
  size?: number;
  format?: 'png' | 'svg';
}

export interface QRCodeResponse {
  success: boolean;
  qr_code_url?: string;
  error?: string;
}

// ==================== 错误类型 ====================

export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp: string;
}

// ==================== 请求配置类型 ====================

export interface RequestConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTime?: number;
}

// ==================== 响应拦截器类型 ====================

export interface ResponseInterceptor {
  onSuccess?: (response: any) => any;
  onError?: (error: any) => any;
}

// ==================== 分页查询通用类型 ====================

export interface PaginatedRequest {
  page?: number;
  limit?: number;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  results: T[];
  pagination: Pagination;
  total: number;
}

// ==================== 图表数据类型 ====================

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
  tension?: number;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    title?: {
      display: boolean;
      text: string;
    };
    legend?: {
      display: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
  };
  scales?: any;
}
