// HTTP客户端配置 - 基于Axios的现代化API客户端
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios';
import { message } from 'ant-design-vue';
import type { ApiError, RequestConfig } from './types';

// 请求状态枚举
enum RequestStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 请求缓存接口
interface RequestCache {
  [key: string]: {
    data: any;
    timestamp: number;
    expiry: number;
  };
}

class HttpClient {
  private instance: AxiosInstance;
  private requestCache: RequestCache = {};
  private pendingRequests: Map<string, Promise<any>> = new Map();

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '',
      timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 30000,
      withCredentials: true, // 允许发送cookies
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      config => {
        // 添加时间戳防止缓存
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now(),
          };
        }

        // 添加认证信息
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 显示加载状态
        this.showLoading(config);

        return config;
      },
      error => {
        this.hideLoading();
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      response => {
        this.hideLoading();
        return this.handleResponse(response);
      },
      error => {
        this.hideLoading();
        return this.handleError(error);
      }
    );
  }

  private handleResponse(response: AxiosResponse): any {
    const { data } = response;

    // 检查业务状态码
    if (data && typeof data === 'object') {
      // 如果有error字段，说明业务逻辑出错
      if (data.error) {
        const errorMessage = data.error || '请求失败';
        message.error(errorMessage);
        throw new Error(errorMessage);
      }

      // 返回数据
      return data;
    }

    return data;
  }

  private handleError(error: any): Promise<never> {
    const { response, message: errorMessage } = error;

    let apiError: ApiError = {
      code: 0,
      message: '网络错误',
      timestamp: new Date().toISOString(),
    };

    if (response) {
      // 服务器响应错误
      const { status, data } = response;
      apiError = {
        code: status,
        message: data?.message || data?.error || this.getErrorMessage(status),
        details: data,
        timestamp: new Date().toISOString(),
      };

      // 处理特定状态码
      switch (status) {
        case 401:
          this.handleUnauthorized();
          break;
        case 403:
          message.error('权限不足');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 500:
          message.error('服务器内部错误');
          break;
        default:
          message.error(apiError.message);
      }
    } else if (errorMessage) {
      // 网络错误或超时
      if (errorMessage.includes('timeout')) {
        apiError.message = '请求超时，请检查网络连接';
      } else if (errorMessage.includes('Network Error')) {
        apiError.message = '网络连接失败，请检查网络设置';
      }
      message.error(apiError.message);
    }

    return Promise.reject(apiError);
  }

  private getErrorMessage(status: number): string {
    const errorMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足',
      404: '请求的资源不存在',
      405: '请求方法不允许',
      408: '请求超时',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时',
    };

    return errorMessages[status] || `请求失败 (${status})`;
  }

  private handleUnauthorized() {
    // 清除认证信息
    this.clearAuthToken();
    
    // 跳转到登录页
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  }

  private getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }
    return null;
  }

  private clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      sessionStorage.removeItem('auth_token');
    }
  }

  private showLoading(config: AxiosRequestConfig): void {
    // 可以在这里实现全局加载状态
    if (config.headers?.['X-Show-Loading'] !== 'false') {
      // 显示加载指示器
    }
  }

  private hideLoading(): void {
    // 隐藏加载指示器
  }

  // 生成缓存键
  private generateCacheKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : '';
    return `${url}_${paramStr}`;
  }

  // 检查缓存
  private checkCache(cacheKey: string): any | null {
    const cached = this.requestCache[cacheKey];
    if (cached && Date.now() < cached.timestamp + cached.expiry) {
      return cached.data;
    }
    return null;
  }

  // 设置缓存
  private setCache(cacheKey: string, data: any, expiry: number): void {
    this.requestCache[cacheKey] = {
      data,
      timestamp: Date.now(),
      expiry,
    };
  }

  // 清除缓存
  public clearCache(pattern?: string): void {
    if (pattern) {
      Object.keys(this.requestCache).forEach(key => {
        if (key.includes(pattern)) {
          delete this.requestCache[key];
        }
      });
    } else {
      this.requestCache = {};
    }
  }

  // GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig & RequestConfig): Promise<T> {
    const { cache = false, cacheTime = 5 * 60 * 1000, ...axiosConfig } = config || {};

    // 检查缓存
    if (cache) {
      const cacheKey = this.generateCacheKey(url, axiosConfig.params);
      const cachedData = this.checkCache(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // 检查是否有相同的请求正在进行
      if (this.pendingRequests.has(cacheKey)) {
        return this.pendingRequests.get(cacheKey)!;
      }

      // 发起请求并缓存Promise
      const requestPromise = this.instance.get(url, axiosConfig).then(processedData => {
        this.setCache(cacheKey, processedData, cacheTime);
        this.pendingRequests.delete(cacheKey);
        return processedData;
      }).catch(error => {
        this.pendingRequests.delete(cacheKey);
        throw error;
      });

      this.pendingRequests.set(cacheKey, requestPromise);
      return requestPromise;
    }

    // 发起请求，响应拦截器会处理响应并返回正确的数据类型
    const result = await this.instance.get(url, axiosConfig);
    return result as T;
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config);
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config);
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config);
  }

  // 上传文件
  async upload<T = any>(url: string, file: File, config?: AxiosRequestConfig): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    return this.instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
  }

  // 下载文件
  async download(url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> {
    const response = await this.instance.get(url, {
      ...config,
      responseType: 'blob',
    });

    // 创建下载链接
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }
}

// 创建HTTP客户端实例
export const http = new HttpClient();

// 导出类型
export type { RequestConfig, ApiError };
