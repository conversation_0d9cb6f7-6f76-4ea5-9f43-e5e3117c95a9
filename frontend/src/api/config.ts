// API配置管理器
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  responseFormat: 'standard' | 'laravel' | 'simple';
  authTokenKey: string;
  authStorageType: 'localStorage' | 'sessionStorage';
  enableMock: boolean;
  enableConsoleLog: boolean;
}

// 获取API配置
export function getApiConfig(): ApiConfig {
  return {
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
    responseFormat: import.meta.env.VITE_API_RESPONSE_FORMAT || 'standard',
    authTokenKey: import.meta.env.VITE_AUTH_TOKEN_KEY || 'auth_token',
    authStorageType: import.meta.env.VITE_AUTH_STORAGE_TYPE || 'localStorage',
    enableMock: import.meta.env.VITE_ENABLE_MOCK === 'true',
    enableConsoleLog: import.meta.env.VITE_ENABLE_CONSOLE_LOG === 'true',
  };
}

// 响应格式处理器
export class ResponseHandler {
  private format: string;

  constructor(format: string = 'standard') {
    this.format = format;
  }

  // 处理成功响应
  handleSuccess(data: any): any {
    switch (this.format) {
      case 'standard':
        // { code: 200, message: 'success', data: {...} }
        if (data && typeof data === 'object' && 'code' in data) {
          if (data.code === 200 || data.code === 0) {
            return data.data !== undefined ? data.data : data;
          } else {
            throw new Error(data.message || '请求失败');
          }
        }
        return data;

      case 'laravel':
        // { success: true, data: {...}, message: 'success' }
        if (data && typeof data === 'object' && 'success' in data) {
          if (data.success) {
            return data.data !== undefined ? data.data : data;
          } else {
            throw new Error(data.message || '请求失败');
          }
        }
        return data;

      case 'simple':
        // 直接返回数据
        return data;

      default:
        return data;
    }
  }

  // 检查是否为错误响应
  isErrorResponse(data: any): boolean {
    switch (this.format) {
      case 'standard':
        return data && typeof data === 'object' && 'code' in data && data.code !== 200 && data.code !== 0;
      
      case 'laravel':
        return data && typeof data === 'object' && 'success' in data && !data.success;
      
      case 'simple':
        return data && typeof data === 'object' && 'error' in data;
      
      default:
        return false;
    }
  }

  // 获取错误消息
  getErrorMessage(data: any): string {
    switch (this.format) {
      case 'standard':
        return data?.message || '请求失败';
      
      case 'laravel':
        return data?.message || '请求失败';
      
      case 'simple':
        return data?.error || '请求失败';
      
      default:
        return '请求失败';
    }
  }
}

// 认证管理器
export class AuthManager {
  private tokenKey: string;
  private storageType: 'localStorage' | 'sessionStorage';

  constructor(tokenKey: string = 'auth_token', storageType: 'localStorage' | 'sessionStorage' = 'localStorage') {
    this.tokenKey = tokenKey;
    this.storageType = storageType;
  }

  // 获取存储对象
  private getStorage(): Storage {
    return this.storageType === 'localStorage' ? localStorage : sessionStorage;
  }

  // 获取token
  getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return this.getStorage().getItem(this.tokenKey);
  }

  // 设置token
  setToken(token: string): void {
    if (typeof window === 'undefined') return;
    this.getStorage().setItem(this.tokenKey, token);
  }

  // 清除token
  clearToken(): void {
    if (typeof window === 'undefined') return;
    this.getStorage().removeItem(this.tokenKey);
  }

  // 检查是否已认证
  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/api/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    USER_INFO: '/auth/user',
    CAPTCHA: '/auth/captcha',
  },

  // 数据查询
  DATA: {
    QUERY: '/data/query',
    EXPORT: '/data/export',
    SUMMARY: '/data/summary',
  },

  // 客户管理
  CUSTOMER: {
    LIST: '/customers',
    DETAIL: '/customers/:id',
    ORDERS: '/customers/:id/orders',
    SUMMARY: '/customers/summary',
  },

  // 订单管理
  ORDER: {
    LIST: '/orders',
    DETAIL: '/orders/:id',
    UPDATE: '/orders/:id',
    SUMMARY: '/orders/summary',
    OVERDUE: '/orders/overdue',
  },

  // 跟进记录
  FOLLOW_UP: {
    LIST: '/follow-ups',
    CREATE: '/follow-ups',
    UPDATE: '/follow-ups/:id',
    DELETE: '/follow-ups/:id',
  },

  // 文件上传
  UPLOAD: {
    FILE: '/upload/file',
    IMAGE: '/upload/image',
  },

  // 工具相关
  TOOLS: {
    QR_CODE: '/tools/qrcode',
    EXPORT: '/tools/export',
  },
};

// URL参数替换
export function buildUrl(template: string, params: Record<string, string | number>): string {
  let url = template;
  Object.entries(params).forEach(([key, value]) => {
    url = url.replace(`:${key}`, String(value));
  });
  return url;
}

// 导出配置实例
export const apiConfig = getApiConfig();
export const responseHandler = new ResponseHandler(apiConfig.responseFormat);
export const authManager = new AuthManager(apiConfig.authTokenKey, apiConfig.authStorageType);
