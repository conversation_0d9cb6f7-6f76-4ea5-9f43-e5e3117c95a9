// 认证服务 - 处理用户登录、权限验证等
import { http } from '../client';
import type { LoginCredentials, LoginResponse, UserInfo } from '../types';

/**
 * 认证服务类 - 提供用户认证相关的API方法
 */
export class AuthService {
  // ==================== 认证相关 ====================

  /**
   * 用户登录
   * @param credentials 登录凭据
   * @returns 登录结果
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await http.post('/auth/api/login', credentials);
    
    // 登录成功后保存token
    if (response.token) {
      this.setAuthToken(response.token);
    }
    
    return response;
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await http.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 无论是否成功，都清除本地token
      this.clearAuthToken();
    }
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  static async getCurrentUser(): Promise<UserInfo> {
    return http.get('/auth/user', {
      cache: true,
      cacheTime: 10 * 60 * 1000, // 10分钟缓存
    });
  }

  /**
   * 刷新用户token
   * @returns 新的token
   */
  static async refreshToken(): Promise<{ token: string }> {
    const response = await http.post('/auth/refresh');
    
    if (response.token) {
      this.setAuthToken(response.token);
    }
    
    return response;
  }

  /**
   * 获取验证码
   * @returns 验证码图片URL
   */
  static async getCaptcha(): Promise<{ captcha_url: string; captcha_key: string }> {
    return http.get('/auth/captcha');
  }

  // ==================== Token管理 ====================

  /**
   * 设置认证token
   * @param token 认证token
   * @param remember 是否记住登录状态
   */
  static setAuthToken(token: string, remember = false): void {
    if (typeof window !== 'undefined') {
      if (remember) {
        localStorage.setItem('auth_token', token);
      } else {
        sessionStorage.setItem('auth_token', token);
      }
    }
  }

  /**
   * 获取认证token
   * @returns 认证token
   */
  static getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }
    return null;
  }

  /**
   * 清除认证token
   */
  static clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      sessionStorage.removeItem('auth_token');
    }
  }

  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  static isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }

  // ==================== 权限验证 ====================

  /**
   * 检查用户权限
   * @param requiredLevel 需要的权限级别
   * @param userLevel 用户权限级别
   * @returns 是否有权限
   */
  static hasPermission(
    requiredLevel: 'limited' | 'standard' | 'full',
    userLevel?: 'limited' | 'standard' | 'full'
  ): boolean {
    if (!userLevel) {
      return false;
    }

    const permissionLevels = {
      limited: 1,
      standard: 2,
      full: 3,
    };

    return permissionLevels[userLevel] >= permissionLevels[requiredLevel];
  }

  /**
   * 获取权限级别描述
   * @param level 权限级别
   * @returns 权限描述
   */
  static getPermissionDescription(level: 'limited' | 'standard' | 'full'): string {
    const descriptions = {
      limited: '有限权限 - 只能查看基础数据',
      standard: '标准权限 - 可以查看大部分数据',
      full: '完全权限 - 可以访问所有功能',
    };

    return descriptions[level] || '未知权限';
  }

  /**
   * 检查是否可以访问逾期订单
   * @param userLevel 用户权限级别
   * @returns 是否可以访问
   */
  static canAccessOverdueOrders(userLevel?: 'limited' | 'standard' | 'full'): boolean {
    return this.hasPermission('standard', userLevel);
  }

  /**
   * 检查是否可以导出数据
   * @param userLevel 用户权限级别
   * @returns 是否可以导出
   */
  static canExportData(userLevel?: 'limited' | 'standard' | 'full'): boolean {
    return this.hasPermission('standard', userLevel);
  }

  /**
   * 检查是否可以访问管理功能
   * @param userLevel 用户权限级别
   * @returns 是否可以访问
   */
  static canAccessAdminFeatures(userLevel?: 'limited' | 'standard' | 'full'): boolean {
    return this.hasPermission('full', userLevel);
  }

  // ==================== 用户偏好设置 ====================

  /**
   * 保存用户偏好设置
   * @param preferences 偏好设置
   */
  static saveUserPreferences(preferences: Record<string, any>): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_preferences', JSON.stringify(preferences));
    }
  }

  /**
   * 获取用户偏好设置
   * @returns 偏好设置
   */
  static getUserPreferences(): Record<string, any> {
    if (typeof window !== 'undefined') {
      const preferences = localStorage.getItem('user_preferences');
      return preferences ? JSON.parse(preferences) : {};
    }
    return {};
  }

  /**
   * 清除用户偏好设置
   */
  static clearUserPreferences(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user_preferences');
    }
  }

  // ==================== 会话管理 ====================

  /**
   * 检查会话是否有效
   * @returns 会话是否有效
   */
  static async checkSession(): Promise<boolean> {
    try {
      await this.getCurrentUser();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 延长会话时间
   */
  static async extendSession(): Promise<void> {
    try {
      await http.post('/auth/extend-session');
    } catch (error) {
      console.error('Failed to extend session:', error);
    }
  }

  /**
   * 获取会话剩余时间
   * @returns 剩余时间（秒）
   */
  static async getSessionRemainingTime(): Promise<number> {
    try {
      const response = await http.get('/auth/session-info');
      return response.remaining_time || 0;
    } catch (error) {
      return 0;
    }
  }
}
