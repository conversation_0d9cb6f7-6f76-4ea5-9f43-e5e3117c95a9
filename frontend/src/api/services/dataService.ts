// 数据服务 - 封装所有数据相关的API调用
import { http } from '../client';
import { apiConfig } from '../config';
import type {
  FilterDataParams,
  FilterDataResponse,
  CustomerQueryParams,
  CustomerOrderResponse,
  OverdueOrderParams,
  OverdueOrderResponse,
  CustomerSummaryParams,
  CustomerSummaryData,
  DataSummaryParams,
  DataSummaryResponse,
  OrderSummaryParams,
  OrderSummaryResponse,
  ExportParams,
  ExportResponse,
} from '../types';

/**
 * 数据服务类 - 提供所有数据查询相关的API方法
 */
export class DataService {
  // ==================== 数据筛选相关 ====================

  /**
   * 根据日期筛选数据
   * @param params 筛选参数
   * @returns 筛选结果
   */
  static async getFilterData(params: FilterDataParams): Promise<FilterDataResponse> {
    try {
      if (apiConfig.enableConsoleLog) {
        console.log('🔍 筛选数据请求参数:', params);
      }

      const response = await http.get('/api/filter_data', {
        params: {
          ...params,
          // 确保日期格式正确
          start_date: params.start_date || params.startDate,
          end_date: params.end_date || params.endDate,
        },
        cache: true,
        cacheTime: 5 * 60 * 1000, // 5分钟缓存
      });

      if (apiConfig.enableConsoleLog) {
        console.log('✅ 筛选数据响应:', response);
      }

      return response;
    } catch (error) {
      console.error('❌ 筛选数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取客户订单数据
   * @param params 客户查询参数
   * @returns 客户订单列表
   */
  static async getCustomerOrders(params: CustomerQueryParams): Promise<CustomerOrderResponse> {
    return http.get('/api/filter_orders_by_customer_name', {
      params,
      cache: true,
      cacheTime: 10 * 60 * 1000, // 10分钟缓存
    });
  }

  /**
   * 获取逾期订单数据
   * @param params 逾期订单查询参数
   * @returns 逾期订单列表
   */
  static async getOverdueOrders(params: OverdueOrderParams = {}): Promise<OverdueOrderResponse> {
    return http.get('/api/filter_overdue_orders', {
      params,
      cache: true,
      cacheTime: 3 * 60 * 1000, // 3分钟缓存（逾期数据更新频繁）
    });
  }

  // ==================== 汇总数据相关 ====================

  /**
   * 获取客户汇总数据
   * @param params 客户汇总参数
   * @returns 客户汇总信息
   */
  static async getCustomerSummary(params: CustomerSummaryParams): Promise<CustomerSummaryData> {
    return http.get('/api/customer_summary', {
      params,
      cache: true,
      cacheTime: 15 * 60 * 1000, // 15分钟缓存
    });
  }

  /**
   * 获取数据汇总
   * @param params 数据汇总参数
   * @returns 数据汇总结果
   */
  static async getDataSummary(params: DataSummaryParams): Promise<DataSummaryResponse> {
    return http.get('/api/summary_data', {
      params,
      cache: true,
      cacheTime: 30 * 60 * 1000, // 30分钟缓存
    });
  }

  /**
   * 获取订单汇总数据
   * @param params 订单汇总参数
   * @returns 订单汇总结果
   */
  static async getOrderSummary(params: OrderSummaryParams): Promise<OrderSummaryResponse> {
    return http.get('/api/order_summary', {
      params,
      cache: true,
      cacheTime: 60 * 60 * 1000, // 1小时缓存
    });
  }

  /**
   * 获取逾期订单汇总
   * @param params 逾期汇总参数
   * @returns 逾期汇总结果
   */
  static async getOverdueSummary(params: { end_date: string }): Promise<DataSummaryResponse> {
    return http.get('/api/overdue_summary', {
      params,
      cache: true,
      cacheTime: 30 * 60 * 1000, // 30分钟缓存
    });
  }

  // ==================== 数据导出相关 ====================

  /**
   * 导出数据
   * @param params 导出参数
   * @returns 导出结果
   */
  static async exportData(params: ExportParams): Promise<ExportResponse> {
    return http.post('/api/export', params);
  }

  /**
   * 导出Excel文件
   * @param params 导出参数
   * @returns 下载链接
   */
  static async exportExcel(params: Omit<ExportParams, 'format'>): Promise<void> {
    const filename = params.filename || `数据导出_${new Date().toISOString().split('T')[0]}.xlsx`;
    
    await http.download('/api/export/excel', filename, {
      method: 'POST',
      data: params,
    });
  }

  /**
   * 导出CSV文件
   * @param params 导出参数
   * @returns 下载链接
   */
  static async exportCSV(params: Omit<ExportParams, 'format'>): Promise<void> {
    const filename = params.filename || `数据导出_${new Date().toISOString().split('T')[0]}.csv`;
    
    await http.download('/api/export/csv', filename, {
      method: 'POST',
      data: params,
    });
  }

  // ==================== 缓存管理 ====================

  /**
   * 清除数据缓存
   * @param type 缓存类型，不传则清除所有
   */
  static clearCache(type?: 'filter' | 'customer' | 'overdue' | 'summary'): void {
    if (type) {
      http.clearCache(type);
    } else {
      http.clearCache();
    }
  }

  /**
   * 刷新指定类型的数据
   * @param type 数据类型
   * @param params 查询参数
   */
  static async refreshData(
    type: 'filter' | 'customer' | 'overdue' | 'summary',
    params: any
  ): Promise<any> {
    // 先清除对应缓存
    this.clearCache(type);

    // 重新获取数据
    switch (type) {
      case 'filter':
        return this.getFilterData(params);
      case 'customer':
        return this.getCustomerOrders(params);
      case 'overdue':
        return this.getOverdueOrders(params);
      case 'summary':
        return this.getDataSummary(params);
      default:
        throw new Error(`未知的数据类型: ${type}`);
    }
  }

  // ==================== 数据验证和处理 ====================

  /**
   * 验证日期格式
   * @param date 日期字符串
   * @returns 是否有效
   */
  static validateDate(date: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return false;
    }

    const dateObj = new Date(date);
    return dateObj instanceof Date && !isNaN(dateObj.getTime());
  }

  /**
   * 格式化金额显示
   * @param amount 金额
   * @returns 格式化后的金额字符串
   */
  static formatAmount(amount: number): string {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return '0.00';
    }
    return amount.toFixed(2);
  }

  /**
   * 格式化日期显示
   * @param date 日期字符串或Date对象
   * @returns 格式化后的日期字符串
   */
  static formatDate(date: string | Date): string {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return '';
    
    return dateObj.toLocaleDateString('zh-CN');
  }

  /**
   * 处理期数字段数据
   * @param record 订单记录
   * @returns 处理后的记录
   */
  static processInstallmentData(record: any): any {
    const processed = { ...record };
    
    // 处理期数字段
    Object.keys(record).forEach(key => {
      if (key.startsWith('期数') && key !== '期数') {
        const value = record[key];
        if (typeof value === 'string' && value.includes('(')) {
          // 解析期数字段中的状态信息
          const [date, status] = value.split('(');
          processed[`${key}_date`] = date.trim();
          processed[`${key}_status`] = status.replace(')', '').trim();
        }
      }
    });
    
    return processed;
  }

  /**
   * 获取状态颜色
   * @param status 状态
   * @returns 颜色类名
   */
  static getStatusColor(status: string): string {
    const statusColors: Record<string, string> = {
      '按时还款': 'success',
      '逾期未还': 'error',
      '逾期还款': 'warning',
      '提前还款': 'processing',
      '账单日': 'default',
    };
    
    return statusColors[status] || 'default';
  }
}
