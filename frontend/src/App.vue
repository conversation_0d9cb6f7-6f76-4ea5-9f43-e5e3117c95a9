<!-- 根组件 -->
<template>
  <div id="app" :class="appClasses">
    <!-- 全局加载遮罩 -->
    <GlobalLoading v-if="appStore.globalLoading" :text="appStore.loadingText" />
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition name="page" mode="out-in">
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app';
import GlobalLoading from '@/components/common/GlobalLoading.vue';

const appStore = useAppStore();

// 计算应用样式类
const appClasses = computed(() => {
  return [
    `theme-${appStore.effectiveTheme}`,
    {
      'is-mobile': appStore.isMobile,
      'is-desktop': appStore.isDesktop,
      'sidebar-collapsed': appStore.sidebarCollapsed
    }
  ];
});

// 应用初始化
onMounted(() => {
  // 初始化应用
  const cleanup = appStore.initializeApp();
  
  // 组件卸载时清理
  onUnmounted(() => {
    cleanup();
  });
});
</script>

<style lang="scss">
// 全局样式
#app {
  min-height: 100vh;
  font-family: $font-family-sans;
  color: $text-primary;
  background-color: $bg-secondary;
  @include transition(background-color);
}

// 页面切换动画
.page-enter-active,
.page-leave-active {
  transition: opacity $transition-normal, transform $transition-normal;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 主题样式
.theme-light {
  // 浅色主题已在variables.scss中定义
}

.theme-dark {
  --bg-primary: #{$gray-900};
  --bg-secondary: #{$gray-800};
  --bg-tertiary: #{$gray-700};
  
  --text-primary: #{$gray-100};
  --text-secondary: #{$gray-300};
  --text-tertiary: #{$gray-500};
  
  --border-light: #{$gray-700};
  --border-medium: #{$gray-600};
  --border-dark: #{$gray-500};
}

// 移动端适配
.is-mobile {
  .sidebar-collapsed {
    // 移动端特定样式
  }
}

// 桌面端适配
.is-desktop {
  // 桌面端特定样式
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: 4px;
  
  &:hover {
    background: var(--border-dark);
  }
}

// 选择文本样式
::selection {
  background-color: rgba($primary-500, 0.2);
  color: $text-primary;
}

// 焦点样式
:focus-visible {
  outline: 2px solid $primary-500;
  outline-offset: 2px;
}

// 禁用状态样式
:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

// 打印样式
@media print {
  #app {
    background: white !important;
    color: black !important;
  }
  
  .no-print {
    display: none !important;
  }
}
</style>
