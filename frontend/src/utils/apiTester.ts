// API连接测试工具
import { DataService } from '@/api/services/dataService';
import { apiConfig } from '@/api/config';
import { message } from 'ant-design-vue';

export interface ApiTestResult {
  endpoint: string;
  success: boolean;
  responseTime: number;
  error?: string;
  data?: any;
}

export interface ApiTestSuite {
  name: string;
  results: ApiTestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalTime: number;
}

/**
 * API测试器类
 */
export class ApiTester {
  private results: ApiTestResult[] = [];

  /**
   * 测试单个API端点
   */
  async testEndpoint(
    name: string,
    testFn: () => Promise<any>,
    timeout: number = 10000
  ): Promise<ApiTestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🧪 测试 ${name}...`);
      
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), timeout);
      });
      
      const data = await Promise.race([testFn(), timeoutPromise]);
      const responseTime = Date.now() - startTime;
      
      const result: ApiTestResult = {
        endpoint: name,
        success: true,
        responseTime,
        data
      };
      
      console.log(`✅ ${name} 测试通过 (${responseTime}ms)`);
      this.results.push(result);
      return result;
      
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      const result: ApiTestResult = {
        endpoint: name,
        success: false,
        responseTime,
        error: error.message || '未知错误'
      };
      
      console.error(`❌ ${name} 测试失败 (${responseTime}ms):`, error.message);
      this.results.push(result);
      return result;
    }
  }

  /**
   * 运行完整的API测试套件
   */
  async runTestSuite(): Promise<ApiTestSuite> {
    console.log('🚀 开始API连接测试...');
    console.log('📡 API基础地址:', apiConfig.baseURL);
    
    this.results = [];
    const startTime = Date.now();

    // 测试数据筛选API
    await this.testEndpoint('数据筛选API', async () => {
      return DataService.getFilterData({
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      });
    });

    // 测试客户查询API
    await this.testEndpoint('客户查询API', async () => {
      return DataService.getCustomerOrders({
        customer_name: '测试客户',
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      });
    });

    // 测试逾期订单API
    await this.testEndpoint('逾期订单API', async () => {
      return DataService.getOverdueOrders({
        end_date: '2024-01-31'
      });
    });

    // 测试客户汇总API
    await this.testEndpoint('客户汇总API', async () => {
      return DataService.getCustomerSummary({
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      });
    });

    // 测试数据汇总API
    await this.testEndpoint('数据汇总API', async () => {
      return DataService.getDataSummary({
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      });
    });

    // 测试订单汇总API
    await this.testEndpoint('订单汇总API', async () => {
      return DataService.getOrderSummary({
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      });
    });

    const totalTime = Date.now() - startTime;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = this.results.filter(r => !r.success).length;

    const suite: ApiTestSuite = {
      name: 'API连接测试',
      results: this.results,
      totalTests: this.results.length,
      passedTests,
      failedTests,
      totalTime
    };

    // 输出测试结果
    this.printTestResults(suite);
    
    return suite;
  }

  /**
   * 打印测试结果
   */
  private printTestResults(suite: ApiTestSuite): void {
    console.log('\n📊 API测试结果汇总:');
    console.log('='.repeat(50));
    console.log(`总测试数: ${suite.totalTests}`);
    console.log(`通过: ${suite.passedTests} ✅`);
    console.log(`失败: ${suite.failedTests} ❌`);
    console.log(`总耗时: ${suite.totalTime}ms`);
    console.log(`成功率: ${((suite.passedTests / suite.totalTests) * 100).toFixed(1)}%`);
    
    if (suite.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      suite.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.endpoint}: ${r.error}`);
        });
    }
    
    console.log('='.repeat(50));
    
    // 显示用户友好的消息
    if (suite.passedTests === suite.totalTests) {
      message.success(`🎉 所有API测试通过！(${suite.totalTests}/${suite.totalTests})`);
    } else if (suite.passedTests > 0) {
      message.warning(`⚠️ 部分API测试通过 (${suite.passedTests}/${suite.totalTests})`);
    } else {
      message.error(`❌ 所有API测试失败，请检查后端服务`);
    }
  }

  /**
   * 测试API连接性
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔗 测试API连接性...');
      
      // 尝试最简单的API调用
      await this.testEndpoint('连接测试', async () => {
        return DataService.getFilterData({
          start_date: '2024-01-01',
          end_date: '2024-01-01'
        });
      }, 5000);

      const lastResult = this.results[this.results.length - 1];
      return lastResult.success;
      
    } catch (error) {
      console.error('❌ API连接测试失败:', error);
      return false;
    }
  }

  /**
   * 获取API健康状态
   */
  async getApiHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: string;
    responseTime: number;
  }> {
    const startTime = Date.now();
    
    try {
      const isConnected = await this.testConnection();
      const responseTime = Date.now() - startTime;
      
      if (isConnected) {
        if (responseTime < 1000) {
          return {
            status: 'healthy',
            details: 'API响应正常',
            responseTime
          };
        } else {
          return {
            status: 'degraded',
            details: 'API响应较慢',
            responseTime
          };
        }
      } else {
        return {
          status: 'unhealthy',
          details: 'API连接失败',
          responseTime
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        details: error.message || 'API健康检查失败',
        responseTime: Date.now() - startTime
      };
    }
  }
}

// 导出单例实例
export const apiTester = new ApiTester();

// 便捷方法
export const testApiConnection = () => apiTester.testConnection();
export const runApiTests = () => apiTester.runTestSuite();
export const getApiHealth = () => apiTester.getApiHealth();
