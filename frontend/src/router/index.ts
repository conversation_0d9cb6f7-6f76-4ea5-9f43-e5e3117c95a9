// Vue Router配置
import { createRouter, createWebHistory } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useAppStore } from '@/stores/app';
import type { RouteRecordRaw } from 'vue-router';

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard/DashboardView.vue'),
    meta: {
      title: '仪表板',
      icon: 'DashboardOutlined',
      requiresAuth: true,
      permission: 'limited'
    }
  },
  {
    path: '/data-query',
    name: 'DataQuery',
    component: () => import('@/views/DataQuery/DataQueryView.vue'),
    meta: {
      title: '数据查询',
      icon: 'SearchOutlined',
      requiresAuth: true,
      permission: 'limited'
    }
  },
  {
    path: '/customer-query',
    name: 'CustomerQuery',
    component: () => import('@/views/CustomerQuery/CustomerQueryView.vue'),
    meta: {
      title: '客户查询',
      icon: 'UserOutlined',
      requiresAuth: true,
      permission: 'limited'
    }
  },
  {
    path: '/overdue-orders',
    name: 'OverdueOrders',
    component: () => import('@/views/OverdueOrders/OverdueOrdersView.vue'),
    meta: {
      title: '逾期订单',
      icon: 'ExclamationCircleOutlined',
      requiresAuth: true,
      permission: 'standard'
    }
  },
  {
    path: '/customer-summary',
    name: 'CustomerSummary',
    component: () => import('@/views/CustomerSummary/CustomerSummaryView.vue'),
    meta: {
      title: '客户汇总',
      icon: 'BarChartOutlined',
      requiresAuth: true,
      permission: 'standard'
    }
  },
  {
    path: '/data-summary',
    name: 'DataSummary',
    component: () => import('@/views/DataSummary/DataSummaryView.vue'),
    meta: {
      title: '数据汇总',
      icon: 'PieChartOutlined',
      requiresAuth: true,
      permission: 'standard'
    }
  },
  {
    path: '/order-summary',
    name: 'OrderSummary',
    component: () => import('@/views/OrderSummary/OrderSummaryView.vue'),
    meta: {
      title: '订单汇总',
      icon: 'LineChartOutlined',
      requiresAuth: true,
      permission: 'standard'
    }
  },
  {
    path: '/tools',
    name: 'Tools',
    meta: {
      title: '工具',
      icon: 'ToolOutlined',
      requiresAuth: true,
      permission: 'standard'
    },
    children: [
      {
        path: 'qr-generator',
        name: 'QRGenerator',
        component: () => import('@/views/Tools/QRGeneratorView.vue'),
        meta: {
          title: '二维码生成',
          requiresAuth: true,
          permission: 'standard'
        }
      }
    ]
  },
  {
    path: '/api-config',
    name: 'ApiConfig',
    component: () => import('@/views/ApiConfig/ApiConfigView.vue'),
    meta: {
      title: 'API配置',
      icon: 'ApiOutlined',
      requiresAuth: true,
      permission: 'admin',
      hidden: import.meta.env.PROD // 生产环境隐藏
    }
  },
  {
    path: '/admin',
    name: 'Admin',
    meta: {
      title: '系统管理',
      icon: 'SettingOutlined',
      requiresAuth: true,
      permission: 'full'
    },
    children: [
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/Admin/UserManagementView.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          permission: 'full'
        }
      },
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/Admin/SystemSettingsView.vue'),
        meta: {
          title: '系统设置',
          requiresAuth: true,
          permission: 'full'
        }
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile/ProfileView.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/Error/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();
  const appStore = useAppStore();
  
  // 设置当前路由
  appStore.setCurrentRoute(to.name as string);
  
  // 设置页面标题
  const title = to.meta?.title as string;
  if (title) {
    document.title = `${title} - ${appStore.appConfig.title}`;
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    // 检查用户是否已登录
    if (!userStore.isAuthenticated) {
      // 尝试从本地存储恢复认证状态
      const isValid = await userStore.initializeAuth();
      
      if (!isValid) {
        // 认证失败，跳转到登录页
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        });
        return;
      }
    }

    // 检查权限
    const requiredPermission = to.meta?.permission as 'limited' | 'standard' | 'full';
    if (requiredPermission && !userStore.hasPermission(requiredPermission)) {
      // 权限不足，跳转到403页面或首页
      next({ name: 'Dashboard' });
      return;
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.name === 'Login' && userStore.isAuthenticated) {
    next({ name: 'Dashboard' });
    return;
  }

  next();
});

router.afterEach((to, from) => {
  // 路由切换后的处理
  const appStore = useAppStore();
  
  // 隐藏全局加载状态
  appStore.hideGlobalLoading();
  
  // 可以在这里添加页面访问统计等逻辑
});

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error);
  
  // 可以在这里添加错误上报逻辑
});

export default router;

// 导出路由配置供其他地方使用
export { routes };

// 路由工具函数
export const getMenuRoutes = () => {
  const userStore = useUserStore();
  
  const filterRoutes = (routes: RouteRecordRaw[]): RouteRecordRaw[] => {
    return routes.filter(route => {
      // 过滤隐藏的菜单项
      if (route.meta?.hideInMenu) {
        return false;
      }
      
      // 检查权限
      const requiredPermission = route.meta?.permission as 'limited' | 'standard' | 'full';
      if (requiredPermission && !userStore.hasPermission(requiredPermission)) {
        return false;
      }
      
      // 递归过滤子路由
      if (route.children) {
        route.children = filterRoutes(route.children);
      }
      
      return true;
    });
  };
  
  return filterRoutes(routes);
};

// 面包屑导航工具
export const getBreadcrumbs = (route: any) => {
  const breadcrumbs: Array<{ name: string; title: string; path?: string }> = [];
  
  const addBreadcrumb = (routeRecord: any, path: string) => {
    if (routeRecord.meta?.title) {
      breadcrumbs.unshift({
        name: routeRecord.name,
        title: routeRecord.meta.title,
        path: path
      });
    }
  };
  
  // 构建面包屑
  let currentRoute = route;
  let currentPath = route.path;
  
  while (currentRoute) {
    addBreadcrumb(currentRoute, currentPath);
    
    // 查找父路由
    const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/'));
    if (parentPath) {
      currentRoute = router.resolve(parentPath).matched[0];
      currentPath = parentPath;
    } else {
      break;
    }
  }
  
  return breadcrumbs;
};
