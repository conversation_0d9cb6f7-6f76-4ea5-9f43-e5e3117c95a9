// 应用入口文件
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import pinia from './stores';

// 导入样式
import './styles/main.scss';

// 导入Ant Design Vue样式
import 'ant-design-vue/dist/reset.css';

// 创建应用实例
const app = createApp(App);

// 注册插件
app.use(pinia);
app.use(router);

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  // 忽略 ant-design-vue 的 isContextmenuToShow 错误
  if (err instanceof Error && err.message && err.message.includes('isContextmenuToShow is not a function')) {
    // 完全忽略这个错误，不输出任何信息
    return;
  }
  
  console.error('Global error:', err);
  if (instance) {
    console.error('Component info:', instance.$?.type?.name || 'Unknown');
    console.error('Component props:', instance.$?.props);
  }
  console.error('Error info:', info);
};

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Global warning:', msg, trace);
};

// 挂载应用
app.mount('#app');

// 开发环境下的调试工具
if (import.meta.env.DEV) {
  // 将应用实例挂载到window对象，方便调试
  (window as any).__app__ = app;
  
  // 启用Vue DevTools
  app.config.performance = true;
}
