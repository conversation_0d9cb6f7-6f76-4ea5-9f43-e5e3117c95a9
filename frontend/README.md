# 太享查询系统 - 前端重构版

基于 Vue 3 + TypeScript + Vite 的现代化金融数据查询展示系统。

## 🚀 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建工具**: Vite 5.0+
- **状态管理**: Pinia 2.0+
- **路由**: Vue Router 4.0+
- **UI组件**: Ant Design Vue 4.0+
- **图表库**: ECharts 5.0+
- **样式**: SCSS + 设计系统
- **工具库**: VueUse, Axios, Day.js

## 📦 项目结构

```
frontend/
├── src/
│   ├── api/                    # API接口层
│   │   ├── types/             # TypeScript类型定义
│   │   ├── services/          # 业务API服务
│   │   └── client.ts          # HTTP客户端配置
│   ├── components/            # 可复用组件
│   │   ├── common/           # 通用组件
│   │   ├── business/         # 业务组件
│   │   └── charts/           # 图表组件
│   ├── views/                # 页面组件
│   │   ├── Dashboard/        # 仪表板
│   │   ├── DataQuery/        # 数据查询
│   │   ├── CustomerManage/   # 客户管理
│   │   └── Reports/          # 报表分析
│   ├── stores/               # Pinia状态管理
│   ├── router/               # 路由配置
│   ├── styles/               # 样式系统
│   │   ├── variables.scss    # SCSS变量
│   │   ├── mixins.scss       # 混入
│   │   └── themes/           # 主题配置
│   ├── utils/                # 工具函数
│   └── composables/          # 组合式函数
├── public/                   # 静态资源
├── tests/                    # 测试文件
└── docs/                     # 项目文档
```

## 🛠️ 开发环境

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 🧪 测试

### 单元测试

```bash
npm run test:unit
```

### E2E测试

```bash
npm run test:e2e
```

## 📝 代码规范

### 代码检查

```bash
npm run lint
```

### 代码格式化

```bash
npm run format
```

### 类型检查

```bash
npm run type-check
```

## 🎨 设计系统

项目采用现代化的设计系统，包括：

- **色彩系统**: 专业的蓝色主题配色
- **字体系统**: Inter字体 + 中文字体回退
- **间距系统**: 基于8px网格的间距规范
- **组件库**: 统一的UI组件设计
- **响应式**: 移动端优先的响应式设计

## 🔧 配置说明

### 环境变量

```bash
# 开发环境 (.env.development)
VITE_APP_ENV=development
VITE_API_BASE_URL=http://localhost:5000
VITE_ENABLE_DEVTOOLS=true

# 生产环境 (.env.production)
VITE_APP_ENV=production
VITE_API_BASE_URL=https://your-production-domain.com
VITE_ENABLE_DEVTOOLS=false
```

### API配置

API客户端支持：
- 自动错误处理
- 请求/响应拦截
- 缓存机制
- 重试机制
- TypeScript类型安全

### 状态管理

使用Pinia进行状态管理：
- 用户状态 (useUserStore)
- 数据状态 (useDataStore)
- 应用状态 (useAppStore)

## 📱 功能特性

### 核心功能

- ✅ 用户认证和权限管理
- ✅ 数据查询和筛选
- ✅ 客户信息管理
- ✅ 逾期订单监控
- ✅ 数据汇总和分析
- ✅ 图表可视化
- ✅ 数据导出 (Excel/CSV)
- ✅ 响应式设计

### 技术特性

- ✅ TypeScript类型安全
- ✅ 组件化开发
- ✅ 状态管理
- ✅ 路由守卫
- ✅ 错误边界
- ✅ 性能优化
- ✅ 代码分割
- ✅ 懒加载

## 🔄 与后端集成

### API接口

项目与现有Flask后端完全兼容：

```typescript
// 数据查询
GET /api/filter_data?date=2024-01-01

// 客户查询
GET /api/filter_orders_by_customer_name?customer_name=张三

// 逾期订单
GET /api/filter_overdue_orders?page=1&limit=20

// 数据导出
POST /api/export
```

### 认证机制

支持现有的认证系统：
- Session认证
- Token认证
- 权限级别控制

## 🚀 部署

### 构建优化

- 代码分割和懒加载
- 资源压缩和优化
- CDN资源配置
- 缓存策略

### 部署配置

```bash
# 构建生产版本
npm run build

# 部署到静态服务器
# 将 dist/ 目录内容部署到Web服务器
```

## 📚 开发指南

### 组件开发

```vue
<template>
  <div class="my-component">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 使用Composition API
interface Props {
  title: string;
  data: any[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  change: [value: string];
}>();
</script>

<style lang="scss" scoped>
.my-component {
  // 使用设计系统变量
  padding: $space-4;
  background: $bg-primary;
  @include border-radius();
}
</style>
```

### API调用

```typescript
import { DataService } from '@/api/services/dataService';

// 在组合式函数中使用
const { data, loading, error } = await DataService.getFilterData({
  date: '2024-01-01'
});
```

### 状态管理

```typescript
import { useDataStore } from '@/stores/data';

const dataStore = useDataStore();

// 获取数据
await dataStore.fetchFilterData({ date: '2024-01-01' });

// 访问状态
const hasData = dataStore.hasFilterData;
const isLoading = dataStore.loading.filter;
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请联系开发团队。
